/**
 * @file
 * Global utilities.
 *
 */
(function ($, Drupal) {
  "use strict";

  Drupal.behaviors.equalHeightArticleTitles = {
    attach: function (context, settings) {
      Drupal.behaviors.equalHeightArticleTitles.calculateEqual();
    },
    npxOnResize: function () {
      Drupal.behaviors.equalHeightArticleTitles.calculateEqual();
    },
    calculateEqual: function () {
      if ($(window).width() >= 992) {
        if ($(".g27").length) {
          var $titles = $(".g27 .field--name-node-title");
          var maxHeight = 0;
          $titles.each(function () {
            var height = $(this).height();
            if (height > maxHeight) {
              maxHeight = height;
            }
          });
          $titles.height(maxHeight);
          var $summaries = $(".g27 .g27-inner");
          maxHeight = 0;
          $summaries.each(function () {
            var height = $(this).height();
            if (height > maxHeight) {
              maxHeight = height;
            }
          });
          $summaries.height(maxHeight);
        }
      } else {
        $(".g27 .field--name-node-title").height("auto");
        $(".g27 .g27-inner").height("auto");
      }
    },
  };

  Drupal.behaviors.equalHeightBlogArticleTitles = {
    attach: function (context, settings) {
      Drupal.behaviors.equalHeightBlogArticleTitles.calculateEqual();
    },
    npxOnResize: function () {
      Drupal.behaviors.equalHeightBlogArticleTitles.calculateEqual();
    },
    calculateEqual: function () {
      if ($(window).width() >= 768) {
        if (
          $("body.page-node-4299").length ||
          $("body.node--type-blog-cat-page").length
        ) {
          var $titles = $(".view-npx-blog-list .field--name-node-title");
          var maxHeight = 0;
          $titles.each(function () {
            var height = $(this).height();
            if (height > maxHeight) {
              maxHeight = height;
            }
          });
          $titles.height(maxHeight);
          var $summaries = $(".view-npx-blog-list .field--name-field-trailer");
          maxHeight = 0;
          $summaries.each(function () {
            var height = $(this).height();
            if (height > maxHeight) {
              maxHeight = height;
            }
          });
          $summaries.height(maxHeight);
        }
      } else {
        $(".view-npx-blog-list .field--name-node-title").height("auto");
        $(".view-npx-blog-list .field--name-field-trailer").height("auto");
      }
    },
  };

  Drupal.behaviors.readMore = {
    attach: function () {
      const elements = once("readmoreclick", ".readmore-js-toggle", document);
      elements.forEach(function (element) {
        var $element = $(element);
        $element.click(function (e) {
          var $parent = $(this).parent();
          $parent.toggleClass("readmore-open");
          e.preventDefault();
        });
      });
    },
  };
  Drupal.behaviors.superfishFix = {
    attach: function (context, settings) {
      $("ul#superfish-grow3menu").css("visibility", "visible");
      $("ul#superfish-grow3menu-accordion").css("visibility", "visible");
    },
  };

  Drupal.behaviors.topTitleImg = {
    attach: function (context, settings) {
      Drupal.behaviors.topTitleImg.topTitleImage();
    },
    npxOnResize: function () {
      Drupal.behaviors.topTitleImg.topTitleImage();
    },
    topTitleImage: function () {
      if ($("body.node--type-npxtraining").length) {
        $("#szkolenie-grupa-1.full-width-image .obraz").each(function () {
          if ($(window).width() < 992) {
            if ($("h1", this).outerHeight() + 120 > $("img", this).height()) {
              $("img", this).height($("h1", this).outerHeight() + 120);
            }
          } else {
            $("img", this).height("auto");
          }
        });
      }
    },
  };

  Drupal.behaviors.faqReadQuestion = {
    attach: function (context, settings) {
      const questions = once('faq-question-click', '.field--name-field-question', context);
      questions.forEach(function (element) {
        $(element).click(function (e) {
          $(this).toggleClass("active");
          $(".field--name-field-answer", $(this).parent()).toggleClass("hidden");
        });
      });
    },
  };
  /*
    Drupal.behaviors.cookiesActions = {
      attach: function(context, settings) {
        $( window ).on( "load", function() {
          if (!$('.cookiesjsr-banner.active').length) {
            $(once('add-active-class', '#cookies-overlay')).removeClass('active');
          }
        });
        setTimeout(function() {
          $(once('cookiesetclick', '.cookiesjsr-btn.cookiesjsr-settings')).click(function(e) {
            e.preventDefault();
            setTimeout(function() {
              $(once('cookieallowallclick','.cookiesjsr-btn.allowAll')).click(function() {
                $('#cookies-overlay').removeClass("active");
              });
              $(once('cookiedenyallclick', '.cookiesjsr-btn.denyAll')).click(function() {
                $('#cookies-overlay').removeClass("active");
              });
              $(once('cookiesaveclick', '.cookiesjsr-btn.save')).click(function() {
                $('#cookies-overlay').removeClass("active");
              });
            },1000);
          });
          $(once('cookieallowallclick2', '.cookiesjsr-btn.allowAll')).click(function() {
            $('#cookies-overlay').removeClass("active");
          });
          $(once('cookiedenyallclick2', '.cookiesjsr-btn.denyAll')).click(function() {
            $('#cookies-overlay').removeClass("active");
          });
        },2000);
        setTimeout(function() {
          if (!$('.cookiesjsr-banner.active').length) {
            $(once('remove-active-class2', '#cookies-overlay')).removeClass('active');
          }
        }, 4000);
        setTimeout(function() {
          $(once('cookieallowallclick4', '.cookiesjsr-btn.allowAll')).click(function() {
            $('#cookies-overlay').removeClass("active");
          });
          $(once('cookiedenyallclick4', '.cookiesjsr-btn.denyAll')).click(function() {
            $('#cookies-overlay').removeClass("active");
          });
        },1000);
        $(once('cookieallowallclick3', '.cookiesjsr-btn.allowAll')).click(function() {
          $('#cookies-overlay').removeClass("active");
        });
        $(once('cookieallowallclick3', '.cookiesjsr-btn.denyAll')).click(function() {
          $('#cookies-overlay').removeClass("active");
        });
      }
    };*/

  Drupal.behaviors.superfishmenuclick = {
    attach: function (context, settings) {
      $(".block-superfishgrow3menu a.sf-depth-1.menuparent").click(function (
        e
      ) {
        if (e.target === this) {
          e.preventDefault();
        }
      });
    },
  };
  Drupal.behaviors.justifyGallery = {
    attach: function (context, settings) {
      if (
        $("body.node--type-npxtraining").length ||
        $("body.node--type-landing-page").length
      ) {
        $("#traininggallery").justifiedGallery({
          rowHeight: 200,
          margins: 8,
          maxRowHeight: 250,
          lastRow: "justify",
          captions: false,
          target: "_blank",
        });
      }
    },
  };

  Drupal.behaviors.bootstrap4growWrapTablesInOverflow = {
    attach: function (context, settings) {
      $(once('bootstrap4grow-wrap-tables-in-overflow', '.text-formatted table', context)).each(function () {
        $(this).wrap('<div class="overflow-auto"></div>');
      });
    }
  };

  Drupal.behaviors.bootstrap4growCookiesConsent = {
    attach: function (context, settings) {
      var cookiesjsrCookie = Cookies.get("cookiesjsr");

      setTimeout(function () {
        if (cookiesjsrCookie) {
          cookiesjsrCookie = JSON.parse(cookiesjsrCookie);
          var analyticsValue = cookiesjsrCookie.gtag;
          if (analyticsValue === true) {
            consentGrantedAdStorage();
          }
        }

        const allowAllElements = once(
          "cookieallowallclick",
          ".cookiesjsr-btn.allowAll",
          document
        );
        allowAllElements.forEach(function (element) {
          $(element).click(function () {
            consentGrantedAdStorage();
          });
        });

        const denyAllElements = once(
          "cookiedenyallclick",
          ".cookiesjsr-btn.denyAll",
          document
        );
        denyAllElements.forEach(function (element) {
          $(element).click(function () {
            consentDeniedAdStorage();
          });
        });

        function attachConsentHandlers() {
          setTimeout(function () {
            $(".allowAll.invert").click(function () {
              consentGrantedAdStorage();
            });
            $(".denyAll").click(function () {
              consentDeniedAdStorage();
            });
            $(".cookiesjsr-btn.save").click(function () {
              var cookiesjsrCookie = Cookies.get("cookiesjsr");
              if (cookiesjsrCookie) {
                cookiesjsrCookie = JSON.parse(cookiesjsrCookie);
                var analyticsValue = cookiesjsrCookie.analytics;
                var adStorageValue = cookiesjsrCookie.gtag;
                if (analyticsValue === true && adStorageValue === true) {
                  consentGrantedAdStorage();
                } else if (analyticsValue === true) {
                  gtag("consent", "update", {
                    ad_storage: "granted",
                    ad_user_data: "granted",
                    ad_personalization: "granted",
                    analytics_storage: "granted",
                    functionality_storage: "denied",
                    personalization_storage: "denied",
                    security_storage: "granted",
                  });

                  pushAnalyticsConsentGranted();
                } else if (adStorageValue === true) {
                  consentDeniedAdStorage();
                } else {
                  consentDeniedAdStorage();
                }
              }
            });
          }, 300);
        }

        $(".cookies-cookie-settings-toggle").click(attachConsentHandlers);
        $(".cookiesjsr-settings").click(attachConsentHandlers);
      }, 750);
    },
  };

  function consentGrantedAdStorage() {
    gtag("consent", "update", {
      ad_storage: "granted",
      ad_user_data: "granted",
      ad_personalization: "granted",
      analytics_storage: "granted",
      functionality_storage: "granted",
      personalization_storage: "granted",
      security_storage: "granted",
    });

    pushAnalyticsConsentGranted();
  }

  function consentDeniedAdStorage() {
    gtag("consent", "update", {
      ad_storage: "denied",
      ad_user_data: "denied",
      ad_personalization: "denied",
      analytics_storage: "denied",
      functionality_storage: "denied",
      personalization_storage: "denied",
      security_storage: "granted",
    });
  }

  function pushAnalyticsConsentGranted() {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ event: "analytics_consent_granted" });
  }

  Drupal.behaviors.cookiesBannerLinks = {
    attach: function (context, settings) {
      function addLinks() {
        $(once('cookies-banner-links', '.cookiesjsr-banner--text', context)).each(function() {
          var $text = $(this);
          var textContent = $text.html();
          
          if (textContent && textContent.indexOf('Polityce cookies') !== -1 && textContent.indexOf('<a href') === -1) {
            textContent = textContent.replace(
              'Polityce cookies',
              '<a href="https://4grow.pl/polityka-cookies" target="_blank" rel="noopener noreferrer">Polityce cookies</a>'
            );
            
            textContent = textContent.replace(
              'Polityce prywatności',
              '<a href="https://4grow.pl/polityka-prywatnosci" target="_blank" rel="noopener noreferrer">Polityce prywatności</a>'
            );
            
            $text.html(textContent);
          }
        });
      }
      
      addLinks();
      
      setTimeout(addLinks, 500);
      setTimeout(addLinks, 1000);
      setTimeout(addLinks, 2000);
    }
  };

  Drupal.behaviors.cookiesCancelButton = {
    attach: function (context, settings) {
      // Użyj MutationObserver żeby obserwować pojawianie się elementów cookies
      if (typeof MutationObserver !== 'undefined') {
        var observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              // Sprawdź czy pojawił się kontener actions
              var $actions = $('.cookiesjsr-layer--actions');
              if ($actions.length > 0) {
                var $denyAllButton = $actions.find('.cookiesjsr-btn.denyAll');
                if ($denyAllButton.length > 0 && !$denyAllButton.hasClass('cancel-modified')) {
                  console.log('Found denyAll button, modifying to Anuluj');
                  $denyAllButton.addClass('cancel-modified');
                  $denyAllButton.text('Anuluj');
                  $denyAllButton.off('click');
                  $denyAllButton.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    var $closeButton = $('.cookiesjsr-layer--close');
                    if ($closeButton.length > 0) {
                      $closeButton.click();
                    }
                  });
                }
              }
            }
          });
        });
        
        // Obserwuj zmiany w całym dokumencie
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }
  };


})(jQuery, Drupal);
