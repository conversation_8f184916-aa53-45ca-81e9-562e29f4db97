@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.node--type-npxtraining h2 {
  font-weight: 700;
  font-size: 2rem;
  line-height: 1.5;
  padding: 0;
  margin: 5rem 0 2.8125rem;
  position: relative;
  display: block;
}
.node--type-npxtraining .ajax-progress-throbber {
  background: transparent;
}
.node--type-npxtraining .ajax-progress-throbber.ajax-progress {
  display: block;
  position: absolute;
  left: 0;
  padding-top: 0.3125rem;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  z-index: 100;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

@media (max-width: 74.99875rem) {
  .ds-2col-fluid > .group-left {
    width: 100%;
  }
}
.ds-2col-fluid > .group-right {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  min-height: 100%;
}
.ds-2col-fluid > .group-right .obraz {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: " ";
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  background-color: transparent !important;
  background-position-y: 53px !important;
  z-index: 10;
}

.field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -1.25rem;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
}
@media (min-width: 62rem) {
  .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
    left: 0;
    margin-left: 0;
  }
}

.narrow {
  max-width: 1200px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}
.narrow h2 {
  margin-left: 0;
  margin-right: 0;
}
@media (min-width: 81.25rem) {
  .narrow h2 {
    margin-left: -3vw;
    margin-right: -3vw;
  }
}
@media (min-width: 100rem) {
  .narrow h2 {
    margin-left: -6.625rem;
    margin-right: -6.625rem;
  }
}

.sekcja {
  max-width: 1415px;
  padding: 1.25rem;
  margin: 0 auto;
  overflow: hidden;
}
@media (min-width: 36rem) {
  .sekcja {
    padding: 1.875rem;
  }
}
.sekcja.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

[id^=szkolenie-grupa-]::before {
  display: block;
  content: " ";
  margin-top: -3.125rem;
  height: 50px;
  visibility: hidden;
}

#szkolenie-grupa-2 .slick__arrow {
  bottom: 0;
  top: unset;
}
#szkolenie-grupa-2 .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
#szkolenie-grupa-2 .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
#szkolenie-grupa-2 .slick-prev:hover, #szkolenie-grupa-2 .slick-prev:focus, #szkolenie-grupa-2 .slick-next:hover, #szkolenie-grupa-2 .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
#szkolenie-grupa-2 .slick {
  max-width: 600px;
  margin: 0 auto;
}
#szkolenie-grupa-2 .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-2 .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

.field--name-field-nasza-jakosc-twoj-komfort .y-box-outer {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  z-index: 10;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
  margin: 0 5%;
  padding: 1.875rem;
  background-color: #fff;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
  border-radius: 5px;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.5;
  font-weight: bold;
  text-align: center;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1%;
    padding: 1.875rem 2.5rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1.875rem;
    padding: 1.875rem 4.0625rem 2.5rem;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-1 {
  padding: 0.125rem 0.5rem;
  background: #B3002B;
  color: #fff;
  font-size: 0.625rem;
  line-height: 1rem;
  display: inline-block;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 {
  display: block;
  margin: 0.625rem 0 0 0;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 strong {
  font-style: normal;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-image {
  height: 60vw;
  background: transparent url("../images/4grow-sala-mobile.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
  margin-top: -3vw;
  position: relative;
  z-index: 1;
  margin-bottom: 1rem;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -6vw;
    -webkit-background-size: cover;
            background-size: cover;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -10.65rem;
    height: 33vw;
    background: transparent url("../images/4grow-sala-szkoleniowa-desktop.jpg") no-repeat center center;
    -webkit-background-size: cover;
            background-size: cover;
  }
}

.page-node-5593 .field--name-field-nasza-jakosc-twoj-komfort .y-image {
  background: transparent url("../images/sala_antystres_bg.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
}

@media (min-width: 100rem) {
  .container.field-name-field-tytul-sekcji-g4 {
    max-width: 1415px;
  }
}
.field--name-field-npxtraining-paragraf-trene > .field__item {
  margin-bottom: 2rem;
}

.paragraph--type-trener-do-szkolenia-par .wrapper-1 {
  position: relative;
  margin: 0 0 1.25rem;
  text-align: center;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
  z-index: 2;
  top: 6%;
  position: relative;
  right: 0;
  width: 100%;
  height: auto;
  display: block;
  margin: 0.9375rem 0 0 0;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
    position: absolute;
    right: 5%;
    width: 50%;
    height: 90%;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-align-content: center;
        -ms-flex-line-pack: center;
            align-content: center;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
  }
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 h3 {
  line-height: 1.2;
  margin: 0 0 0.75rem;
  padding: 0;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col img {
  max-width: 100%;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
    float: right;
    padding: 0 0 0 3%;
    width: 50%;
  }
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
    float: left;
    width: 50%;
    padding: 0 3% 0 0;
  }
}

#szkolenie-grupa-5 .slick-dots {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: -1.5rem;
}
#szkolenie-grupa-5 .slick--less .slick-track {
  text-align: left;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-5 .slick--less .slick-track {
    text-align: center;
  }
}
#szkolenie-grupa-5 .field--name-field-zajawka {
  padding: 0 0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka p {
  margin: 0 0 0.5rem;
  font-size: 80%;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child img {
  width: 100%;
}
#szkolenie-grupa-5 .slick__arrow {
  bottom: 15px;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#szkolenie-grupa-5 .draggable {
  max-height: 410px;
}
#szkolenie-grupa-5 .view-mode-bootstrap_carousel.ds-1col > .inner {
  border: 1px solid #d0d8db;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
#szkolenie-grupa-5 .field--name-field-link-do-profilu-trenera {
  display: none;
}
#szkolenie-grupa-5 .field--name-field-npxtrainer-position {
  text-align: center;
  margin-bottom: 1.5rem;
}
#szkolenie-grupa-5 h3 {
  font-size: 140%;
  line-height: 1.2;
  margin: 1rem 0 0.75rem;
  text-align: center;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera {
  max-height: none;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera span {
  font-size: 1rem;
  color: #343a40;
  line-height: 1.5rem;
  font-family: Muli, sans-serif;
  text-align: left;
  word-break: unset;
  display: inline;
}

#szkolenie-grupa-6 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .item-list {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 5.625rem auto;
}

.field--name-dynamic-block-fieldnode-ds-training-program-block .field-label-above:first-of-type:not(:last-of-type):after {
  display: none;
}

#szkolenie-grupa-8 #npx-bottom-wrapper input {
  width: 100%;
  margin-left: 0.125rem;
  font-size: 0.875rem;
  padding-left: 0.5rem;
}
#szkolenie-grupa-8 #npx-bottom-wrapper textarea {
  width: 100%;
  height: 200px;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-8 #npx-bottom-wrapper textarea {
    height: auto;
  }
}
#szkolenie-grupa-8 #npx-bottom-wrapper button {
  text-transform: uppercase;
}
#szkolenie-grupa-8 #npx-training-form .edit-npx-training-date > .fieldset-wrapper > div > .form-item {
  margin-bottom: 1.25rem;
}
#szkolenie-grupa-8 .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  width: auto;
  margin: 0 auto;
  display: inline-block;
}
#szkolenie-grupa-8 .npx-spoiler-toggle.show-icon:before {
  content: "ROZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-toggle.hide-icon:before {
  content: "ZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-content {
  font-size: inherit;
}
#szkolenie-grupa-8 .npx-spoiler-content a.npx-autolink {
  color: inherit;
  text-decoration: none;
}
#szkolenie-grupa-8 .n-spoiler-toggle-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  display: inline-block;
}

#npx-online-training-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
#npx-online-training-wrapper .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
  margin: 0.5rem 0;
  padding-left: 0;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
    margin: auto 0.625rem;
  }
}

.npx-fv-paper-wrapper .radio {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.npx-fv-paper-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios .js-form-item-npx-fv-paper {
  margin: auto 0.625rem;
}

.npx-fv-paper-info {
  font-size: 1rem !important;
}

.npx-location-info-wrapper.npx-noborder {
  padding-bottom: 1.25rem;
}

#npx-training-form .npx-form-additional-description {
  font-size: 0.875rem;
  top: -1.25rem;
  position: relative;
}
#npx-training-form [id^=edit-fields-wrapper] {
  display: block;
  margin-right: -0.3125rem;
  margin-left: -0.3125rem;
  overflow: hidden;
}
@media (min-width: 48rem) {
  #npx-training-form [id^=edit-fields-wrapper] {
    margin-top: 0.5rem;
  }
}
#npx-training-form [id^=edit-fields-wrapper] .js-form-item {
  max-width: 99%;
  margin-top: 0.5rem;
}
@media (min-width: 62rem) {
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item {
    float: left;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:not(:last-of-type) {
    margin-right: 0.125rem;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(1) {
    width: 9%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(2), #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(5) {
    width: 10%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(3) {
    width: 20%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(4) {
    width: 23%;
  }
}
#npx-training-form .field-suffix {
  display: none;
}
#npx-training-form .npx-form-error {
  display: none;
}
#npx-training-form #npx-bottom-wrapper {
  display: none;
  background-color: #f7f9f8;
}
#npx-training-form #npx-bottom-wrapper.n-webinar-mode .npx-online-training-header, #npx-training-form #npx-bottom-wrapper.n-webinar-mode .form-item-npx-online-training {
  display: none;
}
@media (min-width: 62rem) {
  #npx-training-form .npx-form-additional-description, #npx-training-form .form-item-npx-fv-comment {
    max-width: calc(72% + 17px);
  }
}
#npx-training-form legend {
  font-size: 1rem;
  margin-bottom: 0;
}
#npx-training-form h4 {
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
}
#npx-training-form .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-training-form .npx-fv-paper-wrapper .field-prefix {
  margin-right: 0.5rem;
}
@media (min-width: 48rem) {
  #npx-training-form .npx-fv-paper-wrapper .field-prefix {
    margin-top: -0.5rem;
  }
}
#npx-training-form.with-bottom-wrapper #npx-bottom-wrapper {
  display: block;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper {
  display: none !important;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper-online {
  display: none !important;
}
#npx-training-form div.form-item-npx-training {
  display: none;
}
@media (min-width: 48rem) {
  #npx-training-form #npx-top-wrapper {
    padding-bottom: 0.625rem;
  }
}
#npx-training-form.n-hide-hotel .form-item-npx-hotel-info {
  display: none;
}
#npx-training-form div.npx-border-green {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-green-inner {
  border: #c8dc32 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form div.npx-border-gray {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-gray-inner {
  border: #d4d8db 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form input#edit-npx-accept-4:invalid + label {
  color: #fc5353;
}
#npx-training-form a#npx-expand-bottom-wrapper-online {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  text-align: center;
  margin-bottom: 1rem;
}
@media (min-width: 48rem) {
  #npx-training-form a#npx-expand-bottom-wrapper-online {
    margin-right: 1rem;
    width: auto;
    text-align: left;
  }
}

.npx-float::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}

.npx-training-type .field-prefix {
  margin-right: 1rem;
}

#szkolenie-grupa-14 {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-14 {
    max-width: 1415px;
    width: 100%;
    margin: 0 auto;
    left: 0;
    right: 0;
  }
}

.field--name-field-question {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-left: 2rem;
}
.field--name-field-question.active:before {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 28px;
}
.field--name-field-question:before {
  position: absolute;
  top: 22px;
  cursor: pointer;
  content: "";
  display: inline-block;
  width: 11px;
  height: 11px;
  border-right: 2px solid #343a40;
  border-top: 2px solid #343a40;
  -webkit-transform: rotate(135deg);
  -o-transform: rotate(135deg);
     transform: rotate(135deg);
  margin-right: 0.5em;
  margin-left: 1em;
  max-width: 12px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-item-align: center;
  -webkit-align-self: center;
          align-self: center;
  left: -12px;
  -webkit-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}
.field--name-field-question h3, .field--name-field-question p, .field--name-field-question h4 {
  margin: 0.625rem 0;
}

.field--name-field-answer h3, .field--name-field-answer h4 {
  margin: 0 0 0.625rem 0;
}

.g18.sekcja.kontakt-email {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}
.g18.sekcja.kontakt-email .ajax-progress-throbber {
  background: transparent;
}

.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}

@media (min-width: 62rem) {
  .field--name-field-loga-firm {
    max-height: 106px;
    overflow: hidden;
  }
}
.field--name-field-loga-firm .field__item.col-auto {
  -webkit-transform: scale(0.7);
       -o-transform: scale(0.7);
          transform: scale(0.7);
}
@media (min-width: 36rem) {
  .field--name-field-loga-firm .field__item.col-auto {
    -webkit-transform: none;
         -o-transform: none;
            transform: none;
  }
}
.field--name-field-loga-firm img {
  opacity: 0.5;
  height: 40px;
  width: auto;
  margin: 0.1875rem auto;
}

.field--name-field-logo-1-ref {
  padding-bottom: 0.25rem;
}

.field--name-field-logo-2-ref, .field--name-field-logo-1-ref {
  min-height: 60px;
}

@media (min-width: 75rem) {
  .methodology-items {
    margin-top: 1.5rem;
    margin-left: 3rem;
  }
}
.methodology-item:before {
  display: block;
  content: "";
  width: 50%;
  height: 1px;
  background: #d1d1d1;
  position: absolute;
  left: -2rem;
  z-index: -1;
  top: 50px;
}
@media (min-width: 75rem) {
  .methodology-item:before {
    left: -7rem;
    top: 40%;
    width: 9rem;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-item-title {
    display: block;
    width: 100%;
  }
}
@media (min-width: 75rem) {
  .methodology-item-title {
    border-right: #d1d1d1 solid 1px;
    margin-right: 0.9375rem;
    min-width: 110px;
    max-width: 110px;
    margin-bottom: 0;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-image {
    max-width: 420px;
  }
  .methodology-image:before {
    position: absolute;
    content: "";
    width: 110%;
    padding-top: 100%;
    max-width: 462px;
    border-radius: 50%;
    border-bottom: #d1d1d1 solid 2px;
    top: 28px;
    left: -5%;
  }
  .methodology-image img {
    border-radius: 50%;
    -webkit-box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    width: 100%;
    height: auto;
  }
}
@media (min-width: 75rem) {
  .methodology-image {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-icon {
    width: 100px;
    margin: 0 auto;
  }
}
@media (min-width: 75rem) {
  .methodology-icon {
    max-width: 70px;
    max-height: 70px;
    min-width: 70px;
    min-height: 70px;
    margin-right: 1rem;
  }
}
.methodology-icon img {
  border-radius: 50%;
  -webkit-box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
  box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
}

.field-oni-juz-byli_item_even {
  margin-top: 1.25rem;
  margin-left: auto;
  margin-right: 0;
}

.field--name-field-oni-juz-byli_item {
  max-width: 660px;
  clear: both;
}
.field--name-field-oni-juz-byli_item .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-oni-juz-byli_item .group-middle {
    width: calc(100% - 110px);
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.field--name-field-video-embed {
  text-align: center;
}
.field--name-field-video-embed .video-embed-field-lazy-play {
  border: none;
}

.field--name-field-opinie-wideo-ref-field__item {
  max-width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 48rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
  }
}
.field--name-field-opinie-wideo-ref-field__item .video-embed-field-launch-modal img {
  max-width: 100%;
  cursor: pointer;
}

.node--view-mode-teaser.node--type-wideo {
  border: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.sekcja.wyznaczamy-standardy .npx-form-button-wrapper, body.node--type-page .npx-form-button-wrapper {
  text-align: center;
}
.sekcja.wyznaczamy-standardy a.npx-form-button.npx-autolink, .sekcja.wyznaczamy-standardy a.npx-form-button-inline.npx-autolink, body.node--type-page a.npx-form-button.npx-autolink, body.node--type-page a.npx-form-button-inline.npx-autolink {
  margin-top: 0;
}
.sekcja.wyznaczamy-standardy .field--name-field-extra-tekst-g3 p.npx-hidden-text, body.node--type-page .field--name-field-extra-tekst-g3 p.npx-hidden-text {
  font-size: 1rem;
  font-weight: 300;
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
  width: 100%;
  height: 100px;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 128px;
    height: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 182px;
    height: 182px;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
  max-width: 100px;
  display: inline-block;
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    display: inherit;
    max-width: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    max-width: 100%;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, .sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::after {
  position: absolute;
  content: " ";
  display: block;
  left: -20px;
  top: 0;
  width: calc(100% + 40px);
  height: 100%;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
  z-index: 0;
  background: transparent url(../images/wyznaczamy-standardy-1.png) repeat-y 0 0;
  -webkit-background-size: 150% auto;
          background-size: 150% auto;
}
@media (max-width: 74.99875rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    background-position: -100px 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    position: absolute;
  }
}
@media (min-width: 75rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    -webkit-background-size: 100% auto;
            background-size: 100% auto;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::after {
  z-index: 1;
  background: transparent url("../images/wyznaczamy-standardy-2.png") repeat-x left bottom;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item, body.node--type-page .field--name-field-cechy .field__item {
  z-index: 10;
  clear: both;
  margin-bottom: 1.5625rem;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
    margin-bottom: 1.5625rem;
  }
}
.sekcja.wyznaczamy-standardy .paragraph--type-cecha-par, body.node--type-page .paragraph--type-cecha-par {
  max-width: 900px;
}

.field_cechy_field_item_even .paragraph--type-cecha-par {
  margin-left: auto;
}

.paragraph--type-cecha-par .field--name-field-opis-trenera {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 128px);
    padding: 0 0 0 3.125rem;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 182px);
    padding: 0 0 0 6.25rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera p {
  display: inline-block;
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
  position: relative;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.625rem;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -2.8125rem;
    text-align: left;
    margin-bottom: 0;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -5rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
  display: block;
  max-width: 100px;
  margin: 1.875rem auto;
  padding: 0 0.625rem;
  vertical-align: middle;
  line-height: 1.25rem;
  font-size: 0.625rem;
  color: #fff;
  background-color: #f73965;
  position: relative;
  height: 20px;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
    display: inline-block;
    margin-top: 0;
    margin-left: 0.3125rem;
    vertical-align: sub;
  }
}

a.benefits-linker {
  font-weight: bold;
  line-height: 1.9375rem;
  color: #000;
}
a.benefits-linker:hover, a.benefits-linker:active {
  color: #000;
}
@media (min-width: 36rem) {
  a.benefits-linker {
    margin-left: -2.8125rem;
  }
}
@media (min-width: 62rem) {
  a.benefits-linker {
    margin-left: -5rem;
  }
}

#szkolenie-grupa-13.g13.voucher .narrow h2 {
  margin-left: 0;
  margin-right: 0;
}

.voucher-col-image {
  max-width: 100%;
  margin: 1rem auto;
  text-align: center;
}
@media (min-width: 62rem) {
  .voucher-col-image {
    margin: inherit;
  }
}
.voucher-col-image a {
  margin: 0 auto;
  display: inline-block;
}
.voucher-col-image img {
  max-width: 100%;
}

.voucher-bottom {
  margin-top: 0;
}
@media (min-width: 62rem) {
  .voucher-bottom {
    margin-left: 1rem;
  }
}

.voucher-col-text {
  text-align: center;
  margin-top: 2rem;
}
@media (min-width: 62rem) {
  .voucher-col-text {
    text-align: left;
    margin-top: inherit;
  }
}
.voucher-col-text ul {
  text-align: left;
  list-style-image: url("../images/voucher-check-mark.svg");
}

.g17.sekcja.szybki-kontakt {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}

#szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .no-margin .js-form-type-checkbox {
  margin-left: 0.75rem;
}
@media (max-width: 35.99875rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item {
    text-align: center;
  }
}
@media (min-width: 48rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-top: 0.3125rem;
  }
}
@media (min-width: 68.8125rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item, #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > input {
    width: 33%;
  }
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-left: 1.5rem;
    margin-top: 2rem;
  }
}

#npx-contact-form-wrapper {
  max-width: 1100px;
}
#npx-contact-form-wrapper .form-type-textfield label {
  width: 100%;
}
#npx-contact-form-wrapper #edit-right-col-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-contact-form-wrapper #edit-right-col-row fieldset {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    margin-left: 0;
    margin-right: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset.js-form-item-name {
    padding-left: 0;
  }
}
#npx-contact-form-wrapper .no-margin {
  margin: 0;
  padding: 0;
}
#npx-contact-form-wrapper fieldset {
  max-width: 250px;
}
#npx-contact-form-wrapper .error + div .field-suffix {
  display: block;
}
#npx-contact-form-wrapper .error {
  border-color: red;
}
#npx-contact-form-wrapper .form-checkbox.error + label {
  color: red;
}
#npx-contact-form-wrapper .field-suffix {
  display: none;
  font-size: 0.8125rem;
}
#npx-contact-form-wrapper .npx-contact-thx img {
  height: 60px;
  margin-right: 1.25rem;
}
#npx-contact-form-wrapper .npx-contact-thx .n-big {
  font-size: 1.5rem;
}
#npx-contact-form-wrapper .npx-contact-txt-info {
  margin-bottom: 1rem;
  margin-top: 2.2rem;
  font-size: 1.375rem;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .npx-contact-txt-info {
    margin-bottom: inherit;
  }
}
#npx-contact-form-wrapper .npx-contact-txt-info .n-sm {
  font-size: 1.125rem;
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col {
    position: relative;
  }
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col::before {
    content: "";
    position: absolute;
    top: 45px;
    left: -75px;
    width: 73px;
    height: 78px;
    background: url("../images/call_question_icon.svg") left center no-repeat;
  }
}
#npx-contact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
#npx-contact-form-wrapper .right-col .row {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .right-col .row {
    -webkit-box-pack: inherit;
    -webkit-justify-content: inherit;
        -ms-flex-pack: inherit;
            justify-content: inherit;
  }
}
#npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  max-width: 210px;
}
#npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 1rem;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-right: 0;
    margin-left: calc(var(--bs-gutter-x) * 0.5);
  }
}
#npx-contact-form-wrapper .button.form-submit::hover {
  background-color: #034b7d;
  text-decoration: none;
}
#npx-contact-form-wrapper .messages {
  display: none !important;
}
#npx-contact-form-wrapper .ajax-progress-throbber {
  background: transparent;
}
#npx-contact-form-wrapper .form-item-phone input {
  background: #fff url("../images/phone_icon.svg") left center no-repeat;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
#npx-contact-form-wrapper .form-item-name input {
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: 8px;
}
#npx-contact-form-wrapper .required-info {
  font-size: 0.75rem;
  text-align: right;
  max-width: 135px;
  padding-right: 0;
}
#npx-contact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
#npx-contact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.simple-popup-blocks-global .spb_center {
  margin-left: -25rem;
  margin-top: -12.5rem;
}

#block-exitpopupblock {
  display: none;
}
#block-exitpopupblock * {
  content-visibility: auto;
}
#block-exitpopupblock > h2 {
  display: none;
}

.popover {
  z-index: 999999 !important;
}
.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
}
.popover a {
  text-decoration: underline;
}

#spb-block-exitpopupblock .js-form-item {
  padding-left: 0;
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .left-col, #spb-block-exitpopupblock .right-col {
    margin-bottom: 0 !important;
  }
}
#spb-block-exitpopupblock .form-item-name {
  margin-right: 0.125rem;
}
@media (min-width: 36rem) {
  #spb-block-exitpopupblock .form-item-name input {
    margin-right: -1.25rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-item-name {
    margin-left: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-type-textfield {
    margin-top: 0;
    margin-bottom: 0 !important;
  }
  #spb-block-exitpopupblock .form-type-textfield input {
    width: 140px;
  }
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .wrapper-checkbox-npx {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .left-col::before {
  width: 0;
  height: 0;
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .npx-contact-txt-info {
  margin-top: 1rem;
}
#spb-block-exitpopupblock .col-sm-4.left-col {
  width: 100%;
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  max-width: 100%;
  padding-left: 0;
}
#spb-block-exitpopupblock .col-sm-8.right-col {
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  width: 100%;
  max-width: 100%;
}
#spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
    max-width: 98%;
  }
}
#spb-block-exitpopupblock .col-sm-8.right-col > .row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
#spb-block-exitpopupblock .spb_close {
  border: 0;
  background: transparent;
  font-size: 1.625rem;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .right-col-npx {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
    max-width: 100%;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 1rem auto 0 0.3125rem;
  width: 100%;
  font-weight: 700;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: 0;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  display: block ruby;
  -webkit-box-ordinal-group: 5;
  -webkit-order: 4;
      -ms-flex-order: 4;
          order: 4;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .required-info {
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .form-item {
  margin-top: 0.5rem;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper h5, #spb-block-exitpopupblock #npx-contact-form-wrapper h6 {
  font-weight: 600;
  margin-bottom: 0;
}

.npx-contact-exit-popup-block-wrapper {
  max-width: 1200px;
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .spb-controls {
    right: -2px;
    top: -3px;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 1.25rem 2rem 0;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container {
    padding: 0.25rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
  margin: 0;
  color: var(--secondary);
  font-size: 3rem;
  font-weight: normal;
  line-height: 3rem;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
    font-size: 1.5rem;
    line-height: 1.75rem;
    padding: 0.5rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
  margin: 1rem 0 !important;
  color: #000 !important;
  font-size: 1.3125rem !important;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    font-size: 1.25rem !important;
    line-height: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    margin: 0.5rem 0 !important;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container p {
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container p {
    margin: 0.5rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container hr {
  width: 60%;
  margin-bottom: 0;
  margin-right: auto;
  margin-left: auto;
}
.npx-contact-exit-popup-block-wrapper .nxp-columns-container {
  padding-left: 2rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column img {
  max-width: 200px;
  margin-right: 2.5rem;
  margin-left: 0.625rem;
}
.npx-contact-exit-popup-block-wrapper .toast-wrapper {
  display: none !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-item {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-text {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-email {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input.error {
  border: 2px solid red !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox {
  margin: 0;
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../img/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input:checked + label {
  background-image: url("../img/checkbox-on-blue.png");
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept.form-checkbox.error + label a {
  color: red;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 7px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: bold;
  pointer-events: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 320px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: normal;
  line-height: 0.875rem;
  font-size: 0.75rem;
}
.npx-contact-exit-popup-block-wrapper .npx-msg {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .ajax-progress.ajax-progress-throbber {
  background: transparent;
  margin-top: 5rem;
}

.spb-popup-main-wrapper.spb_center {
  width: 800px !important;
  margin: 0 auto !important;
  -webkit-transform: translate(-50%, -45%);
       -o-transform: translate(-50%, -45%);
          transform: translate(-50%, -45%);
  background-color: #eceeef;
  border: #666 1px solid;
  border-radius: 8px;
  max-height: 100vh;
  overflow-y: auto;
}
@media (max-width: 47.99875rem) {
  .spb-popup-main-wrapper.spb_center {
    max-width: 98%;
  }
}

.spb_close {
  border: none;
  background: transparent;
  font-size: 1.875rem;
}

@media (max-width: 61.99875rem) {
  .block-bootstrap4grow-freshmailpopupexitstronazcytatami-modal.spb_overlay {
    display: none;
  }
}

.npx-dates-table-wrapper {
  margin: 0 0 2.5rem;
}

.npx-date-table-elem {
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
  border: 1px solid #d0d8db;
  width: 100%;
}
@media (min-width: 36rem) {
  .npx-date-table-elem {
    padding: 0.9375rem 2.5rem 0.9375rem 2.5rem;
    width: auto;
  }
}
.npx-date-table-elem:nth-child(1) {
  background-image: url("../images/radio-on.png");
  background-repeat: no-repeat;
  background-position: 30px 20px;
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
}
@media (max-width: 35.99875rem) {
  .npx-date-table-elem:nth-child(1) {
    border-bottom: 0;
  }
}
@media (min-width: 36rem) {
  .npx-date-table-elem:nth-child(1) {
    border-right: 0;
  }
}
.npx-date-table-elem::after {
  display: block;
  position: absolute;
  left: 50%;
  top: 0;
  margin-left: -1rem;
  margin-top: -1rem;
  background: transparent url("../images/termin-plus.png") no-repeat 0 0;
  width: 32px;
  height: 32px;
  content: "";
}
@media (min-width: 36rem) {
  .npx-date-table-elem::after {
    left: 0;
    top: 50%;
  }
}
.npx-date-table-elem:nth-child(1)::after {
  display: none;
}

.npx-date-title {
  height: 30px;
  font-size: 1.25rem;
  line-height: 1.875rem;
}

.npx-date-desc {
  height: 20px;
  font-size: inherit;
  line-height: 1.25rem;
  color: #a2a2a2;
}

#npx-training-form .npx-training-date-dates-header {
  line-height: 1.5;
  margin: 2rem 0 1rem 0;
}

.npx-training-date-not-guaranted {
  background: #cfd8dd;
  left: -1px;
  top: calc(100% + 1px);
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}
.npx-training-date-guaranted {
  left: -1px;
  top: calc(100% + 1px);
  background: #fecc09;
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}

.npx-dates-variant-wrapper .ajax-progress.ajax-progress-throbber {
  top: -20px;
  left: -40px;
}
.npx-dates-variant-wrapper .js-form-type-radio input {
  display: none;
}
.npx-dates-variant-wrapper .js-form-type-radio label {
  line-height: 2rem;
  background-color: #fff;
  background-image: url("../images/radio-off.png");
  background-repeat: no-repeat;
  display: block;
  cursor: pointer;
  -webkit-background-size: 25px 25px;
          background-size: 25px;
}
.npx-dates-variant-wrapper .js-form-type-radio .form-radio[type=radio]:checked + label {
  background-color: #fff;
  background-image: url("../images/radio-on.png");
  padding-left: 2.25rem;
}

.npx-counter-wrapper {
  margin-left: -0.25rem;
}

.npx-tabs {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  .npx-tabs {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 75rem) {
  .npx-box-left {
    padding-right: 1rem;
  }
}
@media (max-width: 74.99875rem) {
  .npx-box-left {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
  }
}

.npx-counter-info {
  color: red;
}

.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  -webkit-background-size: cover;
          background-size: cover;
  margin-right: 0.375rem;
  margin-left: 0.3125rem;
}

#npx-price-info-wrapper {
  font-size: 0.875rem;
}
#npx-price-info-wrapper .form-item-npx-discount-code {
  position: relative;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code input[type=text] {
  margin: 0;
  height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 1px;
  width: 100%;
  max-width: 200px;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix {
  background-color: #fff;
  border: 0;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix a {
  display: block;
  position: absolute;
  z-index: 10;
  height: 34px;
  background: transparent url("../images/przelicz.png") no-repeat center center;
  width: 20px;
  text-align: left;
  text-indent: -9990px;
  right: 10px;
  top: 0;
  outline: 0;
  border: 0;
  margin-right: 0.25rem;
}
#npx-price-info-wrapper #npx-expand-bottom-wrapper {
  width: 100%;
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper #npx-expand-bottom-wrapper {
    width: auto;
    margin-right: 1rem;
  }
}
#npx-price-info-wrapper .list-group-item {
  border: none;
  display: list-item;
  margin-left: 1.25rem;
  padding: 0;
}
#npx-price-info-wrapper .item-list {
  padding-top: 3rem;
}
#npx-price-info-wrapper li {
  list-style-image: url("../images/li.png");
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper .npx-social-colorbox-link {
    top: -15px;
  }
}
#npx-price-info-wrapper .npx-social-colorbox-link a::before {
  vertical-align: sub;
  height: 20px;
  width: 20px;
  margin: 0 0.3125rem 0 0;
  background: transparent url("../images/price-tag.png") no-repeat center center;
  -webkit-background-size: auto auto;
          background-size: auto;
  background-size: auto;
  -webkit-background-size: cover;
          background-size: cover;
  display: inline-block;
  content: "";
}

@media (min-width: 48rem) {
  .npx-box-left .npx-price-b {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -0.9375rem;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0.9375rem;
  }
}
@media (min-width: 75rem) {
  .npx-box-left .npx-price-b {
    margin-top: 0;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0;
  }
}

@media (max-width: 35.99875rem) {
  .npx-price {
    min-height: 150px;
  }
}
.npx-price-a {
  padding: 1.875rem 2.5rem 0 0;
}
.npx-price-a-a {
  line-height: 1.25rem;
  font-size: 0.875rem;
}
.npx-price-a-b {
  font-size: 1.25rem;
}
.npx-price-b {
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-price-b {
    width: 60%;
    padding: inherit;
    padding-top: 3.125rem;
  }
}
@media (min-width: 62rem) {
  .npx-price-b {
    padding-top: 3.125rem;
    width: auto;
  }
}
.npx-price-b-a {
  font-size: 1.25rem;
}
.npx-price-b-b {
  font-size: 1.125rem;
}
.npx-price-b-c {
  color: #a2a2a2;
}
@media (max-width: 47.99875rem) {
  .npx-price-b-c {
    font-size: 0.8125rem;
  }
}
@media (min-width: 75rem) {
  .npx-price-b-c {
    top: 5px;
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
}
@media (min-width: 48rem) {
  .npx-counter-wrapper {
    top: -10px;
  }
}

.npx-calculation-box {
  padding: 1.875rem 1.875rem 0;
  margin: 0 -1.875rem;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
@media (min-width: 36rem) {
  .npx-calculation-box {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
}
.npx-calculation-box .list-group-item {
  background: transparent;
  padding-left: 0;
}
.npx-calculation-box input {
  max-width: 200px;
}

#npx-participants-amount-wrapper .description {
  font-size: 1em;
}
#npx-participants-amount-wrapper small.text-muted {
  max-width: calc(100% - 149px);
  float: right;
  color: #000 !important;
  font-size: 1rem;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper small.text-muted {
    float: none;
    max-width: 100%;
  }
}
#npx-participants-amount-wrapper span.ui-spinner {
  display: inline-block;
  position: relative;
  border: 1px solid #d0d8db;
  padding: 0 2.8125rem;
  border-radius: 0;
  margin: 0 0.625rem 0 0;
}
#npx-participants-amount-wrapper span.ui-spinner .form-control:focus {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#npx-participants-amount-wrapper span.ui-spinner input {
  border: 0;
  height: 44px;
  line-height: 2.8125rem;
  padding: 0 0.625rem;
  margin: 0;
  border-radius: 0;
  width: 45px;
  text-align: center;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper span.ui-spinner input {
    height: 44px;
  }
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button {
  border: 0;
  outline: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
  background-color: transparent;
  opacity: 0.85;
  padding: 0;
  margin: 0;
  right: auto;
  background-image: url("../images/spinner-min.png");
  background-position: center center;
  background-repeat: no-repeat;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button:hover {
  background-color: #ebeff2;
  cursor: pointer;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-tr {
  background-image: url("../images/spinner-plus.png");
  left: auto;
  right: 0;
  border-left: 1px solid #ddd;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-br {
  border-right: 1px solid #ddd;
}

#szkolenie-grupa-8 a.npx-form-tab {
  margin: 0.9375rem 0;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-8 a.npx-form-tab {
    padding: 1.0625rem;
    margin: 1.25rem 0.9375rem;
  }
}
#szkolenie-grupa-8 .form-item-npx-training {
  display: none;
}

a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
@media (min-width: 48rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 45%;
        -ms-flex: 1 0 45%;
            flex: 1 0 45%;
    max-width: 45%;
  }
}
@media (min-width: 75rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 29%;
        -ms-flex: 1 0 29%;
            flex: 1 0 29%;
    max-width: 29%;
  }
}
a.npx-form-tab:hover {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 101;
}
a.npx-form-tab.npx-active-tab {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 100;
}

.npx-form-outer-wrapper {
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}

#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 0.3rem;
}
@media (min-width: 36rem) {
  #npx-top-wrapper > div:not(#npx-tabs) {
    padding: 0 2.5rem;
  }
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.74) 1px;
  border-radius: 4px;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper {
    height: 100%;
  }
}

li a.active .npx-blocks-program-tab-wrapper, li a:hover .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

#npx-regular-box-wrapper {
  opacity: 0.65;
}

.npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
  opacity: 0.65;
}
.npx-box-right:not(.npx-active-box) .npx-price-b-a, .npx-box-left:not(.npx-active-box) .npx-price-b-a {
  text-decoration: line-through;
}
@media (max-width: 74.99875rem) {
  .npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
    display: none !important;
  }
}

#npx-regular-box-wrapper .npx-price-b-c {
  top: 0;
}

.npx-active-tab .npx-training-form-tab-wrapper, .npx-form-tab:hover .npx-training-form-tab-wrapper {
  -webkit-box-shadow: 0 0 15px 0 #54534f;
  box-shadow: 0 0 15px 0 #54534f;
  border-radius: 4px;
}

.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
  width: 100%;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
    width: auto;
  }
}
.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word-last {
  padding-left: 0.625rem;
}
.npx-training-form-type-info-wrapper .npx-spoiler-content {
  font-size: inherit;
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm ul li {
  list-style-image: url("../images/online-li-yellow.png");
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm:nth-child(1) ul li {
  list-style-image: url("../images/online-li-blue.png");
}
.npx-training-form-type-info-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  background-color: #fff;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.show-icon::before {
  content: "ROZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.hide-icon::before {
  content: "ZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
  font-weight: 600;
}
.npx-training-form-type-info-wrapper .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background: var(--secondary);
}

.tr-form-stationary {
  padding: 0.1875rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  font-weight: 800;
  color: #000;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}
.tr-form-online {
  padding: 0.1875rem 1rem;
  border-radius: 20px;
  font-weight: 800;
  background: var(--secondary);
  color: #fff;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.npx-variant .fieldset-legend {
  color: #000;
  margin: 2rem 0;
  display: block;
}
.npx-variant h4 {
  line-height: 1.5rem;
}

.npx-training-form-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.44) 1px;
  border-radius: 4px;
  height: 100%;
}
.npx-training-form-tab-wrapper .n-tab-header-inner {
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header-inner {
  border-radius: 4px 4px 0 0;
  min-height: 183px;
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header {
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
  border-radius: 4px 4px 0 0;
}
.npx-training-form-tab-header-hours {
  text-shadow: none;
  font-size: 0.75rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.npx-training-form-tab-header-type {
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  font-size: 0.625rem;
}
.npx-training-form-tab-header-type .stationary {
  padding: 0.125rem 0.5rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
}
.npx-training-form-tab-header-type .live-online {
  padding: 0.125rem 0.5rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .webinar, .npx-training-form-tab-header-type .online {
  padding: 0.125rem 0.5rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .stationary, .npx-training-form-tab-header-type .live-online, .npx-training-form-tab-header-type .online {
  max-height: 20px;
  display: inline-block;
}
.npx-training-form-tab-header-title h3 {
  font-size: 1.3rem;
  text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
  text-transform: none;
  color: #fff;
  font-weight: 600;
  margin: 2rem 0 1.2rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.npx-training-form-tab-content {
  text-transform: none;
}
.npx-training-form-tab-content p {
  font-size: 0.9375rem;
}
.npx-training-form-tab-content ul {
  padding-left: 0.9375rem;
}
.npx-training-form-tab-content ul li {
  font-size: 0.9375rem;
  text-transform: none;
  font-weight: normal;
  text-align: left;
  list-style-image: url("../images/li_checkmark.png");
  line-height: 1.3rem;
}
.npx-training-form-tab-more {
  margin: auto 0.9375rem 0.625rem 0;
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content .tab-pane {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}

.ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::before, .ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::after {
  background-color: #000;
}

.pdf-program-link img {
  width: 48px;
  height: auto;
}

.program-accordion ul {
  padding-left: 1.2rem;
}
.program-accordion li {
  margin: 0.5rem 0 0.5rem 1.1rem;
}
.program-accordion .pdf-program {
  z-index: 5;
  position: relative;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program {
    float: right;
  }
}
.program-accordion .pdf-program-link {
  margin-left: 0.625rem;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link {
    margin-left: 1.25rem;
  }
}
.program-accordion .pdf-program-link img {
  width: 38px;
  display: inline-block;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link img {
    width: 48px;
  }
}
.program-accordion .pdf-program-link a {
  margin: 0.9375rem;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program span {
    display: block;
    margin-bottom: 0.625rem;
  }
}
@media (min-width: 62rem) {
  .program-accordion h2.field-label-above {
    margin-right: 10.625rem;
  }
}
.program-accordion .ckeditor-accordion-container h4 {
  font-size: 1.5rem;
  font-weight: 700;
}
.program-accordion .ckeditor-accordion-container > dl dt > a, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button) {
  color: inherit;
  border-bottom: #dcdddf 1px solid;
  text-decoration: none;
}
.program-accordion .ckeditor-accordion-container > dl dt > a:hover, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button):hover {
  color: inherit;
  text-decoration: underline;
}
.program-accordion .ckeditor-accordion-toggler {
  background-color: transparent !important;
  color: inherit;
  font-size: 1.2rem;
}
.program-accordion dl dt > a {
  background-color: #ecedef;
  color: #000;
  border-bottom: #dcdddf 1px solid;
}
.program-accordion dl {
  border: #dcdddf 1px solid;
}

#szkolenie-grupa-6.sekcja.w100.w100limitContent .tab-content {
  background: #f1fbfc;
  padding: 1.875rem 0;
}

.npx-training-form-tab-header {
  position: relative;
}
.npx-training-form-tab-header-inner {
  z-index: 9;
  position: relative;
}
.npx-training-form-tab-header img {
  position: absolute;
  z-index: 2;
}

.npx-more-program-tabs-wrapper {
  background: #f1fbfc;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 49%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 49%;
        -ms-flex: 1 0 49%;
            flex: 1 0 49%;
  }
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .npx-more-program-tabs-wrapper .item-list {
  margin-top: 0;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
}
.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link .n-tab-header {
  height: 100%;
}

.npx-more-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-more-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-more-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-more-program-tabs-wrapper .nav-tabs li {
  background: transparent;
}
.npx-more-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper {
  background: #fff;
}
.npx-more-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-more-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-more-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-more-tabs-txt-wrapper {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
.npx-more-tabs-txt-wrapper .npx-more-tabs-txt {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.npx-more-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-more-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-more-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
}
@media (min-width: 62rem) {
  .training-terms-block {
    width: 100%;
  }
}
.training-terms-block-wrapper {
  overflow-x: scroll;
}
@media (min-width: 62rem) {
  .training-terms-block-wrapper {
    overflow-x: auto;
  }
}
.training-terms-block-td-1 {
  width: 16%;
}
.training-terms-block-td-2 {
  width: 20%;
}
.training-terms-block-td-3 {
  width: 12%;
}
.training-terms-block-td-4 {
  width: 6%;
}
.training-terms-block-td-5 {
  width: 12%;
}
.training-terms-block-h4 {
  font-size: 1rem;
}
.training-terms-block-with-sustable-table {
  margin: 0.3125rem 0 0.625rem 0;
}
.training-terms-block-th {
  padding-bottom: 0.625rem;
}
.training-terms-block-td-clickable::after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
  margin-left: 0.625rem;
  -o-transition: -o-transform 300ms ease;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
     transform: scaleY(-1);
}
.training-terms-block-td-clickable.open::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
.training-terms-block-npx-form-button {
  padding: 0.625rem 0.9375rem;
  white-space: nowrap;
  min-width: 125px;
}
.training-terms-block .ask-for-course-closed {
  padding: 0.625rem 0.9375rem;
}

.load-more-terms {
  border: 2px solid #0053B3;
  margin: -1.5rem auto 1.875rem;
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
}
.load-more-terms-bg {
  height: 0;
  background: #d0d8db;
  margin-top: 4.375rem;
}
.load-more-terms-wrapper {
  position: absolute;
  width: calc(100vw - 40px);
}
@media (min-width: 62rem) {
  .load-more-terms-wrapper {
    position: relative;
    width: auto;
  }
}

.paragraph--type-arguments {
  margin-top: 3.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  .paragraph--type-arguments {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.paragraph--type-arguments .group-left {
  width: 100%;
  height: 100px;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-left {
    width: 45%;
  }
}
.paragraph--type-arguments .group-right {
  width: 100%;
  height: auto;
  background: transparent;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-right {
    width: 55%;
    height: 100px;
    background: #f4f7f5;
    border-radius: 100px 0 0 100px;
  }
}
.paragraph--type-arguments a.n-get-pdf {
  padding-right: 2.5rem;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments a.n-get-pdf {
    padding-right: 0;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right {
  padding-left: 1.25rem;
  max-width: 400px;
  margin-left: auto;
  border-left: #000 1px solid;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .field--name-field-arguments-right {
    padding-left: 2.5rem;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right p {
  font-size: 0.7rem;
  line-height: 1rem;
}
.paragraph--type-arguments .field--name-field-arguments-left {
  max-width: 300px;
  padding-left: 3.125rem;
  font-size: 1.2rem;
}
.paragraph--type-arguments .field--name-field-arguments-left::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  z-index: -1;
  background: #f4f7f5;
  border-radius: 100px;
}

.inner-menu-sticky #block-npxfloatingbeltblock {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #ebedec;
}

#block-npxfloatingbeltblock {
  top: 0px;
  display: none;
  z-index: 9999;
  width: 100%;
  left: 0;
  right: 0;
}
@media (min-width: 68.8125rem) {
  #block-npxfloatingbeltblock {
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }
}
#block-npxfloatingbeltblock * {
  content-visibility: auto;
}

.npx-floating-block-wrapper {
  max-width: 1415px;
}
.npx-floating-block-button-wrapper {
  padding-right: 0.625rem;
}
.npx-floating-block-form-button-wrapper {
  padding-right: 0.625rem;
}
@media (min-width: 48rem) {
  .npx-floating-block-amount-wrapper {
    margin-right: 1rem;
  }
}
@media (min-width: 62rem) {
  .npx-floating-block-amount-wrapper {
    line-height: 4.5rem;
  }
}

#npx-floating-block-wrapper {
  max-width: 1415px;
}
#npx-floating-block-wrapper img {
  max-height: 66px;
}

h2.field-label-above p {
  font-size: inherit;
  font-weight: inherit;
  margin-bottom: inherit;
}

@media (min-width: 62rem) {
  .field--name-field-blog-posts {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .field--name-field-blog-posts .node--type-article.node--view-mode-grow3 {
    height: 100%;
    display: block;
    position: relative;
    padding-bottom: 3.5rem;
  }
  .field--name-field-blog-posts .field--name-node-link {
    margin-left: auto;
    margin-right: auto;
  }
  .field--name-field-blog-posts .field__item {
    max-width: 92%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 32%;
        -ms-flex: 1 0 32%;
            flex: 1 0 32%;
  }
}
.field--name-field-blog-posts .field__item {
  text-align: center;
}
.field--name-field-blog-posts h3 {
  margin: 0;
  font-size: 1.25rem;
}
.field--name-field-blog-posts img {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  -o-transition: -o-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s, -o-transform 0.2s;
}
.field--name-field-blog-posts img:hover {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
}
.field--name-field-blog-posts .field--name-field-image {
  overflow: hidden;
}
.field--name-field-blog-posts .node--type-article {
  max-width: 450px;
  margin: 0 auto;
}
@media (max-width: 61.99875rem) {
  .field--name-field-blog-posts .node--type-article {
    margin-bottom: 2rem;
  }
}
.field--name-field-blog-posts .node--type-article h3 a {
  color: #000;
}

#block-valentinesexitpopupotherpagesblock, #block-valentinesexitpopuptrainingblock {
    display: none;
}

#spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
  max-width: 1000px;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  border: none;
}
@media (max-width: 35.99875rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    max-width: 98%;
  }
}
@media (min-width: 36rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    width: 80% !important;
  }
}

.valentines-fb {
  height: 20%;
  width: 28%;
  right: 7%;
  bottom: 2%;
}

.valentines-link {
  height: 12%;
  width: 22%;
  left: 12%;
  bottom: 4%;
}

.npx-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
