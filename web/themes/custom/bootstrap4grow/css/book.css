/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

[id^=block-bootstrap4grow-traininglistfrontblock] {
  padding: 0 1.5625rem 1.5625rem;
  border: 3px solid #f07925;
  max-width: 300px;
}
@media (min-width: 36rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 960px;
  }
}
@media (min-width: 75rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 1200px;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock].content > div::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] > div:not(.fakeheader) {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] h3 {
  color: #f07925;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  font-weight: 800;
  font-size: 2.75rem;
  margin: 2.1875rem 0 0;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-city {
  font-weight: 600;
  font-size: 1.25rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-closest {
  font-weight: 600;
  font-size: 2.1875rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
  margin-top: 1.875rem;
  padding: 0.3125rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  outline: 0 #f07925 solid;
  -webkit-transition: 0.1s all;
  -o-transition: 0.1s all;
  transition: 0.1s all;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
    max-width: 47%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 47%;
        -ms-flex: 1 0 47%;
            flex: 1 0 47%;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
    max-width: 25%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row:hover, [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row:focus {
  outline: 3px #f07925 solid;
  cursor: pointer;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-background-size: cover;
          background-size: cover;
  background-image: none;
  background-color: #1e3850;
  color: #fff;
  text-align: center;
  padding: 0;
  height: 260px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 5rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
  background-image: url(../images/szkolenie-tlo.png);
  background-repeat: none;
  -webkit-background-size: cover;
          background-size: cover;
  margin-top: 16.25rem;
  height: auto;
  padding: 0.9375rem;
  font-size: 1.125rem;
  position: absolute;
  bottom: 0;
  width: 100%;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    height: 130px;
    padding: 1.875rem;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    padding: 0.75rem 1rem;
  }
}
@media (min-width: 75rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    padding: 1.5rem 1rem;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .post-wrapper {
  position: absolute;
  bottom: -88px;
  width: 100%;
  padding: 0.9375rem 0;
  color: #f07925;
  margin-bottom: -1rem;
}

[id^=block-bootstrap4grow-traininglistblockwithfilters] {
  max-width: 1200px;
}
[id^=block-bootstrap4grow-traininglistblockwithfilters] h2.block-title {
  text-align: center;
  color: #aeaeb0;
  font-size: 2.75rem;
  line-height: 1rem;
  text-transform: uppercase;
  margin-bottom: 0.9375rem;
  border-bottom: 1px solid #aeaeb0;
  font-weight: 100;
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistblockwithfilters] h2.block-title {
    text-align: left;
  }
}

[id^=npx-training-list-training-list-form] .content {
  max-width: 1200px;
  margin: 0 auto;
}
[id^=npx-training-list-training-list-form] .form-submit {
  color: #fff;
  background: #05407F;
  text-transform: uppercase;
  padding: 0.625rem 0.9375rem;
  height: auto;
  -webkit-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  width: auto;
  margin: 0 auto;
  border-radius: 7px;
  border: none;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] .form-submit {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 30%;
        -ms-flex: 1 0 30%;
            flex: 1 0 30%;
    padding: 0.5rem;
    margin-left: 1.875rem;
    margin-bottom: 0.5rem;
    max-height: 40px;
    margin-top: 1rem;
  }
}
[id^=npx-training-list-training-list-form] fieldset {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 50%;
      -ms-flex: 1 0 50%;
          flex: 1 0 50%;
}
[id^=npx-training-list-training-list-form] .ajax-progress-throbber {
  background: transparent;
}
[id^=npx-training-list-training-list-form] .ajax-progress-throbber .throbber {
  display: inline-block;
  min-width: 10px;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] [id^=edit-training-category] {
    height: 40px;
  }
}
[id^=npx-training-list-training-list-form] [id^=edit-filters] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0 auto;
  text-align: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] [id^=edit-filters] {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
    max-width: 450px;
  }
}

.npx-training-list-rows-wrapper h2.title {
  color: #057DBC;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5;
  position: relative;
}
.npx-training-list-rows-wrapper h2.title a {
  color: #0173BC;
}
.npx-training-list-rows-wrapper .npx-closest-dates {
  padding-top: 3.125rem;
}
.npx-training-list-rows-wrapper .npx-row {
  z-index: 10;
  background-color: #ffffff;
  *zoom: 1;
  filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr="#FFFFFFFF", endColorstr="#FFF5F5F5");
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2Y1ZjVmNSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==");
  -webkit-background-size: 100% 100%;
          background-size: 100%;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #ffffff), color-stop(100%, #f5f5f5));
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#f5f5f5));
  background-image: -o-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
  border: 1px solid #E5E5E5;
  padding: 0.9375rem 0;
  -webkit-box-shadow: 0 0 5px 0 #e5e5e5;
          box-shadow: 0 0 5px 0 #e5e5e5;
}
.npx-training-list-rows-wrapper .npx-row-inner {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.npx-training-list-rows-wrapper .npx-row-inner::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}
.npx-training-list-rows-wrapper .npx-right {
  margin: 0.9375rem 1.5625rem;
  border-top: 1px solid #E5E5E5;
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-right {
    border-top: none;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-right {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 26%;
        -ms-flex: 1 0 26%;
            flex: 1 0 26%;
  }
}
.npx-training-list-rows-wrapper .npx-right a {
  color: #000;
}
.npx-training-list-rows-wrapper .npx-price {
  font-size: 1.2rem;
  padding: 0.875rem 0;
}
.npx-training-list-rows-wrapper .npx-closest {
  font-weight: 500;
}
.npx-training-list-rows-wrapper .npx-closest.button {
  font-size: 1rem;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  margin-top: 2.5rem;
}
.npx-training-list-rows-wrapper .npx-closest.data {
  font-size: 1.5rem;
}
.npx-training-list-rows-wrapper .npx-closest.data::before {
  content: "";
  display: block;
  background: url(../images/calendar.svg) no-repeat;
  width: 25px;
  height: 25px;
  margin: 1.125rem auto;
}
.npx-training-list-rows-wrapper a.npx-more-dates, .npx-training-list-rows-wrapper .npx-closest a {
  color: #fff;
  line-height: 1.7rem;
}
.npx-training-list-rows-wrapper a.npx-more-dates:hover, .npx-training-list-rows-wrapper .npx-closest a:hover {
  text-decoration: none;
}
.npx-training-list-rows-wrapper .npx-left {
  border-right: 1px solid #E5E5E5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 2.8125rem 0 2.5rem;
}
.npx-training-list-rows-wrapper .view-mode-teaser h2, .npx-training-list-rows-wrapper .field--name-field-npxtraining-tytul-formalny {
  color: #057DBC;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
}
.npx-training-list-rows-wrapper .view-mode-teaser h2 a, .npx-training-list-rows-wrapper .field--name-field-npxtraining-tytul-formalny a {
  color: #0173BC;
}
.npx-training-list-rows-wrapper .view-mode-teaser .field--name-body p, .npx-training-list-rows-wrapper .view-mode-teaser .field--name-field-zajawka p {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  line-height: 1.2rem;
}
.npx-training-list-rows-wrapper .npx-closest-dates {
  padding-top: 3.125rem;
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-label {
  font-weight: 800;
  padding: 0 0.9375rem 0 0;
  width: 100%;
  float: left;
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-closest-dates .npx-label {
    width: auto;
    float: none;
  }
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-date {
  border-right: 1px solid #e5e5e5;
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-date:last-child {
  border-right: none;
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-closest-dates .npx-date {
    padding: 0 0.625rem 0 0;
  }
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-left {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 60%;
        -ms-flex: 1 0 60%;
            flex: 1 0 60%;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-left {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 67%;
        -ms-flex: 1 0 67%;
            flex: 1 0 67%;
  }
}
.npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
  max-width: 300px;
}
@media (min-width: 36rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 960px;
  }
}
@media (min-width: 100rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 1200px;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
