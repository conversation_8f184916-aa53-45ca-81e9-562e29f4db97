/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

[id^=block-bootstrap4grow-traininglistfrontblock] {
  padding: 0 1.5625rem 1.5625rem;
  border: 3px solid #f07925;
  max-width: 300px;
}
@media (min-width: 36rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 960px;
  }
}
@media (min-width: 75rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] {
    max-width: 1200px;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock].content > div::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] > div:not(.fakeheader) {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] h3 {
  color: #f07925;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  font-weight: 800;
  font-size: 2.75rem;
  margin: 2.1875rem 0 0;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-city {
  font-weight: 600;
  font-size: 1.25rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-closest {
  font-weight: 600;
  font-size: 2.1875rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
  margin-top: 1.875rem;
  padding: 0.3125rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  outline: 0 #f07925 solid;
  -webkit-transition: 0.1s all;
  -o-transition: 0.1s all;
  transition: 0.1s all;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
    max-width: 47%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 47%;
        -ms-flex: 1 0 47%;
            flex: 1 0 47%;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row {
    max-width: 25%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row:hover, [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row:focus {
  outline: 3px #f07925 solid;
  cursor: pointer;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-background-size: cover;
          background-size: cover;
  background-image: none;
  background-color: #1e3850;
  color: #fff;
  text-align: center;
  padding: 0;
  height: 260px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 5rem;
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
  background-image: url(../images/szkolenie-tlo.png);
  background-repeat: none;
  -webkit-background-size: cover;
          background-size: cover;
  margin-top: 16.25rem;
  height: auto;
  padding: 0.9375rem;
  font-size: 1.125rem;
  position: absolute;
  bottom: 0;
  width: 100%;
}
@media (min-width: 48rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    height: 130px;
    padding: 1.875rem;
  }
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    padding: 0.75rem 1rem;
  }
}
@media (min-width: 75rem) {
  [id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .wrapper {
    padding: 1.5rem 1rem;
  }
}
[id^=block-bootstrap4grow-traininglistfrontblock] .npx-row-inner .post-wrapper {
  position: absolute;
  bottom: -88px;
  width: 100%;
  padding: 0.9375rem 0;
  color: #f07925;
  margin-bottom: -1rem;
}

[id^=block-bootstrap4grow-traininglistblockwithfilters] {
  max-width: 1200px;
}
[id^=block-bootstrap4grow-traininglistblockwithfilters] h2.block-title {
  text-align: center;
  color: #aeaeb0;
  font-size: 2.75rem;
  line-height: 1rem;
  text-transform: uppercase;
  margin-bottom: 0.9375rem;
  border-bottom: 1px solid #aeaeb0;
  font-weight: 100;
}
@media (min-width: 62rem) {
  [id^=block-bootstrap4grow-traininglistblockwithfilters] h2.block-title {
    text-align: left;
  }
}

[id^=npx-training-list-training-list-form] .content {
  max-width: 1200px;
  margin: 0 auto;
}
[id^=npx-training-list-training-list-form] .form-submit {
  color: #fff;
  background: #05407F;
  text-transform: uppercase;
  padding: 0.625rem 0.9375rem;
  height: auto;
  -webkit-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  width: auto;
  margin: 0 auto;
  border-radius: 7px;
  border: none;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] .form-submit {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 30%;
        -ms-flex: 1 0 30%;
            flex: 1 0 30%;
    padding: 0.5rem;
    margin-left: 1.875rem;
    margin-bottom: 0.5rem;
    max-height: 40px;
    margin-top: 1rem;
  }
}
[id^=npx-training-list-training-list-form] fieldset {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 50%;
      -ms-flex: 1 0 50%;
          flex: 1 0 50%;
}
[id^=npx-training-list-training-list-form] .ajax-progress-throbber {
  background: transparent;
}
[id^=npx-training-list-training-list-form] .ajax-progress-throbber .throbber {
  display: inline-block;
  min-width: 10px;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] [id^=edit-training-category] {
    height: 40px;
  }
}
[id^=npx-training-list-training-list-form] [id^=edit-filters] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0 auto;
  text-align: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  [id^=npx-training-list-training-list-form] [id^=edit-filters] {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
    max-width: 450px;
  }
}

.npx-training-list-rows-wrapper h2.title {
  color: #057DBC;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5;
  position: relative;
}
.npx-training-list-rows-wrapper h2.title a {
  color: #0173BC;
}
.npx-training-list-rows-wrapper .npx-closest-dates {
  padding-top: 3.125rem;
}
.npx-training-list-rows-wrapper .npx-row {
  z-index: 10;
  background-color: #ffffff;
  *zoom: 1;
  filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr="#FFFFFFFF", endColorstr="#FFF5F5F5");
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2Y1ZjVmNSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==");
  -webkit-background-size: 100% 100%;
          background-size: 100%;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #ffffff), color-stop(100%, #f5f5f5));
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#f5f5f5));
  background-image: -o-linear-gradient(top, #ffffff 0%, #f5f5f5 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
  border: 1px solid #E5E5E5;
  padding: 0.9375rem 0;
  -webkit-box-shadow: 0 0 5px 0 #e5e5e5;
          box-shadow: 0 0 5px 0 #e5e5e5;
}
.npx-training-list-rows-wrapper .npx-row-inner {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.npx-training-list-rows-wrapper .npx-row-inner::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}
.npx-training-list-rows-wrapper .npx-right {
  margin: 0.9375rem 1.5625rem;
  border-top: 1px solid #E5E5E5;
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-right {
    border-top: none;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-right {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 26%;
        -ms-flex: 1 0 26%;
            flex: 1 0 26%;
  }
}
.npx-training-list-rows-wrapper .npx-right a {
  color: #000;
}
.npx-training-list-rows-wrapper .npx-price {
  font-size: 1.2rem;
  padding: 0.875rem 0;
}
.npx-training-list-rows-wrapper .npx-closest {
  font-weight: 500;
}
.npx-training-list-rows-wrapper .npx-closest.button {
  font-size: 1rem;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  margin-top: 2.5rem;
}
.npx-training-list-rows-wrapper .npx-closest.data {
  font-size: 1.5rem;
}
.npx-training-list-rows-wrapper .npx-closest.data::before {
  content: "";
  display: block;
  background: url(../images/calendar.svg) no-repeat;
  width: 25px;
  height: 25px;
  margin: 1.125rem auto;
}
.npx-training-list-rows-wrapper a.npx-more-dates, .npx-training-list-rows-wrapper .npx-closest a {
  color: #fff;
  line-height: 1.7rem;
}
.npx-training-list-rows-wrapper a.npx-more-dates:hover, .npx-training-list-rows-wrapper .npx-closest a:hover {
  text-decoration: none;
}
.npx-training-list-rows-wrapper .npx-left {
  border-right: 1px solid #E5E5E5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 2.8125rem 0 2.5rem;
}
.npx-training-list-rows-wrapper .view-mode-teaser h2, .npx-training-list-rows-wrapper .field--name-field-npxtraining-tytul-formalny {
  color: #057DBC;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem;
}
.npx-training-list-rows-wrapper .view-mode-teaser h2 a, .npx-training-list-rows-wrapper .field--name-field-npxtraining-tytul-formalny a {
  color: #0173BC;
}
.npx-training-list-rows-wrapper .view-mode-teaser .field--name-body p, .npx-training-list-rows-wrapper .view-mode-teaser .field--name-field-zajawka p {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  line-height: 1.2rem;
}
.npx-training-list-rows-wrapper .npx-closest-dates {
  padding-top: 3.125rem;
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-label {
  font-weight: 800;
  padding: 0 0.9375rem 0 0;
  width: 100%;
  float: left;
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-closest-dates .npx-label {
    width: auto;
    float: none;
  }
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-date {
  border-right: 1px solid #e5e5e5;
}
.npx-training-list-rows-wrapper .npx-closest-dates .npx-date:last-child {
  border-right: none;
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-closest-dates .npx-date {
    padding: 0 0.625rem 0 0;
  }
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-left {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 60%;
        -ms-flex: 1 0 60%;
            flex: 1 0 60%;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-left {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 67%;
        -ms-flex: 1 0 67%;
            flex: 1 0 67%;
  }
}
.npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
  max-width: 300px;
}
@media (min-width: 36rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 960px;
  }
}
@media (min-width: 100rem) {
  .npx-training-list-rows-wrapper .npx-training-list-rows-wrapper {
    max-width: 1200px;
  }
}

.path-frontpage #page {
  background: #0056B3;
}
.path-frontpage .papper_wrapper, .path-frontpage #main-wrapper {
  background: #fff;
}
.path-frontpage a {
  color: #4c4c4c;
}
.path-frontpage a:hover {
  text-decoration: underline;
}
.path-frontpage #main-wrapper {
  margin-top: 1.5rem;
}
.path-frontpage p {
  margin: 1rem 0 0;
}

#block-bootstrap4grow-views-block-referencje-front-block-1 {
  max-width: 300px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 {
    max-width: 96%;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .view-footer {
  text-align: center;
  width: 100%;
  text-align: center;
  border-top: 1px solid #aeaeb0;
  background: #fff;
  color: #aeaeb0;
  padding: 0.625rem 0 1.25rem;
  margin-top: 0.625rem;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .view-header {
  margin: 0 0 0.9375rem;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .view-header {
    margin: inherit;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .view-header h3 {
  text-align: center;
  color: #aeaeb0;
  font-size: 2.75rem;
  text-transform: uppercase;
  padding: 0 0 0.9375rem;
  margin: 0 0 0.9375rem;
  border-bottom: 1px solid #aeaeb0;
  font-weight: 200;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .views-row {
  margin: 1.875rem 5% 0;
  width: 90%;
  background: #fff;
  color: #939598;
  border: 0;
  float: left;
  padding: 0.625rem 0 0;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .views-row {
    padding: inherit;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left {
  width: 100%;
  border-right: 0;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left {
    border-right: inherit;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left::after {
  content: "";
  display: block;
  width: 100%;
  height: 33px;
  margin: 1.5625rem 0 0.625rem;
  background: url(../images/orange-border.png) no-repeat right;
  -webkit-background-size: cover;
          background-size: cover;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left > div {
  padding: 0;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left > div {
    padding: inherit;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left h3 {
  margin: 0;
  font-size: 1.4em;
  line-height: 1.2rem;
  padding: 0;
  font-weight: 800;
  text-align: left;
  color: #000;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left h3 {
    font-size: 1.5rem;
    line-height: inherit;
    padding-right: 1rem;
    margin: 0 0 1rem;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_left .text {
  font-size: 1rem;
  line-height: 1.2rem;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right {
  width: 100%;
  border-left: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .person {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 47%;
      -ms-flex: 1 0 47%;
          flex: 1 0 47%;
  text-align: right;
  font-weight: 400;
  color: #000;
  line-height: 1.2rem;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .person strong {
  text-align: right;
  font-weight: 400;
  color: #000;
  line-height: 1.2rem;
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 47%;
      -ms-flex: 1 0 47%;
          flex: 1 0 47%;
  text-align: right;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo {
    max-width: 168px;
    position: relative;
    text-align: right;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo img {
  padding-bottom: 0.625rem;
  width: 100%;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo img {
    width: auto;
    margin: 0 auto 0 0;
  }
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo img {
    padding-bottom: 2rem;
  }
}
#block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo a {
  font-size: 0.8em;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo a {
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-referencje-front-block-1 .reference_right .logo a:not(.plik) {
    margin-right: 1.6875rem;
  }
}

#block-bootstrap4grow-tytullistyszkolenfront {
  margin-top: 3.125rem;
}
#block-bootstrap4grow-tytullistyszkolenfront h1 {
  text-align: center;
  color: #aeaeb0;
  font-size: 2.75rem;
  text-transform: uppercase;
  padding: 0 0 0.9375rem;
  margin: 0 auto 0.9375rem;
  border-bottom: 1px solid #aeaeb0;
  font-weight: 200;
  max-width: 1200px;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
