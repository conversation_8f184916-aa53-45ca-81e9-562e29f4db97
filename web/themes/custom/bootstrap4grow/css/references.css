/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.path-referencje h1 {
  border-bottom: 1px solid #aeaeb0;
  font-weight: 800;
  padding: 1.25rem 0;
  margin: 0.3125rem 0 1rem;
  text-align: left;
  color: #000;
}
.path-referencje h3 {
  margin: 0;
  line-height: 1.2rem;
  padding: 0;
}
.path-referencje h3 a {
  color: #000;
}
.path-referencje .views-row {
  color: #939598;
  padding: 0.625rem 0 0 0;
}
.path-referencje p {
  margin: 1rem 0 0;
}
.path-referencje .reference_right {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.path-referencje .reference_right a {
  font-size: 0.8rem;
}
.path-referencje .view-header {
  --bs-gutter-x: 24px;
  margin-left: calc(var(--bs-gutter-x) * -0.5);
}
.path-referencje .logo img {
  display: block;
}
.path-referencje .logo a:not(.plik)::after {
  background-image: url(/core/themes/classy/images/icons/application-pdf.png);
  width: 16px;
  height: 16px;
  display: inline-block;
  content: "";
  vertical-align: sub;
}
.path-referencje .logo, .path-referencje .person {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 50%;
      -ms-flex: 1 0 50%;
          flex: 1 0 50%;
}
.path-referencje .person {
  color: #000;
  text-align: right;
}
.path-referencje a {
  color: #4c4c4c;
}
.path-referencje .reference_left::after {
  content: "";
  display: block;
  width: 100%;
  height: 33px;
  margin: 1.5625rem 0 0.625rem;
  background: url(../images/orange-border.png) no-repeat right;
}
@media (min-width: 36rem) {
  .path-referencje .reference_left::after {
    background: url(../images/orange-border-big.png) no-repeat right;
  }
}
.path-referencje ul {
  list-style-image: url("../images/punktor1a.png");
}

.pagination {
  margin-top: 1rem;
  display: block;
}
@media (min-width: 62rem) {
  .pagination {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.pagination .sr-only {
  display: none;
}
.pagination .page-item, .pagination .pager__item--next {
  display: inline-block;
  padding: 0.5rem;
}

#pagination-heading {
  display: none;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
