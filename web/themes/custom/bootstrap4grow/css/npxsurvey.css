/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

[id^=edit-training-form-type--wrapper] legend span {
  font-size: 1rem;
  margin: 0.625rem 0;
  display: block;
}

#survey-pre-form label, #survey-pre-form .npx-survey-label {
  font-size: 1rem;
  font-weight: 800;
  margin: 0.625rem 0;
}

#edit-user-opinion {
  min-height: 7.5em;
}
@media (min-width: 48rem) {
  #edit-user-opinion {
    min-height: 4.6em;
  }
}

@media (min-width: 62rem) {
  #edit-name, #edit-surname {
    max-width: 33%;
  }
}
#survey-pre-form .npx-full-form-link a {
  font-weight: bold;
  color: #ffab1a;
}
#survey-pre-form h2 {
  font-weight: 700;
}
#survey-pre-form .npx-program-button {
  margin-bottom: 2rem;
}

#edit-training-id--wrapper .js-form-type-radio label {
  font-size: 1rem;
  font-weight: 700;
}
#edit-training-id--wrapper legend {
  font-size: 1.125rem;
}

#edit-user-opinion {
  width: 100%;
}

textarea, input {
  max-width: 100%;
}

@media (min-width: 75rem) {
  #edit-opinion .radio:not(.form-item-promotion) {
    top: -0.5rem;
    position: relative;
  }
}
#edit-opinion small {
  max-width: 400px;
}
#edit-opinion .js-form-type-radio {
  text-transform: lowercase;
}
#edit-opinion .js-form-type-radio:first-of-type {
  margin-right: 1rem;
}
#edit-opinion .js-form-item {
  margin-top: 0;
}
#edit-opinion legend {
  font-size: 1rem;
  margin-bottom: 0;
  font-weight: 800;
}
#edit-opinion strong {
  font-weight: 800;
}
#edit-opinion .details-wrapper > p:first-of-type strong {
  text-decoration: underline;
}
#edit-opinion .opinion-thanks {
  font-weight: 800;
}

.survey-form, .closed-survey-form {
  position: relative;
}
.survey-form select.error, .survey-form fieldset.error .fieldset-legend, .closed-survey-form select.error, .closed-survey-form fieldset.error .fieldset-legend {
  border: solid 2px red;
  border-radius: 5px;
}

.survey-form label, .survey-form .label, .survey-form legend, .closed-survey-form label, .survey-form .npx-survey-label, .closed-survey-form .npx-survey-label {
  font-size: 1rem;
  font-weight: bold;
  margin: 0.625rem 0;
  display: block;
}

.survey-form .form-type-email label, .closed-survey-form .form-type-email label, .survey-form .promotion-opinion .fieldset-legend, .closed-survey-form .promotion-opinion .fieldset-legend {
  font-weight: 300;
}

.survey-form .form-item:not(.form-type-radio), .closed-survey-form .form-item:not(.form-type-radio) {
  margin-bottom: 2rem;
}

.survey-form input.form-submit, .closed-survey-form input.form-submit {
  margin-left: 0;
}

.survey-form .hide-submit, .closed-survey-form .hide-submit {
  display: none !important;
}

.survey-form .main-survey-submit, .closed-survey-form .main-survey-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #000;
  background: #fecc09;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  margin-bottom: 1.875rem;
}

.survey-form .main-survey-submit:hover, .closed-survey-form .main-survey-submit:hover {
  background-color: #ffab1a;
}

.survey-form .survey-or, .closed-survey-form .survey-or {
  position: relative;
  display: inline;
  left: 3px;
}

@media (max-width: 47.9375rem) {
  .survey-form .survey-or, .closed-survey-form .survey-or {
    left: 0;
    margin-top: 0;
    bottom: 0;
  }
}
.survey-form [data-drupal-selector=edit-first-submit], .closed-survey-form [data-drupal-selector=edit-first-submit] {
  position: absolute;
  left: 326px;
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #000;
  background: #fecc09;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  margin-bottom: 1.875rem;
  height: auto;
}

@media (max-width: 47.9375rem) {
  .survey-form [data-drupal-selector=edit-first-submit], .closed-survey-form [data-drupal-selector=edit-first-submit] {
    left: 0;
    position: relative;
    bottom: -5px;
    margin-bottom: 0;
  }
}
.survey-form [data-drupal-selector=edit-first-submit]:hover, .closed-survey-form [data-drupal-selector=edit-first-submit]:hover {
  background-color: #ffab1a;
}

.survey-form details[id*=edit-opinion], .closed-survey-form details[id*=edit-opinion] {
  border: 0;
  display: inline-block;
  max-width: 100%;
}

.survey-form details[id*=edit-opinion] summary, .closed-survey-form details[id*=edit-opinion] summary {
  font-weight: bold;
  color: #fff;
  background-color: #06c;
  width: 283px;
  padding: 0.75rem 1.875rem;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 1.25rem;
  list-style-type: none;
}

@media (max-width: 47.9375rem) {
  .survey-form details[id*=edit-opinion] summary, .closed-survey-form details[id*=edit-opinion] summary {
    margin-bottom: 0.5rem;
    display: block;
  }
}
.survey-form details[id*=edit-opinion] .details-wrapper, .closed-survey-form details[id*=edit-opinion] .details-wrapper {
  border: 1px solid #ccc;
  padding: 0 2rem 2rem;
}

.survey-form details[id*=edit-opinion] .fieldgroup, .closed-survey-form details[id*=edit-opinion] .fieldgroup {
  position: relative;
}

.survey-form details[id*=edit-opinion] .auth-opinion legend, .closed-survey-form details[id*=edit-opinion] .auth-opinion legend {
  width: auto;
  display: block;
}

.survey-form details[id*=edit-opinion] .fieldset-wrapper > div:first-child, .closed-survey-form details[id*=edit-opinion] .fieldset-wrapper > div:first-child {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.survey-form details[id*=edit-opinion] .fieldset-wrapper > div:first-child .form-item:first-child, .closed-survey-form details[id*=edit-opinion] .fieldset-wrapper > div:first-child .form-item:first-child {
  margin-right: 0.625rem;
}

.survey-form details[id*=edit-opinion] .auth-opinion, .closed-survey-form details[id*=edit-opinion] .auth-opinion {
  margin-bottom: 0;
}

.survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper, .closed-survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper {
  width: 60%;
}

@media (min-width: 75rem) {
  .survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper, .closed-survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: row-reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
    max-width: 900px;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
        -ms-flex-pack: end;
            justify-content: flex-end;
  }
}
.survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .form-type-radio, .closed-survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .form-type-radio {
  margin-bottom: 0;
}

.survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .description, .closed-survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .description {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 50%;
      -ms-flex: 1 0 50%;
          flex: 1 0 50%;
  top: -15px;
  font-style: italic;
  left: 0;
}
@media (min-width: 75rem) {
  .survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .description, .closed-survey-form details[id*=edit-opinion] .auth-opinion .fieldset-wrapper .description {
    top: 10px;
  }
}

.survey-form details[id*=edit-opinion] .opinion-thanks, .closed-survey-form details[id*=edit-opinion] .opinion-thanks {
  text-transform: uppercase;
  text-align: center;
  margin-top: -2.5rem;
  margin-bottom: 0.9375rem;
}

.survey-form details[id*=edit-opinion] .opinion-rodo, .closed-survey-form details[id*=edit-opinion] .opinion-rodo {
  font-style: italic;
  font-size: 0.6875rem;
}

.survey-form details[id*=edit-opinion] .opinion-bonus, .closed-survey-form details[id*=edit-opinion] .opinion-bonus {
  margin-top: 2rem;
}

#edit-promotion--wrapper.mb-3 {
  margin-bottom: -1.75rem !important;
}

.opinion-details .form-type-email {
  padding-bottom: 4.5rem;
}

.closed-survey-form legend {
  font-size: 1rem;
}

.form-item-user-opinion.mb-3, .form-item-name.mb-3 {
  margin-bottom: 0 !important;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNvbW1vbi9fdmFyaWFibGVzLnNjc3MiLCJucHhzdXJ2ZXkuY3NzIiwibnB4c3VydmV5LnNjc3MiLCIuLi9ub2RlX21vZHVsZXMvYm9vdHN0cmFwL3Njc3MvbWl4aW5zL19icmVha3BvaW50cy5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXVDQSxjQUFBO0FBbUZBLGVBQUE7QUFFQSxpQkFBQTtBQXlDQTtFQUFPLG9CQUFBO0FDaEtQOztBQ0pBO0VBQ0UsZUFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBRE9GOztBQ0xBO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QURRRjs7QUNMQTtFQUNFLGlCQUFBO0FEUUY7QUUwQ0k7RURuREo7SUFHSSxpQkFBQTtFRFVGO0FBQ0Y7O0FFcUNJO0VEM0NGO0lBQ0UsY0FBQTtFRFVGO0FBQ0Y7QUNQRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtBRFNKO0FDUEU7RUFDRSxnQkFBQTtBRFNKO0FDUEU7RUFDRSxtQkFBQTtBRFNKOztBQ0xFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0FEUUo7QUNORTtFQUNFLG1CQUFBO0FEUUo7O0FDTEE7RUFDRSxXQUFBO0FEUUY7O0FDTkE7RUFDRSxlQUFBO0FEU0Y7O0FDTkU7RUFDRTtJQUNFLFlBQUE7SUFDQSxrQkFBQTtFRFNKO0FBQ0Y7QUNQRTtFQUNFLGdCQUFBO0FEU0o7QUNQRTtFQUNFLHlCQUFBO0FEU0o7QUNQRTtFQUNFLGtCQUFBO0FEU0o7QUNQRTtFQUNFLGFBQUE7QURTSjtBQ1BFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QURTSjtBQ1BFO0VBQ0UsZ0JBQUE7QURTSjtBQ1BFO0VBQ0UsMEJBQUE7QURTSjtBQ1BFO0VBQ0UsZ0JBQUE7QURTSjs7QUNOQTtFQUNFLGtCQUFBO0FEU0Y7QUNSRTtFQUNFLHFCQUFBO0VBQ0Esa0JBQUE7QURVSjs7QUNQQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBRFVGOztBQ1JBO0VBQ0UsZ0JBQUE7QURXRjs7QUNUQTtFQUNFLG1CQUFBO0FEWUY7O0FDVkE7RUFDRSxjQUFBO0FEYUY7O0FDWEE7RUFDRSx3QkFBQTtBRGNGOztBQ1pBO0VBQ0UscUJBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0EseUJBQUE7RUFFQSxxQ0FBQTtFQUNBLDZCQUFBO0VBRUEsd0JBQUE7RUFDQSw2QkFBQTtFQUNBLHFCQUFBO0VBR0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FEZUY7O0FDYkE7RUFDRSx5QkFBQTtBRGdCRjs7QUNkQTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLFNBQUE7QURpQkY7O0FDZkE7RUFDRTtJQUNFLE9BQUE7SUFDQSxhQUFBO0lBQ0EsU0FBQTtFRGtCRjtBQUNGO0FDaEJBO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtFQUNBLFNBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtFQUVBLHFDQUFBO0VBQ0EsNkJBQUE7RUFFQSx3QkFBQTtFQUNBLDZCQUFBO0VBQ0EscUJBQUE7RUFHQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0FEa0JGOztBQ2hCQTtFQUNFO0lBQ0UsT0FBQTtJQUNBLGtCQUFBO0lBQ0EsWUFBQTtJQUNBLGdCQUFBO0VEbUJGO0FBQ0Y7QUNqQkE7RUFDRSx5QkFBQTtBRG1CRjs7QUNqQkE7RUFDRSxTQUFBO0VBQ0EscUJBQUE7RUFDQSxlQUFBO0FEb0JGOztBQ2xCQTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0EscUJBQUE7QURxQkY7O0FDbkJBO0VBQ0U7SUFDRSxxQkFBQTtJQUNBLGNBQUE7RURzQkY7QUFDRjtBQ3BCQTtFQUNFLHNCQUFBO0VBQ0Esb0JBQUE7QURzQkY7O0FDcEJBO0VBQ0Usa0JBQUE7QUR1QkY7O0FDckJBO0VBQ0UsV0FBQTtFQUNBLGNBQUE7QUR3QkY7O0FDdEJBO0VBQ0Usb0JBQUE7RUFBQSxxQkFBQTtFQUFBLG9CQUFBO0VBQUEsYUFBQTtBRHlCRjs7QUN2QkE7RUFDRSxzQkFBQTtBRDBCRjs7QUN4QkE7RUFDRSxnQkFBQTtBRDJCRjs7QUN6QkE7RUFDRSxVQUFBO0FENEJGOztBQzFCQTtFQUNFO0lBQ0Usb0JBQUE7SUFBQSxxQkFBQTtJQUFBLG9CQUFBO0lBQUEsYUFBQTtJQUNBLDhCQUFBO0lBQUEsOEJBQUE7SUFBQSxtQ0FBQTtRQUFBLCtCQUFBO1lBQUEsMkJBQUE7SUFDQSxnQkFBQTtJQUNBLHFCQUFBO0lBQUEsaUNBQUE7UUFBQSxrQkFBQTtZQUFBLHlCQUFBO0VENkJGO0FBQ0Y7QUMzQkE7RUFDRSxnQkFBQTtBRDZCRjs7QUMzQkE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQUEscUJBQUE7TUFBQSxpQkFBQTtVQUFBLGFBQUE7RUFDQSxVQUFBO0VBQ0Esa0JBQUE7RUFDQSxPQUFBO0FEOEJGO0FDN0JFO0VBTkY7SUFPSSxTQUFBO0VEZ0NGO0FBQ0Y7O0FDOUJBO0VBQ0UseUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0Esd0JBQUE7QURpQ0Y7O0FDL0JBO0VBQ0Usa0JBQUE7RUFDQSxvQkFBQTtBRGtDRjs7QUNoQ0E7RUFDRSxnQkFBQTtBRG1DRjs7QUNoQ0E7RUFDRSxrQ0FBQTtBRG1DRjs7QUNqQ0E7RUFDRSxzQkFBQTtBRG9DRjs7QUNsQ0E7RUFDRSxlQUFBO0FEcUNGOztBQ25DQTtFQUNFLDJCQUFBO0FEc0NGIiwiZmlsZSI6Im5weHN1cnZleS5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIkZ3JpZC1icmVha3BvaW50czogKFxyXG4gIHhzOiAwLFxyXG4gIHNtOiA1NzZweCxcclxuICBtZDogNzY4cHgsXHJcbiAgbGc6IDk5MnB4LFxyXG4gIGxnbTogMTA1MHB4LFxyXG4gIGxnYTogMTEwMXB4LFxyXG4gIHhsOiAxMjAwcHgsXHJcbiAgeGxhOiAxMzAwcHgsXHJcbiAgbGQ6IDE0MDBweCxcclxuICB4eGw6IDE2MDBweFxyXG4pICFkZWZhdWx0O1xyXG5cclxuJGNvbnRhaW5lci1tYXgtd2lkdGhzOiAoXHJcbiAgc206IDU3NXB4LFxyXG4gIG1kOiA3NjdweCxcclxuICBsZzogOTkxcHgsXHJcbiAgbGdtOiAxMDQ5cHgsXHJcbiAgbGdhOiAxMTAwcHgsXHJcbiAgeGw6IDExOTlweCxcclxuICB4bGE6IDEyOTlweCxcclxuICBsZDogMTM5OXB4LFxyXG4gIHh4bDogMTU5OXB4XHJcbikgIWRlZmF1bHQ7XHJcblxyXG4kc3BhY2VyOiAxcmVtO1xyXG5cclxuJHNwYWNlcnM6IChcclxuICAwOiAwLFxyXG4gIDE6ICgkc3BhY2VyICogLjUpLCAvLzhweFxyXG4gIDI6ICgkc3BhY2VyICogLjYyNSksIC8vMTBweFxyXG4gIDM6ICgkc3BhY2VyICogMC45Mzc1KSwgLy8xNXB4XHJcbiAgNDogJHNwYWNlciwgLy8xNnB4XHJcbiAgNTogKCRzcGFjZXIgKiAxLjI1KSwgIC8vMjBweFxyXG4gIDY6ICgkc3BhY2VyICogMS44NzUpLCAvLzMwcHhcclxuICA3OiAoJHNwYWNlciAqIDIuNSksIC8vNDBweFxyXG4gIDg6ICgkc3BhY2VyICogMylcclxuKTtcclxuXHJcbi8qIFZBUklBQkxFUyAqL1xyXG5cclxuLy8gY29sb3JzXHJcbiRwcmltYXJ5LXNoYWRlOiByZ2IoMjU1LCA3OCwgNDYpO1xyXG4kcHJpbWFyeS1saWdodDogbGlnaHRlbigkcHJpbWFyeS1zaGFkZSwgMzclKTtcclxuJHByaW1hcnktZGFyazogZGFya2VuKCRwcmltYXJ5LXNoYWRlLCAxMiUpO1xyXG4kYWNjZW50LXNoYWRlOiAjMDA3OUMwO1xyXG4kYWNjZW50LWxpZ2h0OiBsaWdodGVuKCRhY2NlbnQtc2hhZGUsIDM3JSk7XHJcbiRhY2NlbnQtZGFyazogZGFya2VuKCRhY2NlbnQtc2hhZGUsIDEyJSk7XHJcbi8vIFJlYXNzaWduIGNvbG9yIHZhcnMgdG8gc2VtYW50aWMgY29sb3Igc2NoZW1lXHJcbiRyZWQ6ICNkYzM1NDU7XHJcbiR5ZWxsb3c6ICNmZmMxMDc7XHJcbiRncmVlbjogIzI4YTc0NTtcclxuJGN5YW46ICMxN2EyYjg7XHJcbiR3aGl0ZTogI2ZmZjtcclxuJGdyYXktMTAwOiAjZjhmOWZhO1xyXG4kZ3JheS04MDA6ICMzNDNhNDA7XHJcbiRibGFjazogIzAwMDAwMDtcclxuJHRoZW1lLWNvbG9yczogKCBwcmltYXJ5OiAkd2hpdGUsIHNlY29uZGFyeTogIzAwNjZjYywgc3VjY2VzczogJGdyZWVuLCBpbmZvOiAkY3lhbiwgd2FybmluZzogJHllbGxvdywgZGFuZ2VyOiAkcmVkLCBsaWdodDogJGdyYXktMTAwLCBkYXJrOiAkZ3JheS04MDApO1xyXG5cclxuJGJyYW5kLXByaW1hcnk6ICRhY2NlbnQtc2hhZGU7XHJcbi8vJGJyYW5kLXN1Y2Nlc3M6ICRncmVlbjtcclxuLy8kYnJhbmQtaW5mbzogJHRlYWw7XHJcbi8vJGJyYW5kLXdhcm5pbmc6ICRvcmFuZ2U7XHJcbi8vJGJyYW5kLWRhbmdlcjogJHJlZDtcclxuJGJyYW5kLWludmVyc2U6ICRwcmltYXJ5LXNoYWRlO1xyXG4vLyBCb2R5XHJcbi8vXHJcbi8vIFNldHRpbmdzIGZvciB0aGUgYDxib2R5PmAgZWxlbWVudC5cclxuJGJvZHktYmc6ICR3aGl0ZTtcclxuJGJvZHktY29sb3I6ICRncmF5LTgwMDtcclxuJGludmVyc2UtYmc6ICRwcmltYXJ5LXNoYWRlO1xyXG4kaW52ZXJzZS1jb2xvcjogJHdoaXRlO1xyXG4vLyBMaW5rc1xyXG4vL1xyXG4vLyBTdHlsZSBhbmNob3IgZWxlbWVudHMuXHJcbiRsaW5rLWRlY29yYXRpb246IG5vbmU7XHJcbiRsaW5rLWhvdmVyLWNvbG9yOiAkYWNjZW50LWRhcms7XHJcbiRsaW5rLWhvdmVyLWRlY29yYXRpb246IHVuZGVybGluZTtcclxuLy8gQ29tbWVudHNcclxuJGNvbW1lbnQtc3BhY2VyLXg6IDEuMjVlbTtcclxuJGNvbW1lbnQtc3BhY2VyLXk6IDEuMjVlbTtcclxuLy8gUmVzcG9uc2l2ZSBmb250IHNpemVzXHJcbiRlbmFibGUtcmVzcG9uc2l2ZS1mb250LXNpemVzOiB0cnVlO1xyXG4kZW5hYmxlLXNoYWRvd3M6IGZhbHNlO1xyXG4kZW5hYmxlLWdyYWRpZW50czogZmFsc2U7XHJcbi8vIEJyZWFkY3J1bWJzIChXb3JrcyBvbmx5IG9uIEJvb3RzdHJhcCA1KylcclxuLy8gJGJyZWFkY3J1bWItZGl2aWRlcjogcXVvdGUoXCI+XCIpO1xyXG5cclxuJGZvbnQtZmFtaWx5OiBcIk11bGlcIiwgc2Fucy1zZXJpZjtcclxuJHJvYm90bzogJGZvbnQtZmFtaWx5O1xyXG5cclxuXHJcbiRsaXN0LWdyb3VwLWFjdGl2ZS1iZzogdHJhbnNwYXJlbnQ7XHJcbiRsaXN0LWdyb3VwLWJnOiAjZmZmO1xyXG4kdGFibGUtY2VsbC1wYWRkaW5nOiA4cHg7XHJcbiRiYXNlLWZvbnQtc2l6ZTogMTZweDtcclxuJHAtZm9udC1zaXplOiAxcmVtO1xyXG4kZm9udC13ZWlnaHQtYm9sZGVyOiA3MDA7XHJcblxyXG4kYm9yZGVyLXNlcGFyYXRvcjogMXB4IHNvbGlkICNkMGQ4ZGI7XHJcbiRib3JkZXItc2VwYXJhdG9yLWxpZ2h0OiAxcHggc29saWQgI2UzZTllOTtcclxuXHJcblxyXG4kbGluay1jb2xvcjogIzAwNTNCMztcclxuJHBhZ2luYXRpb24tYWN0aXZlLWJnOiAjMDZjO1xyXG4kcGFnaW5hdGlvbi1jb2xvcjogIzA2YztcclxuJGNvbG9yLXRlcnRpYXJ5OiAjZmVjYzA5OyAvL2J1dHRvbnNcclxuJG5weFNwb2lsZXJDb2xvckdyYXk6ICMwMzRiN2Q7XHJcblxyXG4kbnB4RnJlc2htYWlsU3VibWl0QmFja2dyb3VuZDogI2U0NTRmZjtcclxuJG5weEZyZXNobWFpbFN1Ym1pdEhvdmVyQmFja2dyb3VuZDogI2M0MzRkZjtcclxuJG5weEZyZXNobWFpbFN1Ym1pdENvbG9yOiAjZmZmO1xyXG4kbnB4RnJlc2htYWlsTXNnQ29sb3I6ICM5MDkwOTA7XHJcblxyXG4kY29sb3ItcHJpbWFyeTogIzJBN0RFMztcclxuJGNvbG9yLWJsdWUtZGFya2VyOiAjMDA1NkIzO1xyXG4kY29sb3Itb3JhbmdlLWJ1dHRvbjogI0ZGOTkwMDtcclxuJGNvbG9yLW9yYW5nZS1idXR0b24taG92ZXI6ICNGRkI1NDY7XHJcbiRjb2xvci1ib3JkZXIteWVsbG93OiAjRjJDQTEwO1xyXG4kZ3JleS0xMDAtbmV3OiAjRjVGNUY1O1xyXG5cclxuXHJcbi8qIFRZUE9HUkFQSFkgKi9cclxuXHJcbi8qIEdvb2dsZSBGb250cyAqL1xyXG5cclxuLy9AaW1wb3J0IHVybChcImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzP2ZhbWlseT1MYXRvOjQwMCwzMDAsMzAwaXRhbGljLDQwMGl0YWxpYyw3MDAsNzAwaXRhbGljLDkwMCw5MDBpdGFsaWNcIik7XHJcbi8vQGltcG9ydCB1cmwoXCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2Nzcz9mYW1pbHk9UmFsZXdheTo0MDAsMzAwLDUwMCw2MDAsNzAwLDgwMCw5MDBcIik7XHJcbi8vIGZvbnQgZmFtaWxpZXNcclxuJGZvbnQtbGF0bzogJ0xhdG8nLFxyXG5BcmlhbCxcclxuVmVyZGFuYSxcclxuc2Fucy1zZXJpZjtcclxuJGZvbnQtcmFsZXdheTogJ1JhbGV3YXknLFxyXG5BcmlhbCxcclxuVmVyZGFuYSxcclxuc2Fucy1zZXJpZjtcclxuLy8gRm9udHNcclxuLy9cclxuLy8gRm9udCwgbGluZS1oZWlnaHQsIGFuZCBjb2xvciBmb3IgYm9keSB0ZXh0LCBoZWFkaW5ncywgYW5kIG1vcmUuXHJcbiRmb250LWZhbWlseS1zYW5zLXNlcmlmOiAgICAgIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgXCJTZWdvZSBVSVwiLCBSb2JvdG8sIFwiSGVsdmV0aWNhIE5ldWVcIiwgQXJpYWwsIFwiTm90byBTYW5zXCIsIHNhbnMtc2VyaWYsIFwiQXBwbGUgQ29sb3IgRW1vamlcIiwgXCJTZWdvZSBVSSBFbW9qaVwiLCBcIlNlZ29lIFVJIFN5bWJvbFwiLCBcIk5vdG8gQ29sb3IgRW1vamlcIjtcclxuJGZvbnQtZmFtaWx5LW1vbm9zcGFjZTogICAgICAgU0ZNb25vLVJlZ3VsYXIsIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCBcIkxpYmVyYXRpb24gTW9ub1wiLCBcIkNvdXJpZXIgTmV3XCIsIG1vbm9zcGFjZTtcclxuJGZvbnQtZmFtaWx5LXNlcmlmOiBHZW9yZ2lhLFwiVGltZXMgTmV3IFJvbWFuXCIsVGltZXMsc2VyaWY7XHJcbiRmb250LWZhbWlseS1iYXNlOiAkZm9udC1mYW1pbHk7XHJcbiRmb250LXNpemUtYmFzZTogMXJlbTsgLy8gQXNzdW1lcyB0aGUgYnJvd3NlciBkZWZhdWx0LCB0eXBpY2FsbHkgYDE2cHhgXHJcbiRmb250LXNpemUtbGc6ICRmb250LXNpemUtYmFzZSAqIDEuMjU7XHJcbiRmb250LXNpemUtc206ICRmb250LXNpemUtYmFzZSAqIC44NzU7XHJcbiRmb250LXNpemUteHM6ICRmb250LXNpemUtYmFzZSAqIC43NTtcclxuJGZvbnQtd2VpZ2h0LW5vcm1hbDogbm9ybWFsO1xyXG4kZm9udC13ZWlnaHQtYm9sZDogYm9sZDtcclxuJGZvbnQtd2VpZ2h0LWJhc2U6ICRmb250LXdlaWdodC1ub3JtYWw7XHJcbiRsaW5lLWhlaWdodC1iYXNlOiAxLjU7XHJcbiRoMS1mb250LXNpemU6ICRmb250LXNpemUtYmFzZSAqIDIuNTtcclxuJGgyLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMjtcclxuJGgzLWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMS41O1xyXG4kaDQtZm9udC1zaXplOiAkZm9udC1zaXplLWJhc2UgKiAxLjI7XHJcblxyXG4kaGVhZGluZ3MtZm9udC13ZWlnaHQ6IDYwMDtcclxuJGZvbnQtd2VpZ2h0LWxpZ2h0OiA0MDA7XHJcbiRmb250LXNpemUtbWQtbmV3OiAkZm9udC1zaXplLWJhc2UgKiAxLjEyNTtcclxuXHJcblxyXG5cclxuJG1heC13aWR0aC1jb250YWluZXI6IDE0MTVweDtcclxuXHJcbjpyb290IHstLXNlY29uZGFyeTogIzAwNjZjYzt9XHJcblxyXG5cclxuLy8gTmVjZXNzYXJ5IEJTIHZhcmlhYmxlc1xyXG4kZ3JheS0yMDA6ICNlOWVjZWYgIWRlZmF1bHQ7XHJcbiRncmF5LTMwMDogI2RlZTJlNiAhZGVmYXVsdDtcclxuJGdyYXktNzAwOiAjNDk1MDU3ICFkZWZhdWx0O1xyXG4kY29tcG9uZW50LWFjdGl2ZS1jb2xvcjogI2ZmZiAhZGVmYXVsdDtcclxuJGNvbXBvbmVudC1hY3RpdmUtYmc6ICMwZDZlZmQgIWRlZmF1bHQ7XHJcbiRuYXYtbGluay1wYWRkaW5nLXk6IC41cmVtICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstcGFkZGluZy14OiAxcmVtICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstZm9udC1zaXplOiBudWxsICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstZm9udC13ZWlnaHQ6IG51bGwgIWRlZmF1bHQ7XHJcbiRuYXYtbGluay1jb2xvcjogJGxpbmstY29sb3IgIWRlZmF1bHQ7XHJcbiRuYXYtbGluay10cmFuc2l0aW9uOiBjb2xvciAuMTVzIGVhc2UtaW4tb3V0LCBiYWNrZ3JvdW5kLWNvbG9yIC4xNXMgZWFzZS1pbi1vdXQsIGJvcmRlci1jb2xvciAuMTVzIGVhc2UtaW4tb3V0ICFkZWZhdWx0O1xyXG4kZW5hYmxlLXRyYW5zaXRpb25zOiB0cnVlICFkZWZhdWx0O1xyXG4kZW5hYmxlLXJlZHVjZWQtbW90aW9uOiB0cnVlICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstaG92ZXItY29sb3I6ICRsaW5rLWhvdmVyLWNvbG9yICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstZGlzYWJsZWQtY29sb3I6ICM2Yzc1N2QgIWRlZmF1bHQ7XHJcbiRuYXYtdGFicy1ib3JkZXItd2lkdGg6IDFweCAhZGVmYXVsdDtcclxuJG5hdi10YWJzLWJvcmRlci1jb2xvcjogJGdyYXktMzAwICFkZWZhdWx0O1xyXG4kYm9yZGVyLXJhZGl1czogLjI1cmVtICFkZWZhdWx0O1xyXG4kbmF2LXRhYnMtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXMgIWRlZmF1bHQ7XHJcbiRlbmFibGUtcm91bmRlZDogdHJ1ZSAhZGVmYXVsdDtcclxuJG5hdi10YWJzLWxpbmstaG92ZXItYm9yZGVyLWNvbG9yOiAkZ3JheS0yMDAgJGdyYXktMjAwICRuYXYtdGFicy1ib3JkZXItY29sb3IgIWRlZmF1bHQ7XHJcbiRuYXYtdGFicy1saW5rLWFjdGl2ZS1jb2xvcjogJGdyYXktNzAwICFkZWZhdWx0O1xyXG4kbmF2LXRhYnMtbGluay1hY3RpdmUtYmc6ICRib2R5LWJnICFkZWZhdWx0O1xyXG4kbmF2LXRhYnMtbGluay1hY3RpdmUtYm9yZGVyLWNvbG9yOiAkZ3JheS0zMDAgJGdyYXktMzAwICRuYXYtdGFicy1saW5rLWFjdGl2ZS1iZyAhZGVmYXVsdDtcclxuJG5hdi1waWxscy1ib3JkZXItcmFkaXVzOiAkYm9yZGVyLXJhZGl1cyAhZGVmYXVsdDtcclxuJG5hdi1waWxscy1saW5rLWFjdGl2ZS1jb2xvcjogJGNvbXBvbmVudC1hY3RpdmUtY29sb3IgIWRlZmF1bHQ7XHJcbiRuYXYtcGlsbHMtbGluay1hY3RpdmUtYmc6ICRjb21wb25lbnQtYWN0aXZlLWJnICFkZWZhdWx0O1xyXG5cclxuLy8gQWRkaXRpb25hbCB2YXJpYWJsZXMgZm9yIHRpbGVzIGFuZCBjb21wb25lbnRzXHJcbiRjb2xvci1wcmltYXJ5OiAjMTgzODgxO1xyXG4kY29sb3ItYm9yZGVyLWxpZ2h0OiAjRjRGOUZGO1xyXG4kY29sb3ItYWNjZW50OiAjZjJjYTEwO1xyXG4kY29sb3ItYWNjZW50LWFsdDogI2ZmZDgxNztcclxuJGNvbG9yLWxpbms6ICMwMDU2QjM7XHJcbiRjb2xvci10ZXh0OiAjMzQzQTQwO1xyXG4kY29sb3ItYmx1ZTogIzJBN0RFMztcclxuXHJcbiRmb250LXNpemUtdGl0bGU6IDIwcHg7XHJcbiRmb250LXNpemUtY29udGVudDogMTRweDtcclxuJGZvbnQtc2l6ZS1zbWFsbDogMTJweDtcclxuXHJcbiRmb250LXdlaWdodC1zZW1pYm9sZDogNjAwO1xyXG5cclxuIiwiLyogVkFSSUFCTEVTICovXG4vKiBUWVBPR1JBUEhZICovXG4vKiBHb29nbGUgRm9udHMgKi9cbjpyb290IHtcbiAgLS1zZWNvbmRhcnk6ICMwMDY2Y2M7XG59XG5cbltpZF49ZWRpdC10cmFpbmluZy1mb3JtLXR5cGUtLXdyYXBwZXJdIGxlZ2VuZCBzcGFuIHtcbiAgZm9udC1zaXplOiAxcmVtO1xuICBtYXJnaW46IDAuNjI1cmVtIDA7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuXG4jc3VydmV5LXByZS1mb3JtIGxhYmVsLCAjc3VydmV5LXByZS1mb3JtIC5ucHgtc3VydmV5LWxhYmVsIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBmb250LXdlaWdodDogODAwO1xuICBtYXJnaW46IDEwcHggMDtcbn1cblxuI2VkaXQtdXNlci1vcGluaW9uIHtcbiAgbWluLWhlaWdodDogNy41ZW07XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcbiAgI2VkaXQtdXNlci1vcGluaW9uIHtcbiAgICBtaW4taGVpZ2h0OiA0LjZlbTtcbiAgfVxufVxuXG5AbWVkaWEgKG1pbi13aWR0aDogOTkycHgpIHtcbiAgI2VkaXQtbmFtZSwgI2VkaXQtc3VybmFtZSB7XG4gICAgbWF4LXdpZHRoOiAzMyU7XG4gIH1cbn1cbiNzdXJ2ZXktcHJlLWZvcm0gLm5weC1mdWxsLWZvcm0tbGluayBhIHtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIGNvbG9yOiAjZmZhYjFhO1xufVxuI3N1cnZleS1wcmUtZm9ybSBoMiB7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG59XG4jc3VydmV5LXByZS1mb3JtIC5ucHgtcHJvZ3JhbS1idXR0b24ge1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xufVxuXG4jZWRpdC10cmFpbmluZy1pZC0td3JhcHBlciAuanMtZm9ybS10eXBlLXJhZGlvIGxhYmVsIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBmb250LXdlaWdodDogNzAwO1xufVxuI2VkaXQtdHJhaW5pbmctaWQtLXdyYXBwZXIgbGVnZW5kIHtcbiAgZm9udC1zaXplOiAxOHB4O1xufVxuXG4jZWRpdC11c2VyLW9waW5pb24ge1xuICB3aWR0aDogMTAwJTtcbn1cblxudGV4dGFyZWEsIGlucHV0IHtcbiAgbWF4LXdpZHRoOiAxMDAlO1xufVxuXG5AbWVkaWEgKG1pbi13aWR0aDogMTIwMHB4KSB7XG4gICNlZGl0LW9waW5pb24gLnJhZGlvOm5vdCguZm9ybS1pdGVtLXByb21vdGlvbikge1xuICAgIHRvcDogLTAuNXJlbTtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIH1cbn1cbiNlZGl0LW9waW5pb24gc21hbGwge1xuICBtYXgtd2lkdGg6IDQwMHB4O1xufVxuI2VkaXQtb3BpbmlvbiAuanMtZm9ybS10eXBlLXJhZGlvIHtcbiAgdGV4dC10cmFuc2Zvcm06IGxvd2VyY2FzZTtcbn1cbiNlZGl0LW9waW5pb24gLmpzLWZvcm0tdHlwZS1yYWRpbzpmaXJzdC1vZi10eXBlIHtcbiAgbWFyZ2luLXJpZ2h0OiAxcmVtO1xufVxuI2VkaXQtb3BpbmlvbiAuanMtZm9ybS1pdGVtIHtcbiAgbWFyZ2luLXRvcDogMDtcbn1cbiNlZGl0LW9waW5pb24gbGVnZW5kIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBtYXJnaW4tYm90dG9tOiAwO1xuICBmb250LXdlaWdodDogODAwO1xufVxuI2VkaXQtb3BpbmlvbiBzdHJvbmcge1xuICBmb250LXdlaWdodDogODAwO1xufVxuI2VkaXQtb3BpbmlvbiAuZGV0YWlscy13cmFwcGVyID4gcDpmaXJzdC1vZi10eXBlIHN0cm9uZyB7XG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xufVxuI2VkaXQtb3BpbmlvbiAub3Bpbmlvbi10aGFua3Mge1xuICBmb250LXdlaWdodDogODAwO1xufVxuXG4uc3VydmV5LWZvcm0sIC5jbG9zZWQtc3VydmV5LWZvcm0ge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG59XG4uc3VydmV5LWZvcm0gc2VsZWN0LmVycm9yLCAuc3VydmV5LWZvcm0gZmllbGRzZXQuZXJyb3IgLmZpZWxkc2V0LWxlZ2VuZCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBzZWxlY3QuZXJyb3IsIC5jbG9zZWQtc3VydmV5LWZvcm0gZmllbGRzZXQuZXJyb3IgLmZpZWxkc2V0LWxlZ2VuZCB7XG4gIGJvcmRlcjogc29saWQgMnB4IHJlZDtcbiAgYm9yZGVyLXJhZGl1czogNXB4O1xufVxuXG4uc3VydmV5LWZvcm0gbGFiZWwsIC5zdXJ2ZXktZm9ybSAubGFiZWwsIC5zdXJ2ZXktZm9ybSBsZWdlbmQsIC5jbG9zZWQtc3VydmV5LWZvcm0gbGFiZWwsIC5zdXJ2ZXktZm9ybSAubnB4LXN1cnZleS1sYWJlbCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSAubnB4LXN1cnZleS1sYWJlbCB7XG4gIGZvbnQtc2l6ZTogMTZweDtcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gIG1hcmdpbjogMTBweCAwO1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLnN1cnZleS1mb3JtIC5mb3JtLXR5cGUtZW1haWwgbGFiZWwsIC5jbG9zZWQtc3VydmV5LWZvcm0gLmZvcm0tdHlwZS1lbWFpbCBsYWJlbCwgLnN1cnZleS1mb3JtIC5wcm9tb3Rpb24tb3BpbmlvbiAuZmllbGRzZXQtbGVnZW5kLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5wcm9tb3Rpb24tb3BpbmlvbiAuZmllbGRzZXQtbGVnZW5kIHtcbiAgZm9udC13ZWlnaHQ6IDMwMDtcbn1cblxuLnN1cnZleS1mb3JtIC5mb3JtLWl0ZW06bm90KC5mb3JtLXR5cGUtcmFkaW8pLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5mb3JtLWl0ZW06bm90KC5mb3JtLXR5cGUtcmFkaW8pIHtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbn1cblxuLnN1cnZleS1mb3JtIGlucHV0LmZvcm0tc3VibWl0LCAuY2xvc2VkLXN1cnZleS1mb3JtIGlucHV0LmZvcm0tc3VibWl0IHtcbiAgbWFyZ2luLWxlZnQ6IDA7XG59XG5cbi5zdXJ2ZXktZm9ybSAuaGlkZS1zdWJtaXQsIC5jbG9zZWQtc3VydmV5LWZvcm0gLmhpZGUtc3VibWl0IHtcbiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xufVxuXG4uc3VydmV5LWZvcm0gLm1haW4tc3VydmV5LXN1Ym1pdCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSAubWFpbi1zdXJ2ZXktc3VibWl0IHtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICBwYWRkaW5nOiAxMnB4IDMwcHg7XG4gIG1hcmdpbjogMTBweCBhdXRvIDA7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBmb250LXNpemU6IDE0cHg7XG4gIGxpbmUtaGVpZ2h0OiAyNHB4O1xuICBjb2xvcjogIzAwMDtcbiAgYmFja2dyb3VuZDogI2ZlY2MwOTtcbiAgYm9yZGVyOiBub25lO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIC1tb3otYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XG4gIC13ZWJraXQtYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XG4gIGJveC1zaGFkb3c6IDAgMCAwIHRyYW5zcGFyZW50O1xuICAtbW96LXRyYW5zaXRpb246IGFsbCA1MDBtcztcbiAgLW8tdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xuICAtd2Via2l0LXRyYW5zaXRpb246IGFsbCA1MDBtcztcbiAgdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcbiAgLXdlYmtpdC1ib3JkZXItcmFkaXVzOiA0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgd2hpdGUtc3BhY2U6IG5vcm1hbDtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcbn1cblxuLnN1cnZleS1mb3JtIC5tYWluLXN1cnZleS1zdWJtaXQ6aG92ZXIsIC5jbG9zZWQtc3VydmV5LWZvcm0gLm1haW4tc3VydmV5LXN1Ym1pdDpob3ZlciB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmFiMWE7XG59XG5cbi5zdXJ2ZXktZm9ybSAuc3VydmV5LW9yLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5zdXJ2ZXktb3Ige1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGRpc3BsYXk6IGlubGluZTtcbiAgbGVmdDogM3B4O1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLnN1cnZleS1mb3JtIC5zdXJ2ZXktb3IsIC5jbG9zZWQtc3VydmV5LWZvcm0gLnN1cnZleS1vciB7XG4gICAgbGVmdDogMDtcbiAgICBtYXJnaW4tdG9wOiAwO1xuICAgIGJvdHRvbTogMDtcbiAgfVxufVxuLnN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1lZGl0LWZpcnN0LXN1Ym1pdF0sIC5jbG9zZWQtc3VydmV5LWZvcm0gW2RhdGEtZHJ1cGFsLXNlbGVjdG9yPWVkaXQtZmlyc3Qtc3VibWl0XSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgbGVmdDogMzI2cHg7XG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgcGFkZGluZzogMTJweCAzMHB4O1xuICBtYXJnaW46IDA7XG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xuICBmb250LXNpemU6IDE0cHg7XG4gIGxpbmUtaGVpZ2h0OiAyNHB4O1xuICBjb2xvcjogIzAwMDtcbiAgYmFja2dyb3VuZDogI2ZlY2MwOTtcbiAgYm9yZGVyOiBub25lO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIC1tb3otYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XG4gIC13ZWJraXQtYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XG4gIGJveC1zaGFkb3c6IDAgMCAwIHRyYW5zcGFyZW50O1xuICAtbW96LXRyYW5zaXRpb246IGFsbCA1MDBtcztcbiAgLW8tdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xuICAtd2Via2l0LXRyYW5zaXRpb246IGFsbCA1MDBtcztcbiAgdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcbiAgLXdlYmtpdC1ib3JkZXItcmFkaXVzOiA0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgd2hpdGUtc3BhY2U6IG5vcm1hbDtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgaGVpZ2h0OiBhdXRvO1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLnN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1lZGl0LWZpcnN0LXN1Ym1pdF0sIC5jbG9zZWQtc3VydmV5LWZvcm0gW2RhdGEtZHJ1cGFsLXNlbGVjdG9yPWVkaXQtZmlyc3Qtc3VibWl0XSB7XG4gICAgbGVmdDogMDtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgYm90dG9tOiAtNXB4O1xuICAgIG1hcmdpbi1ib3R0b206IDA7XG4gIH1cbn1cbi5zdXJ2ZXktZm9ybSBbZGF0YS1kcnVwYWwtc2VsZWN0b3I9ZWRpdC1maXJzdC1zdWJtaXRdOmhvdmVyLCAuY2xvc2VkLXN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1lZGl0LWZpcnN0LXN1Ym1pdF06aG92ZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZhYjFhO1xufVxuXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIHtcbiAgYm9yZGVyOiAwO1xuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gIG1heC13aWR0aDogMTAwJTtcbn1cblxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gc3VtbWFyeSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIHN1bW1hcnkge1xuICBmb250LXdlaWdodDogYm9sZDtcbiAgY29sb3I6ICNmZmY7XG4gIGJhY2tncm91bmQtY29sb3I6ICMwNmM7XG4gIHdpZHRoOiAyODNweDtcbiAgcGFkZGluZzogMTJweCAzMHB4O1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgbGlzdC1zdHlsZS10eXBlOiBub25lO1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcbiAgLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gc3VtbWFyeSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIHN1bW1hcnkge1xuICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxufVxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmRldGFpbHMtd3JhcHBlciwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5kZXRhaWxzLXdyYXBwZXIge1xuICBib3JkZXI6IDFweCBzb2xpZCAjY2NjO1xuICBwYWRkaW5nOiAwIDJyZW0gMnJlbTtcbn1cblxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmZpZWxkZ3JvdXAsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuZmllbGRncm91cCB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmF1dGgtb3BpbmlvbiBsZWdlbmQsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIGxlZ2VuZCB7XG4gIHdpZHRoOiBhdXRvO1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmZpZWxkc2V0LXdyYXBwZXIgPiBkaXY6Zmlyc3QtY2hpbGQsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuZmllbGRzZXQtd3JhcHBlciA+IGRpdjpmaXJzdC1jaGlsZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG59XG5cbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5maWVsZHNldC13cmFwcGVyID4gZGl2OmZpcnN0LWNoaWxkIC5mb3JtLWl0ZW06Zmlyc3QtY2hpbGQsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuZmllbGRzZXQtd3JhcHBlciA+IGRpdjpmaXJzdC1jaGlsZCAuZm9ybS1pdGVtOmZpcnN0LWNoaWxkIHtcbiAgbWFyZ2luLXJpZ2h0OiAxMHB4O1xufVxuXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uLCAuY2xvc2VkLXN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmF1dGgtb3BpbmlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDA7XG59XG5cbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIHtcbiAgd2lkdGg6IDYwJTtcbn1cblxuQG1lZGlhIChtaW4td2lkdGg6IDEyMDBweCkge1xuICAuc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyLCAuY2xvc2VkLXN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmF1dGgtb3BpbmlvbiAuZmllbGRzZXQtd3JhcHBlciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93LXJldmVyc2U7XG4gICAgbWF4LXdpZHRoOiA5MDBweDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xuICB9XG59XG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIC5mb3JtLXR5cGUtcmFkaW8sIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIC5mb3JtLXR5cGUtcmFkaW8ge1xuICBtYXJnaW4tYm90dG9tOiAwO1xufVxuXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIC5kZXNjcmlwdGlvbiwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIgLmRlc2NyaXB0aW9uIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBmbGV4OiAxIDAgNTAlO1xuICB0b3A6IC0xNXB4O1xuICBmb250LXN0eWxlOiBpdGFsaWM7XG4gIGxlZnQ6IDA7XG59XG5AbWVkaWEgKG1pbi13aWR0aDogMTIwMHB4KSB7XG4gIC5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIgLmRlc2NyaXB0aW9uLCAuY2xvc2VkLXN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLmF1dGgtb3BpbmlvbiAuZmllbGRzZXQtd3JhcHBlciAuZGVzY3JpcHRpb24ge1xuICAgIHRvcDogMTBweDtcbiAgfVxufVxuXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAub3Bpbmlvbi10aGFua3MsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAub3Bpbmlvbi10aGFua3Mge1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIG1hcmdpbi10b3A6IC00MHB4O1xuICBtYXJnaW4tYm90dG9tOiAwLjkzNzVyZW07XG59XG5cbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5vcGluaW9uLXJvZG8sIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9ZWRpdC1vcGluaW9uXSAub3Bpbmlvbi1yb2RvIHtcbiAgZm9udC1zdHlsZTogaXRhbGljO1xuICBmb250LXNpemU6IDExcHg7XG59XG5cbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1lZGl0LW9waW5pb25dIC5vcGluaW9uLWJvbnVzLCAuY2xvc2VkLXN1cnZleS1mb3JtIGRldGFpbHNbaWQqPWVkaXQtb3Bpbmlvbl0gLm9waW5pb24tYm9udXMge1xuICBtYXJnaW4tdG9wOiAycmVtO1xufVxuXG4jZWRpdC1wcm9tb3Rpb24tLXdyYXBwZXIubWItMyB7XG4gIG1hcmdpbi1ib3R0b206IC0xLjc1cmVtICFpbXBvcnRhbnQ7XG59XG5cbi5vcGluaW9uLWRldGFpbHMgLmZvcm0tdHlwZS1lbWFpbCB7XG4gIHBhZGRpbmctYm90dG9tOiA0LjVyZW07XG59XG5cbi5jbG9zZWQtc3VydmV5LWZvcm0gbGVnZW5kIHtcbiAgZm9udC1zaXplOiAxNnB4O1xufVxuXG4uZm9ybS1pdGVtLXVzZXItb3Bpbmlvbi5tYi0zLCAuZm9ybS1pdGVtLW5hbWUubWItMyB7XG4gIG1hcmdpbi1ib3R0b206IDAgIWltcG9ydGFudDtcbn0iLCJAaW1wb3J0IFwiLi4vYm9vdHN0cmFwL3Njc3MvY29tbW9uL2Jhc2VcIjtcclxuW2lkXj1lZGl0LXRyYWluaW5nLWZvcm0tdHlwZS0td3JhcHBlcl0gbGVnZW5kIHNwYW4ge1xyXG4gIGZvbnQtc2l6ZTogMXJlbTtcclxuICBtYXJnaW46IC42MjVyZW0gMDtcclxuICBkaXNwbGF5OiBibG9jaztcclxufVxyXG4jc3VydmV5LXByZS1mb3JtIGxhYmVsLCAjc3VydmV5LXByZS1mb3JtIC5ucHgtc3VydmV5LWxhYmVsIHtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDgwMDtcclxuICBtYXJnaW46IDEwcHggMDtcclxufVxyXG5cclxuI2VkaXQtdXNlci1vcGluaW9uIHtcclxuICBtaW4taGVpZ2h0OiA3LjVlbTtcclxuICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKG1kKSB7XHJcbiAgICBtaW4taGVpZ2h0OiA0LjZlbTtcclxuICB9XHJcbn1cclxuXHJcbkBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAobGcpIHtcclxuICAjZWRpdC1uYW1lLCAjZWRpdC1zdXJuYW1lIHtcclxuICAgIG1heC13aWR0aDogMzMlO1xyXG4gIH1cclxufVxyXG4jc3VydmV5LXByZS1mb3JtIHtcclxuICAubnB4LWZ1bGwtZm9ybS1saW5rIGEge1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICBjb2xvcjogI2ZmYWIxYTtcclxuICB9XHJcbiAgaDIge1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICB9XHJcbiAgLm5weC1wcm9ncmFtLWJ1dHRvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG4gIH1cclxufVxyXG4jZWRpdC10cmFpbmluZy1pZC0td3JhcHBlciB7XHJcbiAgLmpzLWZvcm0tdHlwZS1yYWRpbyBsYWJlbCB7XHJcbiAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICBmb250LXdlaWdodDogNzAwO1xyXG4gIH1cclxuICBsZWdlbmQge1xyXG4gICAgZm9udC1zaXplOiAxOHB4O1xyXG4gIH1cclxufVxyXG4jZWRpdC11c2VyLW9waW5pb24ge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcbnRleHRhcmVhLCBpbnB1dCB7XHJcbiAgbWF4LXdpZHRoOiAxMDAlO1xyXG59XHJcbiNlZGl0LW9waW5pb24ge1xyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHtcclxuICAgIC5yYWRpbzpub3QoLmZvcm0taXRlbS1wcm9tb3Rpb24pIHtcclxuICAgICAgdG9wOiAtMC41cmVtO1xyXG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICB9XHJcbiAgfVxyXG4gIHNtYWxsIHtcclxuICAgIG1heC13aWR0aDogNDAwcHg7XHJcbiAgfVxyXG4gIC5qcy1mb3JtLXR5cGUtcmFkaW8ge1xyXG4gICAgdGV4dC10cmFuc2Zvcm06IGxvd2VyY2FzZTtcclxuICB9XHJcbiAgLmpzLWZvcm0tdHlwZS1yYWRpbzpmaXJzdC1vZi10eXBlIHtcclxuICAgIG1hcmdpbi1yaWdodDogMXJlbTtcclxuICB9XHJcbiAgLmpzLWZvcm0taXRlbSB7XHJcbiAgICBtYXJnaW4tdG9wOiAwO1xyXG4gIH1cclxuICBsZWdlbmQge1xyXG4gICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgIGZvbnQtd2VpZ2h0OiA4MDA7XHJcbiAgfVxyXG4gIHN0cm9uZyB7XHJcbiAgICBmb250LXdlaWdodDogODAwO1xyXG4gIH1cclxuICAuZGV0YWlscy13cmFwcGVyID4gcDpmaXJzdC1vZi10eXBlIHN0cm9uZyB7XHJcbiAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICB9XHJcbiAgLm9waW5pb24tdGhhbmtzIHtcclxuICAgIGZvbnQtd2VpZ2h0OiA4MDA7XHJcbiAgfVxyXG59XHJcbi5zdXJ2ZXktZm9ybSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHNlbGVjdC5lcnJvciwgZmllbGRzZXQuZXJyb3IgLmZpZWxkc2V0LWxlZ2VuZCB7XHJcbiAgICBib3JkZXI6IHNvbGlkIDJweCByZWQ7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1cHg7XHJcbiAgfVxyXG59XHJcbi5zdXJ2ZXktZm9ybSBsYWJlbCwgLnN1cnZleS1mb3JtIC5sYWJlbCwgLnN1cnZleS1mb3JtIGxlZ2VuZCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBsYWJlbCwgLnN1cnZleS1mb3JtIC5ucHgtc3VydmV5LWxhYmVsLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5ucHgtc3VydmV5LWxhYmVsIHtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgbWFyZ2luOiAxMHB4IDA7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuLnN1cnZleS1mb3JtIC5mb3JtLXR5cGUtZW1haWwgbGFiZWwsIC5jbG9zZWQtc3VydmV5LWZvcm0gLmZvcm0tdHlwZS1lbWFpbCBsYWJlbCwgLnN1cnZleS1mb3JtIC5wcm9tb3Rpb24tb3BpbmlvbiAuZmllbGRzZXQtbGVnZW5kLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5wcm9tb3Rpb24tb3BpbmlvbiAuZmllbGRzZXQtbGVnZW5kIHtcclxuICBmb250LXdlaWdodDogMzAwO1xyXG59XHJcbi5zdXJ2ZXktZm9ybSAuZm9ybS1pdGVtOm5vdCguZm9ybS10eXBlLXJhZGlvKSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSAuZm9ybS1pdGVtOm5vdCguZm9ybS10eXBlLXJhZGlvKSB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcclxufVxyXG4uc3VydmV5LWZvcm0gaW5wdXQuZm9ybS1zdWJtaXQsIC5jbG9zZWQtc3VydmV5LWZvcm0gaW5wdXQuZm9ybS1zdWJtaXQge1xyXG4gIG1hcmdpbi1sZWZ0OiAwO1xyXG59XHJcbi5zdXJ2ZXktZm9ybSAuaGlkZS1zdWJtaXQsIC5jbG9zZWQtc3VydmV5LWZvcm0gLmhpZGUtc3VibWl0IHtcclxuICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuLnN1cnZleS1mb3JtIC5tYWluLXN1cnZleS1zdWJtaXQsIC5jbG9zZWQtc3VydmV5LWZvcm0gLm1haW4tc3VydmV5LXN1Ym1pdCB7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHBhZGRpbmc6IDEycHggMzBweDtcclxuICBtYXJnaW46IDEwcHggYXV0byAwO1xyXG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBsaW5lLWhlaWdodDogMjRweDtcclxuICBjb2xvcjogIzAwMDtcclxuICBiYWNrZ3JvdW5kOiAjZmVjYzA5O1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAtbW96LWJveC1zaGFkb3c6IDAgMCAwIHRyYW5zcGFyZW50O1xyXG4gIC13ZWJraXQtYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XHJcbiAgYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XHJcbiAgLW1vei10cmFuc2l0aW9uOiBhbGwgNTAwbXM7XHJcbiAgLW8tdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xyXG4gIC13ZWJraXQtdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xyXG4gIHRyYW5zaXRpb246IGFsbCA1MDBtcztcclxuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICAtd2Via2l0LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgd2hpdGUtc3BhY2U6IG5vcm1hbDtcclxuICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG59XHJcbi5zdXJ2ZXktZm9ybSAubWFpbi1zdXJ2ZXktc3VibWl0OmhvdmVyLCAuY2xvc2VkLXN1cnZleS1mb3JtIC5tYWluLXN1cnZleS1zdWJtaXQ6aG92ZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmFiMWE7XHJcbn1cclxuLnN1cnZleS1mb3JtIC5zdXJ2ZXktb3IsIC5jbG9zZWQtc3VydmV5LWZvcm0gLnN1cnZleS1vciB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGRpc3BsYXk6IGlubGluZTtcclxuICBsZWZ0OiAzcHg7XHJcbn1cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XHJcbiAgLnN1cnZleS1mb3JtIC5zdXJ2ZXktb3IsIC5jbG9zZWQtc3VydmV5LWZvcm0gLnN1cnZleS1vciB7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgbWFyZ2luLXRvcDogMDtcclxuICAgIGJvdHRvbTogMDtcclxuICB9XHJcbn1cclxuLnN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1cImVkaXQtZmlyc3Qtc3VibWl0XCJdLCAuY2xvc2VkLXN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1cImVkaXQtZmlyc3Qtc3VibWl0XCJdIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgbGVmdDogMzI2cHg7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHBhZGRpbmc6IDEycHggMzBweDtcclxuICBtYXJnaW46IDA7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGxpbmUtaGVpZ2h0OiAyNHB4O1xyXG4gIGNvbG9yOiAjMDAwO1xyXG4gIGJhY2tncm91bmQ6ICNmZWNjMDk7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gIC1tb3otYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XHJcbiAgLXdlYmtpdC1ib3gtc2hhZG93OiAwIDAgMCB0cmFuc3BhcmVudDtcclxuICBib3gtc2hhZG93OiAwIDAgMCB0cmFuc3BhcmVudDtcclxuICAtbW96LXRyYW5zaXRpb246IGFsbCA1MDBtcztcclxuICAtby10cmFuc2l0aW9uOiBhbGwgNTAwbXM7XHJcbiAgLXdlYmtpdC10cmFuc2l0aW9uOiBhbGwgNTAwbXM7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xyXG4gIC1tb3otYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIC13ZWJraXQtYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB3aGl0ZS1zcGFjZTogbm9ybWFsO1xyXG4gIG1hcmdpbi1ib3R0b206IDMwcHg7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG59XHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xyXG4gIC5zdXJ2ZXktZm9ybSBbZGF0YS1kcnVwYWwtc2VsZWN0b3I9XCJlZGl0LWZpcnN0LXN1Ym1pdFwiXSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBbZGF0YS1kcnVwYWwtc2VsZWN0b3I9XCJlZGl0LWZpcnN0LXN1Ym1pdFwiXSB7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgYm90dG9tOiAtNXB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICB9XHJcbn1cclxuLnN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1cImVkaXQtZmlyc3Qtc3VibWl0XCJdOmhvdmVyLCAuY2xvc2VkLXN1cnZleS1mb3JtIFtkYXRhLWRydXBhbC1zZWxlY3Rvcj1cImVkaXQtZmlyc3Qtc3VibWl0XCJdOmhvdmVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZhYjFhO1xyXG59XHJcbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSB7XHJcbiAgYm9yZGVyOiAwO1xyXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICBtYXgtd2lkdGg6IDEwMCU7XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIHN1bW1hcnksIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gc3VtbWFyeSB7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzA2YztcclxuICB3aWR0aDogMjgzcHg7XHJcbiAgcGFkZGluZzogMTJweCAzMHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICBsaXN0LXN0eWxlLXR5cGU6IG5vbmU7XHJcbn1cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XHJcbiAgLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIHN1bW1hcnksIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gc3VtbWFyeSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICB9XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5kZXRhaWxzLXdyYXBwZXIsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmRldGFpbHMtd3JhcHBlciB7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2NjYztcclxuICBwYWRkaW5nOiAwIDJyZW0gMnJlbTtcclxufVxyXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmZpZWxkZ3JvdXAsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmZpZWxkZ3JvdXAge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxufVxyXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmF1dGgtb3BpbmlvbiBsZWdlbmQsIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmF1dGgtb3BpbmlvbiBsZWdlbmQge1xyXG4gIHdpZHRoOiBhdXRvO1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG59XHJcbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuZmllbGRzZXQtd3JhcHBlciA+IGRpdjpmaXJzdC1jaGlsZCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuZmllbGRzZXQtd3JhcHBlciA+IGRpdjpmaXJzdC1jaGlsZCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxufVxyXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmZpZWxkc2V0LXdyYXBwZXIgPiBkaXY6Zmlyc3QtY2hpbGQgLmZvcm0taXRlbTpmaXJzdC1jaGlsZCwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuZmllbGRzZXQtd3JhcHBlciA+IGRpdjpmaXJzdC1jaGlsZCAuZm9ybS1pdGVtOmZpcnN0LWNoaWxkIHtcclxuICBtYXJnaW4tcmlnaHQ6IDEwcHg7XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5hdXRoLW9waW5pb24sIC5jbG9zZWQtc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmF1dGgtb3BpbmlvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMDtcclxufVxyXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmF1dGgtb3BpbmlvbiAuZmllbGRzZXQtd3JhcHBlciwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIHtcclxuICB3aWR0aDogNjAlO1xyXG59XHJcbkBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHtcclxuICAuc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLmF1dGgtb3BpbmlvbiAuZmllbGRzZXQtd3JhcHBlciwgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93LXJldmVyc2U7XHJcbiAgICBtYXgtd2lkdGg6IDkwMHB4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcclxuICB9XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIgLmZvcm0tdHlwZS1yYWRpbywgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAuYXV0aC1vcGluaW9uIC5maWVsZHNldC13cmFwcGVyIC5mb3JtLXR5cGUtcmFkaW8ge1xyXG4gIG1hcmdpbi1ib3R0b206IDA7XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIgLmRlc2NyaXB0aW9uLCAuY2xvc2VkLXN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5hdXRoLW9waW5pb24gLmZpZWxkc2V0LXdyYXBwZXIgLmRlc2NyaXB0aW9uIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgZmxleDogMSAwIDUwJTtcclxuICB0b3A6IC0xNXB4O1xyXG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICBsZWZ0OiAwO1xyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHtcclxuICAgIHRvcDogMTBweDtcclxuICB9XHJcbn1cclxuLnN1cnZleS1mb3JtIGRldGFpbHNbaWQqPVwiZWRpdC1vcGluaW9uXCJdIC5vcGluaW9uLXRoYW5rcywgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAub3Bpbmlvbi10aGFua3Mge1xyXG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIG1hcmdpbi10b3A6IC00MHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDAuOTM3NXJlbTtcclxufVxyXG4uc3VydmV5LWZvcm0gZGV0YWlsc1tpZCo9XCJlZGl0LW9waW5pb25cIl0gLm9waW5pb24tcm9kbywgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAub3Bpbmlvbi1yb2RvIHtcclxuICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgZm9udC1zaXplOiAxMXB4O1xyXG59XHJcbi5zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAub3Bpbmlvbi1ib251cywgLmNsb3NlZC1zdXJ2ZXktZm9ybSBkZXRhaWxzW2lkKj1cImVkaXQtb3BpbmlvblwiXSAub3Bpbmlvbi1ib251cyB7XHJcbiAgbWFyZ2luLXRvcDogMnJlbTtcclxufVxyXG5cclxuI2VkaXQtcHJvbW90aW9uLS13cmFwcGVyLm1iLTMge1xyXG4gIG1hcmdpbi1ib3R0b206IC0xLjc1cmVtIWltcG9ydGFudDtcclxufVxyXG4ub3Bpbmlvbi1kZXRhaWxzIC5mb3JtLXR5cGUtZW1haWwge1xyXG4gIHBhZGRpbmctYm90dG9tOiA0LjVyZW07XHJcbn1cclxuLmNsb3NlZC1zdXJ2ZXktZm9ybSBsZWdlbmQge1xyXG4gIGZvbnQtc2l6ZTogMTZweDtcclxufVxyXG4uZm9ybS1pdGVtLXVzZXItb3Bpbmlvbi5tYi0zLCAuZm9ybS1pdGVtLW5hbWUubWItMyB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMCFpbXBvcnRhbnQ7XHJcbn1cclxuIiwiLy8gQnJlYWtwb2ludCB2aWV3cG9ydCBzaXplcyBhbmQgbWVkaWEgcXVlcmllcy5cbi8vXG4vLyBCcmVha3BvaW50cyBhcmUgZGVmaW5lZCBhcyBhIG1hcCBvZiAobmFtZTogbWluaW11bSB3aWR0aCksIG9yZGVyIGZyb20gc21hbGwgdG8gbGFyZ2U6XG4vL1xuLy8gICAgKHhzOiAwLCBzbTogNTc2cHgsIG1kOiA3NjhweCwgbGc6IDk5MnB4LCB4bDogMTIwMHB4KVxuLy9cbi8vIFRoZSBtYXAgZGVmaW5lZCBpbiB0aGUgYCRncmlkLWJyZWFrcG9pbnRzYCBnbG9iYWwgdmFyaWFibGUgaXMgdXNlZCBhcyB0aGUgYCRicmVha3BvaW50c2AgYXJndW1lbnQgYnkgZGVmYXVsdC5cblxuLy8gTmFtZSBvZiB0aGUgbmV4dCBicmVha3BvaW50LCBvciBudWxsIGZvciB0aGUgbGFzdCBicmVha3BvaW50LlxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbmV4dChzbSlcbi8vICAgIG1kXG4vLyAgICA+PiBicmVha3BvaW50LW5leHQoc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBtZFxuLy8gICAgPj4gYnJlYWtwb2ludC1uZXh0KHNtLCAkYnJlYWtwb2ludC1uYW1lczogKHhzIHNtIG1kIGxnIHhsKSlcbi8vICAgIG1kXG5AZnVuY3Rpb24gYnJlYWtwb2ludC1uZXh0KCRuYW1lLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzLCAkYnJlYWtwb2ludC1uYW1lczogbWFwLWtleXMoJGJyZWFrcG9pbnRzKSkge1xuICAkbjogaW5kZXgoJGJyZWFrcG9pbnQtbmFtZXMsICRuYW1lKTtcbiAgQGlmIG5vdCAkbiB7XG4gICAgQGVycm9yIFwiYnJlYWtwb2ludCBgI3skbmFtZX1gIG5vdCBmb3VuZCBpbiBgI3skYnJlYWtwb2ludHN9YFwiO1xuICB9XG4gIEByZXR1cm4gaWYoJG4gPCBsZW5ndGgoJGJyZWFrcG9pbnQtbmFtZXMpLCBudGgoJGJyZWFrcG9pbnQtbmFtZXMsICRuICsgMSksIG51bGwpO1xufVxuXG4vLyBNaW5pbXVtIGJyZWFrcG9pbnQgd2lkdGguIE51bGwgZm9yIHRoZSBzbWFsbGVzdCAoZmlyc3QpIGJyZWFrcG9pbnQuXG4vL1xuLy8gICAgPj4gYnJlYWtwb2ludC1taW4oc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICA1NzZweFxuQGZ1bmN0aW9uIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IG1hcC1nZXQoJGJyZWFrcG9pbnRzLCAkbmFtZSk7XG4gIEByZXR1cm4gaWYoJG1pbiAhPSAwLCAkbWluLCBudWxsKTtcbn1cblxuLy8gTWF4aW11bSBicmVha3BvaW50IHdpZHRoLlxuLy8gVGhlIG1heGltdW0gdmFsdWUgaXMgcmVkdWNlZCBieSAwLjAycHggdG8gd29yayBhcm91bmQgdGhlIGxpbWl0YXRpb25zIG9mXG4vLyBgbWluLWAgYW5kIGBtYXgtYCBwcmVmaXhlcyBhbmQgdmlld3BvcnRzIHdpdGggZnJhY3Rpb25hbCB3aWR0aHMuXG4vLyBTZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL21lZGlhcXVlcmllcy00LyNtcS1taW4tbWF4XG4vLyBVc2VzIDAuMDJweCByYXRoZXIgdGhhbiAwLjAxcHggdG8gd29yayBhcm91bmQgYSBjdXJyZW50IHJvdW5kaW5nIGJ1ZyBpbiBTYWZhcmkuXG4vLyBTZWUgaHR0cHM6Ly9idWdzLndlYmtpdC5vcmcvc2hvd19idWcuY2dpP2lkPTE3ODI2MVxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbWF4KG1kLCAoeHM6IDAsIHNtOiA1NzZweCwgbWQ6IDc2OHB4LCBsZzogOTkycHgsIHhsOiAxMjAwcHgpKVxuLy8gICAgNzY3Ljk4cHhcbkBmdW5jdGlvbiBicmVha3BvaW50LW1heCgkbmFtZSwgJGJyZWFrcG9pbnRzOiAkZ3JpZC1icmVha3BvaW50cykge1xuICAkbWF4OiBtYXAtZ2V0KCRicmVha3BvaW50cywgJG5hbWUpO1xuICBAcmV0dXJuIGlmKCRtYXggYW5kICRtYXggPiAwLCAkbWF4IC0gLjAyLCBudWxsKTtcbn1cblxuLy8gUmV0dXJucyBhIGJsYW5rIHN0cmluZyBpZiBzbWFsbGVzdCBicmVha3BvaW50LCBvdGhlcndpc2UgcmV0dXJucyB0aGUgbmFtZSB3aXRoIGEgZGFzaCBpbiBmcm9udC5cbi8vIFVzZWZ1bCBmb3IgbWFraW5nIHJlc3BvbnNpdmUgdXRpbGl0aWVzLlxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtaW5maXgoeHMsICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBcIlwiICAoUmV0dXJucyBhIGJsYW5rIHN0cmluZylcbi8vICAgID4+IGJyZWFrcG9pbnQtaW5maXgoc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBcIi1zbVwiXG5AZnVuY3Rpb24gYnJlYWtwb2ludC1pbmZpeCgkbmFtZSwgJGJyZWFrcG9pbnRzOiAkZ3JpZC1icmVha3BvaW50cykge1xuICBAcmV0dXJuIGlmKGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpID09IG51bGwsIFwiXCIsIFwiLSN7JG5hbWV9XCIpO1xufVxuXG4vLyBNZWRpYSBvZiBhdCBsZWFzdCB0aGUgbWluaW11bSBicmVha3BvaW50IHdpZHRoLiBObyBxdWVyeSBmb3IgdGhlIHNtYWxsZXN0IGJyZWFrcG9pbnQuXG4vLyBNYWtlcyB0aGUgQGNvbnRlbnQgYXBwbHkgdG8gdGhlIGdpdmVuIGJyZWFrcG9pbnQgYW5kIHdpZGVyLlxuQG1peGluIG1lZGlhLWJyZWFrcG9pbnQtdXAoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogYnJlYWtwb2ludC1taW4oJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWluIHtcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogJG1pbikge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSBvZiBhdCBtb3N0IHRoZSBtYXhpbXVtIGJyZWFrcG9pbnQgd2lkdGguIE5vIHF1ZXJ5IGZvciB0aGUgbGFyZ2VzdCBicmVha3BvaW50LlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50IGFuZCBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LWRvd24oJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWF4IHtcbiAgICBAbWVkaWEgKG1heC13aWR0aDogJG1heCkge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSB0aGF0IHNwYW5zIG11bHRpcGxlIGJyZWFrcG9pbnQgd2lkdGhzLlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IGJldHdlZW4gdGhlIG1pbiBhbmQgbWF4IGJyZWFrcG9pbnRzXG5AbWl4aW4gbWVkaWEtYnJlYWtwb2ludC1iZXR3ZWVuKCRsb3dlciwgJHVwcGVyLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IGJyZWFrcG9pbnQtbWluKCRsb3dlciwgJGJyZWFrcG9pbnRzKTtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJHVwcGVyLCAkYnJlYWtwb2ludHMpO1xuXG4gIEBpZiAkbWluICE9IG51bGwgYW5kICRtYXggIT0gbnVsbCB7XG4gICAgQG1lZGlhIChtaW4td2lkdGg6ICRtaW4pIGFuZCAobWF4LXdpZHRoOiAkbWF4KSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1heCA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKCRsb3dlciwgJGJyZWFrcG9pbnRzKSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1pbiA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LWRvd24oJHVwcGVyLCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfVxufVxuXG4vLyBNZWRpYSBiZXR3ZWVuIHRoZSBicmVha3BvaW50J3MgbWluaW11bSBhbmQgbWF4aW11bSB3aWR0aHMuXG4vLyBObyBtaW5pbXVtIGZvciB0aGUgc21hbGxlc3QgYnJlYWtwb2ludCwgYW5kIG5vIG1heGltdW0gZm9yIHRoZSBsYXJnZXN0IG9uZS5cbi8vIE1ha2VzIHRoZSBAY29udGVudCBhcHBseSBvbmx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50LCBub3Qgdmlld3BvcnRzIGFueSB3aWRlciBvciBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LW9ubHkoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbmV4dDogYnJlYWtwb2ludC1uZXh0KCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbWF4OiAgYnJlYWtwb2ludC1tYXgoJG5leHQpO1xuXG4gIEBpZiAkbWluICE9IG51bGwgYW5kICRtYXggIT0gbnVsbCB7XG4gICAgQG1lZGlhIChtaW4td2lkdGg6ICRtaW4pIGFuZCAobWF4LXdpZHRoOiAkbWF4KSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1heCA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKCRuYW1lLCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfSBAZWxzZSBpZiAkbWluID09IG51bGwge1xuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtZG93bigkbmV4dCwgJGJyZWFrcG9pbnRzKSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH1cbn1cbiJdfQ== */
