@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

body:not(.inner-menu-sticky) .inner-menu-full-wrapper {
  border-bottom: 1px solid #ebedec;
}
body:not(.inner-menu-sticky) .inner-menu-mobile-toggle:not(.inner-menu-expanded) {
  border-bottom: 1px solid #ebedec;
}
@media (min-width: 62rem) {
  body:not(.inner-menu-sticky) .inner-menu-mobile-toggle:not(.inner-menu-expanded) {
    border-bottom: none;
  }
}
body:not(.inner-menu-sticky) #inner-menu-ul.innermenu-expanded {
  border-bottom: 1px solid #ebedec;
}
body:not(.inner-menu-sticky) #inner-menu-table {
  border-top: 1px solid #ebedec;
}

.inner-menu-full-wrapper {
  text-align: center;
  background: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 62rem) {
  .inner-menu-full-wrapper {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

#inner-menu-table {
  width: 100%;
  background: #fff;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  width: 100vw !important;
}
@media (min-width: 62rem) {
  #inner-menu-table {
    text-align: center;
  }
}
#inner-menu-table .inner-menu-title {
  text-transform: uppercase;
  display: inline-block;
  color: #0053B3;
  margin-right: 0.25rem;
}
@media (min-width: 62rem) {
  #inner-menu-table .inner-menu-title {
    margin-left: 1rem;
    margin-right: 0.5rem;
  }
}
#inner-menu-table .inner-menu-title span.h4 {
  margin: 2.25rem 0;
}
@media (min-width: 62rem) {
  #inner-menu-table .inner-menu-title span.h4 {
    color: #000;
  }
}
#inner-menu-table .inner-menu-title span.h4 .hide-on-mobile {
  display: none;
}
@media (min-width: 62rem) {
  #inner-menu-table .inner-menu-title span.h4 .hide-on-mobile {
    display: inline-block;
  }
}
#inner-menu-table span.h4 {
  font-size: 1.2rem;
  display: inline-block;
}
@media (min-width: 62rem) {
  #inner-menu-table span.h4 {
    font-size: 0.68rem;
  }
}
@media (min-width: 63.75rem) {
  #inner-menu-table span.h4 {
    font-size: 0.7rem;
  }
}
@media (min-width: 70rem) {
  #inner-menu-table span.h4 {
    font-size: 0.79rem;
  }
}
@media (min-width: 76.25rem) {
  #inner-menu-table span.h4 {
    font-size: 0.87rem;
  }
}
@media (min-width: 81.25rem) {
  #inner-menu-table span.h4 {
    font-size: 0.84rem;
  }
}
@media (min-width: 93.625rem) {
  #inner-menu-table span.h4 {
    font-size: 1rem;
  }
}
@media (min-width: 100rem) {
  #inner-menu-table span.h4 {
    font-size: 1.08rem;
  }
}
#inner-menu-table .sf-accordion-toggle {
  display: inline-block;
}
#inner-menu-table .inner-menu-mobile-toggle {
  text-align: center;
}
@media (min-width: 62rem) {
  #inner-menu-table .inner-menu-mobile-toggle {
    display: inline-block;
  }
}
#inner-menu-table .inner-menu-mobile-toggle a#npxtraining-innermenu-toggle {
  position: relative;
  top: -7px;
}
@media (min-width: 62rem) {
  #inner-menu-table .inner-menu-mobile-toggle a#npxtraining-innermenu-toggle {
    display: none;
  }
}
#inner-menu-table .inner-menu-mobile-toggle a#npxtraining-innermenu-toggle span {
  display: block;
  text-indent: -9900px;
  text-align: left;
  background: transparent url("/themes/custom/bootstrap4grow/images/hamburger.png") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
  width: 35px;
  height: 35px;
}
#inner-menu-table .inner-menu-mobile-toggle a#npxtraining-innermenu-toggle.innermenu-expanded-hamburger span {
  background-image: url("/themes/custom/bootstrap4grow/images/hamburgerON.png");
}
#inner-menu-table ul.inner-menu-full.innermenu-expanded {
  display: block;
  text-align: center;
  padding: 0.1rem 0;
  border-top: 1px solid #ebedec;
}
@media (min-width: 62rem) {
  #inner-menu-table ul.inner-menu-full.innermenu-expanded {
    display: inline-block;
    border-top: none;
  }
}
#inner-menu-table ul.inner-menu-full {
  list-style: none;
  display: none;
  padding-left: 0.25rem;
}
@media (min-width: 62rem) {
  #inner-menu-table ul.inner-menu-full {
    display: inline-block;
  }
}
#inner-menu-table ul.inner-menu-full li {
  display: block;
  padding: 0 0.25rem;
  margin-bottom: 0;
}
@media (min-width: 62rem) {
  #inner-menu-table ul.inner-menu-full li {
    display: inline-block;
  }
}
@media (min-width: 81.25rem) {
  #inner-menu-table ul.inner-menu-full li {
    padding: 0 0.625rem;
  }
}
#inner-menu-table ul {
  margin-bottom: 0;
  margin-left: 0;
}
#inner-menu-table ul a {
  color: inherit;
}
#inner-menu-table ul a:hover {
  text-decoration: none;
  color: #0053B3;
}
#inner-menu-table ul li.active a {
  color: #0053B3;
}
#inner-menu-table ul span.h4 {
  text-transform: uppercase;
  -webkit-transition: margin-top 0.15s linear, margin-bottom 0.15s linear;
  -o-transition: margin-top 0.15s linear, margin-bottom 0.15s linear;
  transition: margin-top 0.15s linear, margin-bottom 0.15s linear;
  text-align: center;
  margin: 1rem 0;
}
@media (min-width: 62rem) {
  #inner-menu-table ul span.h4 {
    text-align: left;
  }
}

.inner-menu-sticky #inner-menu-table ul span.h4, .inner-menu-sticky #inner-menu-table .inner-menu-title span.h4 {
  margin: 0.7rem 0;
}
.inner-menu-sticky #block-npxfloatingbeltblock {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #ebedec;
}

body.inner-menu-sticky.main-menu-block-visible #block-npxfloatingbeltblock {
  border-bottom: none;
}
body.inner-menu-sticky.main-menu-block-visible #header {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #ebedec;
}
body.inner-menu-sticky.main-menu-block-visible .headerwrapper.grow3 {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

#inner-menu-table-sticky-wrapper.is-sticky #inner-menu-table {
  -webkit-box-shadow: 0px 0px 40px #ebedec;
  box-shadow: 0px 0px 40px #ebedec;
}

@media (min-width: 62rem) {
  .inner-menu-full-wrapper {
    height: 93px;
    line-height: 5.8125rem;
  }
}

#szkolenie-grupa-1 {
  background: #154DB2;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
}
#szkolenie-grupa-1 .field__label {
  display: none;
}
#szkolenie-grupa-1 a.npx-form-button-inline.npx-autolink {
  padding: 0;
  border: 0;
  background: transparent;
  color: #fecc09;
  font-weight: bold;
  text-transform: none;
  margin-top: 0.75rem;
  display: inline-block;
  font-size: inherit;
}
#szkolenie-grupa-1 .field--name-field-ts-opis {
  font-size: 1.125rem;
  line-height: 1.5rem;
  margin: 0;
  position: relative;
  z-index: 9;
  padding-top: 0.5rem;
}
#szkolenie-grupa-1 .field--name-field-ts-opis p {
  font-size: 1.125rem;
  line-height: 1.5rem;
}
#szkolenie-grupa-1 .inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}
#szkolenie-grupa-1 h2 {
  margin-top: 0;
}
#szkolenie-grupa-1 h1 {
  line-height: 3.125rem;
  font-weight: bold;
  font-size: 2.5rem;
  margin-top: 2.75rem;
  z-index: 9;
  position: relative;
}
#szkolenie-grupa-1 a, #szkolenie-grupa-1 span.h1, #szkolenie-grupa-1 h1, #szkolenie-grupa-1 h2, #szkolenie-grupa-1 h3, #szkolenie-grupa-1 h4, #szkolenie-grupa-1 h5, #szkolenie-grupa-1 p, #szkolenie-grupa-1 li {
  color: #fff;
}
#szkolenie-grupa-1 a.npx-program-button {
  color: #191919;
}
#szkolenie-grupa-1 ul, #szkolenie-grupa-1 ol {
  list-style-image: url("/themes/custom/bootstrap4grow/images/check-white.png");
}
#szkolenie-grupa-1 .group-right .obraz img {
  padding-left: 0.625rem;
}
#szkolenie-grupa-1 .obraz {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
#szkolenie-grupa-1 .obraz img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  color: transparent;
  min-height: 220px;
}
#szkolenie-grupa-1.full-width-image h1 {
  margin-top: 5.75rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
}
@media (max-width: 61.99875rem) {
  #szkolenie-grupa-1.full-width-image h1 {
    margin-top: 3rem;
  }
}
@media (min-width: 75rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    max-width: 100%;
    padding-right: 0;
    padding-left: 1.875rem;
  }
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    padding-left: calc(50vw - 44.21875rem + 1.875rem);
  }
}
#szkolenie-grupa-1.half-width-image h1 {
  margin-top: 2rem;
}
#szkolenie-grupa-1.half-width-image span.h1 {
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 2rem;
  line-height: 2.25rem;
  display: block;
  font-weight: 700;
}
#szkolenie-grupa-1 .inner-absolute {
  bottom: 10px;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 50%;
  width: 100%;
  z-index: 3;
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 1.5rem;
  line-height: 2rem;
  display: block;
  font-weight: 700;
  color: #fff;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-1 .inner-absolute {
    font-size: 2.5rem;
    line-height: 3.5rem;
  }
}

#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile {
  display: block;
  position: relative;
}
@media (min-width: 75rem) {
  #szkolenie-grupa-1.half-width-image .obraz--gradient-mobile, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile {
    display: none;
  }
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile img, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile img {
  position: relative;
  z-index: 1;
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile::before, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: -webkit-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: -o-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: linear-gradient(76.43deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  z-index: 2;
  pointer-events: none;
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile .inner-absolute, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile .inner-absolute {
  z-index: 3;
}

@media (max-width: 61.99875rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (min-width: 75rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
    padding-right: 1.5rem;
  }
}

.node--type-landing-page .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.node--type-landing-page .paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.node--type-landing-page .paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
  padding-bottom: 2rem !important;
}
@media (min-width: 75rem) {
  .node--type-landing-page #szkolenie-grupa-1.half-width-image h1 {
    margin-top: 5rem;
  }
}

@media (min-width: 75rem) {
  .paragraph--type-tytul-szkolenia-video.ds-2col-fluid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
  }
}
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .field--name-field-ts-video-embed,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .video-embed-field-lazy,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .video-embed-field-lazy img {
  height: 100%;
}

#szkolenie-grupa-1 .video-mobile {
  position: relative;
  padding-top: 56.25%;
  width: 100%;
}
#szkolenie-grupa-1 .video-mobile .video-embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#szkolenie-grupa-1 .video-mobile .video-embed iframe,
#szkolenie-grupa-1 .video-mobile .video-embed video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.video-embed-field-responsive-video {
  position: static !important;
  width: 100%;
}

.video-embed-field-lazy-play {
  border: none;
}

.n-breadcrumb {
  padding-left: 0.75rem;
  color: #000;
}
.n-breadcrumb a, .n-breadcrumb a:hover, .n-breadcrumb a:active, .n-breadcrumb a:focus {
  color: #000;
}
.n-breadcrumb li::before {
  content: " »";
  font-size: 1.2rem;
  padding: 0 0.5rem 0 0.25rem;
}
.n-breadcrumb li:first-child::before {
  content: none;
}
.n-breadcrumb.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1rem;
}

.node--type-landing-page .n-breadcrumb,
.node--type-npxtraining .n-breadcrumb {
  font-size: 0.875rem;
  color: #183881;
  line-height: 1.2;
  font-weight: 700;
  color: #183881;
}
.node--type-landing-page .n-breadcrumb a,
.node--type-landing-page .n-breadcrumb a:hover,
.node--type-landing-page .n-breadcrumb a:active,
.node--type-landing-page .n-breadcrumb a:focus,
.node--type-npxtraining .n-breadcrumb a,
.node--type-npxtraining .n-breadcrumb a:hover,
.node--type-npxtraining .n-breadcrumb a:active,
.node--type-npxtraining .n-breadcrumb a:focus {
  color: inherit;
  text-decoration: none;
}
.node--type-landing-page .n-breadcrumb li::before,
.node--type-npxtraining .n-breadcrumb li::before {
  content: "> ";
  font-size: 1.1em;
  width: 8px;
  padding: 0 0.5rem 0 0.5rem;
  color: inherit;
}
.node--type-landing-page .n-breadcrumb li:first-child::before,
.node--type-npxtraining .n-breadcrumb li:first-child::before {
  content: none;
}

#main-wrapper {
  border-top: 1px solid #ebedec;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
