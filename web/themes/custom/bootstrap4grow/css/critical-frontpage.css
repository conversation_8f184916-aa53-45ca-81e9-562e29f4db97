/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

body .slider {
  height: 703px;
}

body .slider .owl-item {
  background: none !important;
}
@media (min-width: 48rem) {
  body .slider .owl-item {
    background: url("/sites/default/files/2017-12/slider2-2.png") !important;
    background-repeat: no-repeat;
    -webkit-background-size: cover !important;
            background-size: cover !important;
    background-position: left -125px center !important;
  }
}
body .slider .owl-item .views-field-body {
  margin-left: auto;
  margin-right: auto;
  width: 96%;
}
@media (min-width: 36rem) {
  body .slider .owl-item .views-field-body {
    width: 1200px;
    max-width: 100%;
  }
}
body .slider .owl-item .views-field-body .field-content {
  width: 100%;
}
@media (min-width: 36rem) {
  body .slider .owl-item .views-field-body .field-content {
    width: auto;
  }
}
body .slider .owl-item .views-field-body .cytat {
  position: absolute;
}
@media (min-width: 62rem) {
  body .slider .owl-item .views-field-body .cytat {
    right: 75px;
    top: 25px;
    width: 30%;
  }
}
@media (min-width: 75rem) {
  body .slider .owl-item .views-field-body .cytat {
    right: 55px;
    top: -25px;
  }
}
@media (min-width: 81.25rem) {
  body .slider .owl-item .views-field-body .cytat {
    right: 245px;
  }
}
body .slider .owl-item .views-field-body .cytat img {
  display: none;
}
@media (min-width: 62rem) {
  body .slider .owl-item .views-field-body .cytat img {
    display: block;
  }
}
body .slider .owl-item .views-field-body .tekst {
  max-width: 450px;
  float: left;
}
@media (min-width: 36rem) {
  body .slider .owl-item .views-field-body .tekst {
    max-width: 100%;
    padding: 0 0.9375rem;
  }
}
@media (min-width: 48rem) {
  body .slider .owl-item .views-field-body .tekst {
    max-width: 450px;
  }
}
body .slider .owl-item .views-field-body .tekst em.title {
  font-size: 4.125rem;
  font-weight: 100;
  text-align: center;
  line-height: 3.875rem;
  text-transform: uppercase;
  padding-top: 6.25rem;
  margin-bottom: 1rem;
  float: left;
  font-style: normal;
  color: #fff;
}
body .slider .owl-item .views-field-body .tekst p, body .slider .owl-item .views-field-body .tekst h2 {
  color: #fff;
  font-size: 1.25rem;
  font-weight: 700;
  text-align: center;
  padding-top: 0.625rem;
  padding-bottom: 3.75rem;
}
body .slider .owl-item .views-field-body .tekst a {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  font-size: 1.125rem;
  padding: 1.3125rem 1.5625rem;
}
body .slider .owl-item .views-field-body .tekst a:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.frontcolor {
  color: #fff;
}

#block-bootstrap4grow-views-block-advantages-block-1 {
  width: 90%;
  background-image: none;
  padding: 0 5% 1.875rem;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 {
    background: #fff;
  }
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 {
    max-width: 900px;
  }
}
#block-bootstrap4grow-views-block-advantages-block-1 .view-footer {
  width: 100%;
  text-align: center;
  border-top: 1px solid #aeaeb0;
  background: #fff;
  color: #aeaeb0;
  padding: 0.625rem 0 1.25rem 0;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 .view-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}
#block-bootstrap4grow-views-block-advantages-block-1 .view-header {
  font-size: 1.8125rem;
  line-height: 2.0625rem;
  font-family: "Muli", sans-serif;
  text-align: center;
  padding: 4.0625rem 0;
  color: #0173bc;
  width: 100%;
}
#block-bootstrap4grow-views-block-advantages-block-1 .view-header b {
  font-weight: 700;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 .view-header {
    font-size: 2.3em;
    line-height: 1em;
  }
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row {
  text-align: center;
  width: 95%;
  padding: 0 1.25% 2.8125rem 1.25%;
  display: inline-table;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 .views-row {
    max-width: 46%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 46%;
        -ms-flex: 1 0 46%;
            flex: 1 0 46%;
  }
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 .views-row {
    max-width: 31%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
  }
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(1) .views-field-field-c {
  color: #ee1c25;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(1) .views-field-field-circle- {
  background: #ee1c25;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(2) .views-field-field-c {
  color: #0173bc;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(2) .views-field-field-circle- {
  background: #0173bc;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(3) .views-field-field-c {
  color: #ffbd11;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(3) .views-field-field-circle- {
  background: #ffbd11;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(4) .views-field-field-c {
  color: #018d4e;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(4) .views-field-field-circle- {
  background: #018d4e;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(5) .views-field-field-c {
  color: #f57921;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(5) .views-field-field-circle- {
  background: #f57921;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(6) .views-field-field-c {
  color: #01bcf3;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-row:nth-child(6) .views-field-field-circle- {
  background: #01bcf3;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-field-body {
  font-size: 1rem;
  text-transform: uppercase;
  line-height: 1.2rem;
  padding-top: 1.25rem;
  max-width: 100%;
  min-height: 90px;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-field-field-circle- {
  text-align: center;
  width: 150px;
  height: 150px;
  border-radius: 75px;
  color: #fff;
  font-size: 3rem;
  font-weight: 800;
  margin: auto;
  position: relative;
  top: -25px;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-field-field-circle-::after {
  position: static;
  content: "";
  width: 150px;
  height: 80px;
  background: url("../images/circle-shadow.png");
  background-repeat: no-repeat;
  background-position: center 20px;
  -webkit-background-size: contain;
          background-size: contain;
  display: block;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-field-field-circle- .field-content div {
  padding-top: 2.5rem;
}
#block-bootstrap4grow-views-block-advantages-block-1 .views-field-field-c {
  font-size: 0.875rem;
  margin-left: -1.5625rem;
  font-family: "Muli", sans-serif;
  font-weight: 800;
  float: left;
  width: 100px;
  text-align: right;
  margin-top: -1.25rem;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-advantages-block-1 .views-field-field-c {
    margin-left: -3.125rem;
    font-size: 1.125rem;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNvbW1vbi9fdmFyaWFibGVzLnNjc3MiLCJjcml0aWNhbC1mcm9udHBhZ2UuY3NzIiwicGFnZXMvZnJvbnQvX3NsaWRlci5zY3NzIiwiLi4vbm9kZV9tb2R1bGVzL2Jvb3RzdHJhcC9zY3NzL21peGlucy9fYnJlYWtwb2ludHMuc2NzcyIsImNvbW1vbi9fbWl4aW5zLnNjc3MiLCJwYWdlcy9mcm9udC9fdmlld3NfYmxvY2thZHZhbnRhZ2VzX2Jsb2NrXzEuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUF1Q0EsY0FBQTtBQW1GQSxlQUFBO0FBRUEsaUJBQUE7QUF5Q0E7RUFBTyxvQkFBQTtBQ2hLUDs7QUNMQTtFQUNFLGFBQUE7QURRRjs7QUNOQTtFQUNFLDJCQUFBO0FEU0Y7QUVrREk7RUQ1REo7SUFHSSx3RUFBQTtJQUNBLDRCQUFBO0lBQ0EseUNBQUE7WUFBQSxpQ0FBQTtJQUNBLGtEQUFBO0VEV0Y7QUFDRjtBQ1ZFO0VBQ0UsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7QURZSjtBRXFDSTtFRHBERjtJQUtJLGFBQUE7SUFDQSxlQUFBO0VEY0o7QUFDRjtBQ2JJO0VBQ0UsV0FBQTtBRGVOO0FFNEJJO0VENUNBO0lBR0ksV0FBQTtFRGlCTjtBQUNGO0FDZkk7RUFDRSxrQkFBQTtBRGlCTjtBRW9CSTtFRHRDQTtJQUdJLFdBQUE7SUFDQSxTQUFBO0lBQ0EsVUFBQTtFRG1CTjtBQUNGO0FFYUk7RUR0Q0E7SUFRSSxXQUFBO0lBQ0EsVUFBQTtFRHFCTjtBQUNGO0FFT0k7RUR0Q0E7SUFZSSxZQUFBO0VEdUJOO0FBQ0Y7QUN0Qk07RUFDRSxhQUFBO0FEd0JSO0FFREk7RUR4QkU7SUFHSSxjQUFBO0VEMEJSO0FBQ0Y7QUN2Qkk7RUFDRSxnQkFBQTtFQUNBLFdBQUE7QUR5Qk47QUVWSTtFRGpCQTtJQUlJLGVBQUE7SUFDQSxvQkFBQTtFRDJCTjtBQUNGO0FFaEJJO0VEakJBO0lBUUksZ0JBQUE7RUQ2Qk47QUFDRjtBQzVCTTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBRDhCUjtBQzVCTTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxxQkFBQTtFQUNBLHVCQUFBO0FEOEJSO0FDNUJNO0VFekVKLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUVBLHdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZ0NBQUE7RUFFQSx1QkFBQTtFQUNBLDRCQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkpvRmU7RUluRmYscUJBQUE7RUFDQSxlQUFBO0VGc0RELG1CQUFBO0VBQ08sNEJBQUE7QURxRFI7QUczR0U7RUFDRSxxQkFBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtBSDZHSjs7QUNyREE7RUFDRSxXQUFBO0FEd0RGOztBSTdJQTtFQUNFLFVBQUE7RUFDQSxzQkFBQTtFQUNBLHNCQUFBO0FKZ0pGO0FFcEZJO0VFL0RKO0lBS0ksZ0JBQUE7RUprSkY7QUFDRjtBRXpGSTtFRS9ESjtJQVFJLGdCQUFBO0VKb0pGO0FBQ0Y7QUluSkU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSw2QkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLDZCQUFBO0FKcUpKO0FFdEdJO0VFN0NGO0lBRUksb0JBQUE7SUFBQSxxQkFBQTtJQUFBLG9CQUFBO0lBQUEsYUFBQTtFSnFKSjtBQUNGO0FJbkpFO0VBQ0Usb0JBQUE7RUFDQSxzQkFBQTtFQUNBLCtCTDhEVTtFSzdEVixrQkFBQTtFQUNBLG9CQUFBO0VBQ0EsY0FBQTtFQUNBLFdBQUE7QUpxSko7QUlwSkk7RUFDRSxnQkFBQTtBSnNKTjtBRXZISTtFRXhDRjtJQVlJLGdCQUFBO0lBQ0EsZ0JBQUE7RUp1Sko7QUFDRjtBSXJKRTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLGdDQUFBO0VBQ0EscUJBQUE7QUp1Sko7QUVuSUk7RUV4QkY7SUFNSSxjQUFBO0lBQ0EsbUJBQUE7SUFBQSxxQkFBQTtRQUFBLGlCQUFBO1lBQUEsYUFBQTtFSnlKSjtBQUNGO0FFeklJO0VFeEJGO0lBVUksY0FBQTtJQUNBLG1CQUFBO0lBQUEscUJBQUE7UUFBQSxpQkFBQTtZQUFBLGFBQUE7RUoySko7QUFDRjtBSXZKTTtFQUNFLGNBQUE7QUp5SlI7QUl2Sk07RUFDRSxtQkFBQTtBSnlKUjtBSXJKTTtFQUNFLGNBQUE7QUp1SlI7QUlySk07RUFDRSxtQkFBQTtBSnVKUjtBSW5KTTtFQUNFLGNBQUE7QUpxSlI7QUluSk07RUFDRSxtQkFBQTtBSnFKUjtBSWpKTTtFQUNFLGNBQUE7QUptSlI7QUlqSk07RUFDRSxtQkFBQTtBSm1KUjtBSS9JTTtFQUNFLGNBQUE7QUppSlI7QUkvSU07RUFDRSxtQkFBQTtBSmlKUjtBSTdJTTtFQUNFLGNBQUE7QUorSVI7QUk3SU07RUFDRSxtQkFBQTtBSitJUjtBSTNJRTtFQUNFLGVMd0NhO0VLdkNiLHlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBSjZJSjtBSTNJRTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FKNklKO0FJNUlJO0VBQ0UsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSw4Q0FBQTtFQUNBLDRCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxnQ0FBQTtVQUFBLHdCQUFBO0VBQ0EsY0FBQTtBSjhJTjtBSTVJSTtFQUNFLG1CQUFBO0FKOElOO0FJM0lFO0VBQ0UsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLCtCTHBEVTtFS3FEVixnQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxvQkFBQTtBSjZJSjtBRS9OSTtFRTBFRjtJQVVJLHNCQUFBO0lBQ0EsbUJBQUE7RUorSUo7QUFDRiIsImZpbGUiOiJjcml0aWNhbC1mcm9udHBhZ2UuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiJGdyaWQtYnJlYWtwb2ludHM6IChcclxuICB4czogMCxcclxuICBzbTogNTc2cHgsXHJcbiAgbWQ6IDc2OHB4LFxyXG4gIGxnOiA5OTJweCxcclxuICBsZ206IDEwNTBweCxcclxuICBsZ2E6IDExMDFweCxcclxuICB4bDogMTIwMHB4LFxyXG4gIHhsYTogMTMwMHB4LFxyXG4gIGxkOiAxNDAwcHgsXHJcbiAgeHhsOiAxNjAwcHhcclxuKSAhZGVmYXVsdDtcclxuXHJcbiRjb250YWluZXItbWF4LXdpZHRoczogKFxyXG4gIHNtOiA1NzVweCxcclxuICBtZDogNzY3cHgsXHJcbiAgbGc6IDk5MXB4LFxyXG4gIGxnbTogMTA0OXB4LFxyXG4gIGxnYTogMTEwMHB4LFxyXG4gIHhsOiAxMTk5cHgsXHJcbiAgeGxhOiAxMjk5cHgsXHJcbiAgbGQ6IDEzOTlweCxcclxuICB4eGw6IDE1OTlweFxyXG4pICFkZWZhdWx0O1xyXG5cclxuJHNwYWNlcjogMXJlbTtcclxuXHJcbiRzcGFjZXJzOiAoXHJcbiAgMDogMCxcclxuICAxOiAoJHNwYWNlciAqIC41KSwgLy84cHhcclxuICAyOiAoJHNwYWNlciAqIC42MjUpLCAvLzEwcHhcclxuICAzOiAoJHNwYWNlciAqIDAuOTM3NSksIC8vMTVweFxyXG4gIDQ6ICRzcGFjZXIsIC8vMTZweFxyXG4gIDU6ICgkc3BhY2VyICogMS4yNSksICAvLzIwcHhcclxuICA2OiAoJHNwYWNlciAqIDEuODc1KSwgLy8zMHB4XHJcbiAgNzogKCRzcGFjZXIgKiAyLjUpLCAvLzQwcHhcclxuICA4OiAoJHNwYWNlciAqIDMpXHJcbik7XHJcblxyXG4vKiBWQVJJQUJMRVMgKi9cclxuXHJcbi8vIGNvbG9yc1xyXG4kcHJpbWFyeS1zaGFkZTogcmdiKDI1NSwgNzgsIDQ2KTtcclxuJHByaW1hcnktbGlnaHQ6IGxpZ2h0ZW4oJHByaW1hcnktc2hhZGUsIDM3JSk7XHJcbiRwcmltYXJ5LWRhcms6IGRhcmtlbigkcHJpbWFyeS1zaGFkZSwgMTIlKTtcclxuJGFjY2VudC1zaGFkZTogIzAwNzlDMDtcclxuJGFjY2VudC1saWdodDogbGlnaHRlbigkYWNjZW50LXNoYWRlLCAzNyUpO1xyXG4kYWNjZW50LWRhcms6IGRhcmtlbigkYWNjZW50LXNoYWRlLCAxMiUpO1xyXG4vLyBSZWFzc2lnbiBjb2xvciB2YXJzIHRvIHNlbWFudGljIGNvbG9yIHNjaGVtZVxyXG4kcmVkOiAjZGMzNTQ1O1xyXG4keWVsbG93OiAjZmZjMTA3O1xyXG4kZ3JlZW46ICMyOGE3NDU7XHJcbiRjeWFuOiAjMTdhMmI4O1xyXG4kd2hpdGU6ICNmZmY7XHJcbiRncmF5LTEwMDogI2Y4ZjlmYTtcclxuJGdyYXktODAwOiAjMzQzYTQwO1xyXG4kYmxhY2s6ICMwMDAwMDA7XHJcbiR0aGVtZS1jb2xvcnM6ICggcHJpbWFyeTogJHdoaXRlLCBzZWNvbmRhcnk6ICMwMDY2Y2MsIHN1Y2Nlc3M6ICRncmVlbiwgaW5mbzogJGN5YW4sIHdhcm5pbmc6ICR5ZWxsb3csIGRhbmdlcjogJHJlZCwgbGlnaHQ6ICRncmF5LTEwMCwgZGFyazogJGdyYXktODAwKTtcclxuXHJcbiRicmFuZC1wcmltYXJ5OiAkYWNjZW50LXNoYWRlO1xyXG4vLyRicmFuZC1zdWNjZXNzOiAkZ3JlZW47XHJcbi8vJGJyYW5kLWluZm86ICR0ZWFsO1xyXG4vLyRicmFuZC13YXJuaW5nOiAkb3JhbmdlO1xyXG4vLyRicmFuZC1kYW5nZXI6ICRyZWQ7XHJcbiRicmFuZC1pbnZlcnNlOiAkcHJpbWFyeS1zaGFkZTtcclxuLy8gQm9keVxyXG4vL1xyXG4vLyBTZXR0aW5ncyBmb3IgdGhlIGA8Ym9keT5gIGVsZW1lbnQuXHJcbiRib2R5LWJnOiAkd2hpdGU7XHJcbiRib2R5LWNvbG9yOiAkZ3JheS04MDA7XHJcbiRpbnZlcnNlLWJnOiAkcHJpbWFyeS1zaGFkZTtcclxuJGludmVyc2UtY29sb3I6ICR3aGl0ZTtcclxuLy8gTGlua3NcclxuLy9cclxuLy8gU3R5bGUgYW5jaG9yIGVsZW1lbnRzLlxyXG4kbGluay1kZWNvcmF0aW9uOiBub25lO1xyXG4kbGluay1ob3Zlci1jb2xvcjogJGFjY2VudC1kYXJrO1xyXG4kbGluay1ob3Zlci1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbi8vIENvbW1lbnRzXHJcbiRjb21tZW50LXNwYWNlci14OiAxLjI1ZW07XHJcbiRjb21tZW50LXNwYWNlci15OiAxLjI1ZW07XHJcbi8vIFJlc3BvbnNpdmUgZm9udCBzaXplc1xyXG4kZW5hYmxlLXJlc3BvbnNpdmUtZm9udC1zaXplczogdHJ1ZTtcclxuJGVuYWJsZS1zaGFkb3dzOiBmYWxzZTtcclxuJGVuYWJsZS1ncmFkaWVudHM6IGZhbHNlO1xyXG4vLyBCcmVhZGNydW1icyAoV29ya3Mgb25seSBvbiBCb290c3RyYXAgNSspXHJcbi8vICRicmVhZGNydW1iLWRpdmlkZXI6IHF1b3RlKFwiPlwiKTtcclxuXHJcbiRmb250LWZhbWlseTogXCJNdWxpXCIsIHNhbnMtc2VyaWY7XHJcbiRyb2JvdG86ICRmb250LWZhbWlseTtcclxuXHJcblxyXG4kbGlzdC1ncm91cC1hY3RpdmUtYmc6IHRyYW5zcGFyZW50O1xyXG4kbGlzdC1ncm91cC1iZzogI2ZmZjtcclxuJHRhYmxlLWNlbGwtcGFkZGluZzogOHB4O1xyXG4kYmFzZS1mb250LXNpemU6IDE2cHg7XHJcbiRwLWZvbnQtc2l6ZTogMXJlbTtcclxuJGZvbnQtd2VpZ2h0LWJvbGRlcjogNzAwO1xyXG5cclxuJGJvcmRlci1zZXBhcmF0b3I6IDFweCBzb2xpZCAjZDBkOGRiO1xyXG4kYm9yZGVyLXNlcGFyYXRvci1saWdodDogMXB4IHNvbGlkICNlM2U5ZTk7XHJcblxyXG5cclxuJGxpbmstY29sb3I6ICMwMDUzQjM7XHJcbiRwYWdpbmF0aW9uLWFjdGl2ZS1iZzogIzA2YztcclxuJHBhZ2luYXRpb24tY29sb3I6ICMwNmM7XHJcbiRjb2xvci10ZXJ0aWFyeTogI2ZlY2MwOTsgLy9idXR0b25zXHJcbiRucHhTcG9pbGVyQ29sb3JHcmF5OiAjMDM0YjdkO1xyXG5cclxuJG5weEZyZXNobWFpbFN1Ym1pdEJhY2tncm91bmQ6ICNlNDU0ZmY7XHJcbiRucHhGcmVzaG1haWxTdWJtaXRIb3ZlckJhY2tncm91bmQ6ICNjNDM0ZGY7XHJcbiRucHhGcmVzaG1haWxTdWJtaXRDb2xvcjogI2ZmZjtcclxuJG5weEZyZXNobWFpbE1zZ0NvbG9yOiAjOTA5MDkwO1xyXG5cclxuJGNvbG9yLXByaW1hcnk6ICMyQTdERTM7XHJcbiRjb2xvci1ibHVlLWRhcmtlcjogIzAwNTZCMztcclxuJGNvbG9yLW9yYW5nZS1idXR0b246ICNGRjk5MDA7XHJcbiRjb2xvci1vcmFuZ2UtYnV0dG9uLWhvdmVyOiAjRkZCNTQ2O1xyXG4kY29sb3ItYm9yZGVyLXllbGxvdzogI0YyQ0ExMDtcclxuJGdyZXktMTAwLW5ldzogI0Y1RjVGNTtcclxuXHJcblxyXG4vKiBUWVBPR1JBUEhZICovXHJcblxyXG4vKiBHb29nbGUgRm9udHMgKi9cclxuXHJcbi8vQGltcG9ydCB1cmwoXCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2Nzcz9mYW1pbHk9TGF0bzo0MDAsMzAwLDMwMGl0YWxpYyw0MDBpdGFsaWMsNzAwLDcwMGl0YWxpYyw5MDAsOTAwaXRhbGljXCIpO1xyXG4vL0BpbXBvcnQgdXJsKFwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3M/ZmFtaWx5PVJhbGV3YXk6NDAwLDMwMCw1MDAsNjAwLDcwMCw4MDAsOTAwXCIpO1xyXG4vLyBmb250IGZhbWlsaWVzXHJcbiRmb250LWxhdG86ICdMYXRvJyxcclxuQXJpYWwsXHJcblZlcmRhbmEsXHJcbnNhbnMtc2VyaWY7XHJcbiRmb250LXJhbGV3YXk6ICdSYWxld2F5JyxcclxuQXJpYWwsXHJcblZlcmRhbmEsXHJcbnNhbnMtc2VyaWY7XHJcbi8vIEZvbnRzXHJcbi8vXHJcbi8vIEZvbnQsIGxpbmUtaGVpZ2h0LCBhbmQgY29sb3IgZm9yIGJvZHkgdGV4dCwgaGVhZGluZ3MsIGFuZCBtb3JlLlxyXG4kZm9udC1mYW1pbHktc2Fucy1zZXJpZjogICAgICAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsIFwiU2Vnb2UgVUlcIiwgUm9ib3RvLCBcIkhlbHZldGljYSBOZXVlXCIsIEFyaWFsLCBcIk5vdG8gU2Fuc1wiLCBzYW5zLXNlcmlmLCBcIkFwcGxlIENvbG9yIEVtb2ppXCIsIFwiU2Vnb2UgVUkgRW1vamlcIiwgXCJTZWdvZSBVSSBTeW1ib2xcIiwgXCJOb3RvIENvbG9yIEVtb2ppXCI7XHJcbiRmb250LWZhbWlseS1tb25vc3BhY2U6ICAgICAgIFNGTW9uby1SZWd1bGFyLCBNZW5sbywgTW9uYWNvLCBDb25zb2xhcywgXCJMaWJlcmF0aW9uIE1vbm9cIiwgXCJDb3VyaWVyIE5ld1wiLCBtb25vc3BhY2U7XHJcbiRmb250LWZhbWlseS1zZXJpZjogR2VvcmdpYSxcIlRpbWVzIE5ldyBSb21hblwiLFRpbWVzLHNlcmlmO1xyXG4kZm9udC1mYW1pbHktYmFzZTogJGZvbnQtZmFtaWx5O1xyXG4kZm9udC1zaXplLWJhc2U6IDFyZW07IC8vIEFzc3VtZXMgdGhlIGJyb3dzZXIgZGVmYXVsdCwgdHlwaWNhbGx5IGAxNnB4YFxyXG4kZm9udC1zaXplLWxnOiAkZm9udC1zaXplLWJhc2UgKiAxLjI1O1xyXG4kZm9udC1zaXplLXNtOiAkZm9udC1zaXplLWJhc2UgKiAuODc1O1xyXG4kZm9udC1zaXplLXhzOiAkZm9udC1zaXplLWJhc2UgKiAuNzU7XHJcbiRmb250LXdlaWdodC1ub3JtYWw6IG5vcm1hbDtcclxuJGZvbnQtd2VpZ2h0LWJvbGQ6IGJvbGQ7XHJcbiRmb250LXdlaWdodC1iYXNlOiAkZm9udC13ZWlnaHQtbm9ybWFsO1xyXG4kbGluZS1oZWlnaHQtYmFzZTogMS41O1xyXG4kaDEtZm9udC1zaXplOiAkZm9udC1zaXplLWJhc2UgKiAyLjU7XHJcbiRoMi1mb250LXNpemU6ICRmb250LXNpemUtYmFzZSAqIDI7XHJcbiRoMy1mb250LXNpemU6ICRmb250LXNpemUtYmFzZSAqIDEuNTtcclxuJGg0LWZvbnQtc2l6ZTogJGZvbnQtc2l6ZS1iYXNlICogMS4yO1xyXG5cclxuJGhlYWRpbmdzLWZvbnQtd2VpZ2h0OiA2MDA7XHJcbiRmb250LXdlaWdodC1saWdodDogNDAwO1xyXG4kZm9udC1zaXplLW1kLW5ldzogJGZvbnQtc2l6ZS1iYXNlICogMS4xMjU7XHJcblxyXG5cclxuXHJcbiRtYXgtd2lkdGgtY29udGFpbmVyOiAxNDE1cHg7XHJcblxyXG46cm9vdCB7LS1zZWNvbmRhcnk6ICMwMDY2Y2M7fVxyXG5cclxuXHJcbi8vIE5lY2Vzc2FyeSBCUyB2YXJpYWJsZXNcclxuJGdyYXktMjAwOiAjZTllY2VmICFkZWZhdWx0O1xyXG4kZ3JheS0zMDA6ICNkZWUyZTYgIWRlZmF1bHQ7XHJcbiRncmF5LTcwMDogIzQ5NTA1NyAhZGVmYXVsdDtcclxuJGNvbXBvbmVudC1hY3RpdmUtY29sb3I6ICNmZmYgIWRlZmF1bHQ7XHJcbiRjb21wb25lbnQtYWN0aXZlLWJnOiAjMGQ2ZWZkICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstcGFkZGluZy15OiAuNXJlbSAhZGVmYXVsdDtcclxuJG5hdi1saW5rLXBhZGRpbmcteDogMXJlbSAhZGVmYXVsdDtcclxuJG5hdi1saW5rLWZvbnQtc2l6ZTogbnVsbCAhZGVmYXVsdDtcclxuJG5hdi1saW5rLWZvbnQtd2VpZ2h0OiBudWxsICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstY29sb3I6ICRsaW5rLWNvbG9yICFkZWZhdWx0O1xyXG4kbmF2LWxpbmstdHJhbnNpdGlvbjogY29sb3IgLjE1cyBlYXNlLWluLW91dCwgYmFja2dyb3VuZC1jb2xvciAuMTVzIGVhc2UtaW4tb3V0LCBib3JkZXItY29sb3IgLjE1cyBlYXNlLWluLW91dCAhZGVmYXVsdDtcclxuJGVuYWJsZS10cmFuc2l0aW9uczogdHJ1ZSAhZGVmYXVsdDtcclxuJGVuYWJsZS1yZWR1Y2VkLW1vdGlvbjogdHJ1ZSAhZGVmYXVsdDtcclxuJG5hdi1saW5rLWhvdmVyLWNvbG9yOiAkbGluay1ob3Zlci1jb2xvciAhZGVmYXVsdDtcclxuJG5hdi1saW5rLWRpc2FibGVkLWNvbG9yOiAjNmM3NTdkICFkZWZhdWx0O1xyXG4kbmF2LXRhYnMtYm9yZGVyLXdpZHRoOiAxcHggIWRlZmF1bHQ7XHJcbiRuYXYtdGFicy1ib3JkZXItY29sb3I6ICRncmF5LTMwMCAhZGVmYXVsdDtcclxuJGJvcmRlci1yYWRpdXM6IC4yNXJlbSAhZGVmYXVsdDtcclxuJG5hdi10YWJzLWJvcmRlci1yYWRpdXM6ICRib3JkZXItcmFkaXVzICFkZWZhdWx0O1xyXG4kZW5hYmxlLXJvdW5kZWQ6IHRydWUgIWRlZmF1bHQ7XHJcbiRuYXYtdGFicy1saW5rLWhvdmVyLWJvcmRlci1jb2xvcjogJGdyYXktMjAwICRncmF5LTIwMCAkbmF2LXRhYnMtYm9yZGVyLWNvbG9yICFkZWZhdWx0O1xyXG4kbmF2LXRhYnMtbGluay1hY3RpdmUtY29sb3I6ICRncmF5LTcwMCAhZGVmYXVsdDtcclxuJG5hdi10YWJzLWxpbmstYWN0aXZlLWJnOiAkYm9keS1iZyAhZGVmYXVsdDtcclxuJG5hdi10YWJzLWxpbmstYWN0aXZlLWJvcmRlci1jb2xvcjogJGdyYXktMzAwICRncmF5LTMwMCAkbmF2LXRhYnMtbGluay1hY3RpdmUtYmcgIWRlZmF1bHQ7XHJcbiRuYXYtcGlsbHMtYm9yZGVyLXJhZGl1czogJGJvcmRlci1yYWRpdXMgIWRlZmF1bHQ7XHJcbiRuYXYtcGlsbHMtbGluay1hY3RpdmUtY29sb3I6ICRjb21wb25lbnQtYWN0aXZlLWNvbG9yICFkZWZhdWx0O1xyXG4kbmF2LXBpbGxzLWxpbmstYWN0aXZlLWJnOiAkY29tcG9uZW50LWFjdGl2ZS1iZyAhZGVmYXVsdDtcclxuXHJcbi8vIEFkZGl0aW9uYWwgdmFyaWFibGVzIGZvciB0aWxlcyBhbmQgY29tcG9uZW50c1xyXG4kY29sb3ItcHJpbWFyeTogIzE4Mzg4MTtcclxuJGNvbG9yLWJvcmRlci1saWdodDogI0Y0RjlGRjtcclxuJGNvbG9yLWFjY2VudDogI2YyY2ExMDtcclxuJGNvbG9yLWFjY2VudC1hbHQ6ICNmZmQ4MTc7XHJcbiRjb2xvci1saW5rOiAjMDA1NkIzO1xyXG4kY29sb3ItdGV4dDogIzM0M0E0MDtcclxuJGNvbG9yLWJsdWU6ICMyQTdERTM7XHJcblxyXG4kZm9udC1zaXplLXRpdGxlOiAyMHB4O1xyXG4kZm9udC1zaXplLWNvbnRlbnQ6IDE0cHg7XHJcbiRmb250LXNpemUtc21hbGw6IDEycHg7XHJcblxyXG4kZm9udC13ZWlnaHQtc2VtaWJvbGQ6IDYwMDtcclxuXHJcbiIsIi8qIFZBUklBQkxFUyAqL1xuLyogVFlQT0dSQVBIWSAqL1xuLyogR29vZ2xlIEZvbnRzICovXG46cm9vdCB7XG4gIC0tc2Vjb25kYXJ5OiAjMDA2NmNjO1xufVxuXG5ib2R5IC5zbGlkZXIge1xuICBoZWlnaHQ6IDcwM3B4O1xufVxuXG5ib2R5IC5zbGlkZXIgLm93bC1pdGVtIHtcbiAgYmFja2dyb3VuZDogbm9uZSAhaW1wb3J0YW50O1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XG4gIGJvZHkgLnNsaWRlciAub3dsLWl0ZW0ge1xuICAgIGJhY2tncm91bmQ6IHVybChcIi9zaXRlcy9kZWZhdWx0L2ZpbGVzLzIwMTctMTIvc2xpZGVyMi0yLnBuZ1wiKSAhaW1wb3J0YW50O1xuICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XG4gICAgYmFja2dyb3VuZC1zaXplOiBjb3ZlciAhaW1wb3J0YW50O1xuICAgIGJhY2tncm91bmQtcG9zaXRpb246IGxlZnQgLTEyNXB4IGNlbnRlciAhaW1wb3J0YW50O1xuICB9XG59XG5ib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IHtcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XG4gIG1hcmdpbi1yaWdodDogYXV0bztcbiAgd2lkdGg6IDk2JTtcbn1cbkBtZWRpYSAobWluLXdpZHRoOiA1NzZweCkge1xuICBib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IHtcbiAgICB3aWR0aDogMTIwMHB4O1xuICAgIG1heC13aWR0aDogMTAwJTtcbiAgfVxufVxuYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAuZmllbGQtY29udGVudCB7XG4gIHdpZHRoOiAxMDAlO1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gIGJvZHkgLnNsaWRlciAub3dsLWl0ZW0gLnZpZXdzLWZpZWxkLWJvZHkgLmZpZWxkLWNvbnRlbnQge1xuICAgIHdpZHRoOiBhdXRvO1xuICB9XG59XG5ib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC5jeXRhdCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbn1cbkBtZWRpYSAobWluLXdpZHRoOiA5OTJweCkge1xuICBib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC5jeXRhdCB7XG4gICAgcmlnaHQ6IDc1cHg7XG4gICAgdG9wOiAyNXB4O1xuICAgIHdpZHRoOiAzMCU7XG4gIH1cbn1cbkBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIHtcbiAgYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAuY3l0YXQge1xuICAgIHJpZ2h0OiA1NXB4O1xuICAgIHRvcDogLTI1cHg7XG4gIH1cbn1cbkBtZWRpYSAobWluLXdpZHRoOiAxMzAwcHgpIHtcbiAgYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAuY3l0YXQge1xuICAgIHJpZ2h0OiAyNDVweDtcbiAgfVxufVxuYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAuY3l0YXQgaW1nIHtcbiAgZGlzcGxheTogbm9uZTtcbn1cbkBtZWRpYSAobWluLXdpZHRoOiA5OTJweCkge1xuICBib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC5jeXRhdCBpbWcge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICB9XG59XG5ib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC50ZWtzdCB7XG4gIG1heC13aWR0aDogNDUwcHg7XG4gIGZsb2F0OiBsZWZ0O1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gIGJvZHkgLnNsaWRlciAub3dsLWl0ZW0gLnZpZXdzLWZpZWxkLWJvZHkgLnRla3N0IHtcbiAgICBtYXgtd2lkdGg6IDEwMCU7XG4gICAgcGFkZGluZzogMCAwLjkzNzVyZW07XG4gIH1cbn1cbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkge1xuICBib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC50ZWtzdCB7XG4gICAgbWF4LXdpZHRoOiA0NTBweDtcbiAgfVxufVxuYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAudGVrc3QgZW0udGl0bGUge1xuICBmb250LXNpemU6IDY2cHg7XG4gIGZvbnQtd2VpZ2h0OiAxMDA7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbGluZS1oZWlnaHQ6IDYycHg7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIHBhZGRpbmctdG9wOiAxMDBweDtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgZmxvYXQ6IGxlZnQ7XG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcbiAgY29sb3I6ICNmZmY7XG59XG5ib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC50ZWtzdCBwLCBib2R5IC5zbGlkZXIgLm93bC1pdGVtIC52aWV3cy1maWVsZC1ib2R5IC50ZWtzdCBoMiB7XG4gIGNvbG9yOiAjZmZmO1xuICBmb250LXNpemU6IDIwcHg7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZy10b3A6IDAuNjI1cmVtO1xuICBwYWRkaW5nLWJvdHRvbTogNjBweDtcbn1cbmJvZHkgLnNsaWRlciAub3dsLWl0ZW0gLnZpZXdzLWZpZWxkLWJvZHkgLnRla3N0IGEge1xuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcbiAgLXdlYmtpdC1ib3JkZXItcmFkaXVzOiA0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAtbW96LWJveC1zaGFkb3c6IG5vbmU7XG4gIC13ZWJraXQtYm94LXNoYWRvdzogbm9uZTtcbiAgYm94LXNoYWRvdzogbm9uZTtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBmb250LXdlaWdodDogNzAwO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGNvbG9yOiAjMTkxOTE5O1xuICBsaW5lLWhlaWdodDogMjRweDtcbiAgcGFkZGluZzogMTJweCAzMHB4O1xuICB3aWR0aDogYXV0bztcbiAgYm9yZGVyOiBub25lO1xuICBtYXJnaW46IDEwcHggYXV0byAxMHB4IDA7XG4gIC1tb3otdHJhbnNpdGlvbjogYWxsIDAuNXM7XG4gIC1vLXRyYW5zaXRpb246IGFsbCAwLjVzO1xuICAtd2Via2l0LXRyYW5zaXRpb246IGFsbCAwLjVzO1xuICB0cmFuc2l0aW9uOiBhbGwgMC41cztcbiAgYmFja2dyb3VuZDogI2ZlY2MwOTtcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICBtYXgtd2lkdGg6IDEwMCU7XG4gIGZvbnQtc2l6ZTogMThweDtcbiAgcGFkZGluZzogMjFweCAyNXB4O1xufVxuYm9keSAuc2xpZGVyIC5vd2wtaXRlbSAudmlld3MtZmllbGQtYm9keSAudGVrc3QgYTpob3ZlciB7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmYWIxYTtcbiAgY29sb3I6ICMxOTE5MTk7XG59XG5cbi5mcm9udGNvbG9yIHtcbiAgY29sb3I6ICNmZmY7XG59XG5cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEge1xuICB3aWR0aDogOTAlO1xuICBiYWNrZ3JvdW5kLWltYWdlOiBub25lO1xuICBwYWRkaW5nOiAwIDUlIDEuODc1cmVtO1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XG4gICNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEge1xuICAgIGJhY2tncm91bmQ6ICNmZmY7XG4gIH1cbn1cbkBtZWRpYSAobWluLXdpZHRoOiA5OTJweCkge1xuICAjYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIHtcbiAgICBtYXgtd2lkdGg6IDkwMHB4O1xuICB9XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3LWZvb3RlciB7XG4gIHdpZHRoOiAxMDAlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjYWVhZWIwO1xuICBiYWNrZ3JvdW5kOiAjZmZmO1xuICBjb2xvcjogI2FlYWViMDtcbiAgcGFkZGluZzogMC42MjVyZW0gMCAxLjI1cmVtIDA7XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNTc2cHgpIHtcbiAgI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlldy1jb250ZW50IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICB9XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3LWhlYWRlciB7XG4gIGZvbnQtc2l6ZTogMjlweDtcbiAgbGluZS1oZWlnaHQ6IDMzcHg7XG4gIGZvbnQtZmFtaWx5OiBcIk11bGlcIiwgc2Fucy1zZXJpZjtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nOiA2NXB4IDA7XG4gIGNvbG9yOiAjMDE3M2JjO1xuICB3aWR0aDogMTAwJTtcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXctaGVhZGVyIGIge1xuICBmb250LXdlaWdodDogNzAwO1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gICNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXctaGVhZGVyIHtcbiAgICBmb250LXNpemU6IDIuM2VtO1xuICAgIGxpbmUtaGVpZ2h0OiAxZW07XG4gIH1cbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLXJvdyB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgd2lkdGg6IDk1JTtcbiAgcGFkZGluZzogMCAxLjI1JSA0NXB4IDEuMjUlO1xuICBkaXNwbGF5OiBpbmxpbmUtdGFibGU7XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNTc2cHgpIHtcbiAgI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93IHtcbiAgICBtYXgtd2lkdGg6IDQ2JTtcbiAgICBmbGV4OiAxIDAgNDYlO1xuICB9XG59XG5AbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcbiAgI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93IHtcbiAgICBtYXgtd2lkdGg6IDMxJTtcbiAgICBmbGV4OiAxIDAgMzElO1xuICB9XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1yb3c6bnRoLWNoaWxkKDEpIC52aWV3cy1maWVsZC1maWVsZC1jIHtcbiAgY29sb3I6ICNlZTFjMjU7XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1yb3c6bnRoLWNoaWxkKDEpIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIHtcbiAgYmFja2dyb3VuZDogI2VlMWMyNTtcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLXJvdzpudGgtY2hpbGQoMikgLnZpZXdzLWZpZWxkLWZpZWxkLWMge1xuICBjb2xvcjogIzAxNzNiYztcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLXJvdzpudGgtY2hpbGQoMikgLnZpZXdzLWZpZWxkLWZpZWxkLWNpcmNsZS0ge1xuICBiYWNrZ3JvdW5kOiAjMDE3M2JjO1xufVxuI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93Om50aC1jaGlsZCgzKSAudmlld3MtZmllbGQtZmllbGQtYyB7XG4gIGNvbG9yOiAjZmZiZDExO1xufVxuI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93Om50aC1jaGlsZCgzKSAudmlld3MtZmllbGQtZmllbGQtY2lyY2xlLSB7XG4gIGJhY2tncm91bmQ6ICNmZmJkMTE7XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1yb3c6bnRoLWNoaWxkKDQpIC52aWV3cy1maWVsZC1maWVsZC1jIHtcbiAgY29sb3I6ICMwMThkNGU7XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1yb3c6bnRoLWNoaWxkKDQpIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIHtcbiAgYmFja2dyb3VuZDogIzAxOGQ0ZTtcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLXJvdzpudGgtY2hpbGQoNSkgLnZpZXdzLWZpZWxkLWZpZWxkLWMge1xuICBjb2xvcjogI2Y1NzkyMTtcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLXJvdzpudGgtY2hpbGQoNSkgLnZpZXdzLWZpZWxkLWZpZWxkLWNpcmNsZS0ge1xuICBiYWNrZ3JvdW5kOiAjZjU3OTIxO1xufVxuI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93Om50aC1jaGlsZCg2KSAudmlld3MtZmllbGQtZmllbGQtYyB7XG4gIGNvbG9yOiAjMDFiY2YzO1xufVxuI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3Mtcm93Om50aC1jaGlsZCg2KSAudmlld3MtZmllbGQtZmllbGQtY2lyY2xlLSB7XG4gIGJhY2tncm91bmQ6ICMwMWJjZjM7XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1maWVsZC1ib2R5IHtcbiAgZm9udC1zaXplOiAxcmVtO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBsaW5lLWhlaWdodDogMTkuMnB4O1xuICBwYWRkaW5nLXRvcDogMS4yNXJlbTtcbiAgbWF4LXdpZHRoOiAxMDAlO1xuICBtaW4taGVpZ2h0OiA5MHB4O1xufVxuI2Jsb2NrLWJvb3RzdHJhcDRncm93LXZpZXdzLWJsb2NrLWFkdmFudGFnZXMtYmxvY2stMSAudmlld3MtZmllbGQtZmllbGQtY2lyY2xlLSB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgd2lkdGg6IDE1MHB4O1xuICBoZWlnaHQ6IDE1MHB4O1xuICBib3JkZXItcmFkaXVzOiA3NXB4O1xuICBjb2xvcjogI2ZmZjtcbiAgZm9udC1zaXplOiA0OHB4O1xuICBmb250LXdlaWdodDogODAwO1xuICBtYXJnaW46IGF1dG87XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgdG9wOiAtMjVweDtcbn1cbiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLWZpZWxkLWZpZWxkLWNpcmNsZS06OmFmdGVyIHtcbiAgcG9zaXRpb246IHN0YXRpYztcbiAgY29udGVudDogXCJcIjtcbiAgd2lkdGg6IDE1MHB4O1xuICBoZWlnaHQ6IDgwcHg7XG4gIGJhY2tncm91bmQ6IHVybChcIi4uL2ltYWdlcy9jaXJjbGUtc2hhZG93LnBuZ1wiKTtcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyIDIwcHg7XG4gIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjtcbiAgZGlzcGxheTogYmxvY2s7XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIC5maWVsZC1jb250ZW50IGRpdiB7XG4gIHBhZGRpbmctdG9wOiAyLjVyZW07XG59XG4jYmxvY2stYm9vdHN0cmFwNGdyb3ctdmlld3MtYmxvY2stYWR2YW50YWdlcy1ibG9jay0xIC52aWV3cy1maWVsZC1maWVsZC1jIHtcbiAgZm9udC1zaXplOiAxNHB4O1xuICBtYXJnaW4tbGVmdDogLTI1cHg7XG4gIGZvbnQtZmFtaWx5OiBcIk11bGlcIiwgc2Fucy1zZXJpZjtcbiAgZm9udC13ZWlnaHQ6IDgwMDtcbiAgZmxvYXQ6IGxlZnQ7XG4gIHdpZHRoOiAxMDBweDtcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gIG1hcmdpbi10b3A6IC0yMHB4O1xufVxuQG1lZGlhIChtaW4td2lkdGg6IDU3NnB4KSB7XG4gICNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEgLnZpZXdzLWZpZWxkLWZpZWxkLWMge1xuICAgIG1hcmdpbi1sZWZ0OiAtNTBweDtcbiAgICBmb250LXNpemU6IDE4cHg7XG4gIH1cbn0iLCJib2R5IC5zbGlkZXIge1xyXG4gIGhlaWdodDogNzAzcHg7XHJcbn1cclxuYm9keSAuc2xpZGVyIC5vd2wtaXRlbSB7XHJcbiAgYmFja2dyb3VuZDogbm9uZSFpbXBvcnRhbnQ7XHJcbiAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChtZCkge1xyXG4gICAgYmFja2dyb3VuZDogdXJsKCcvc2l0ZXMvZGVmYXVsdC9maWxlcy8yMDE3LTEyL3NsaWRlcjItMi5wbmcnKSFpbXBvcnRhbnQ7XHJcbiAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgYmFja2dyb3VuZC1zaXplOiBjb3ZlciAhaW1wb3J0YW50O1xyXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogbGVmdCAtMTI1cHggY2VudGVyICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG4gIC52aWV3cy1maWVsZC1ib2R5IHtcclxuICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xyXG4gICAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xyXG4gICAgd2lkdGg6IDk2JTtcclxuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoc20pIHtcclxuICAgICAgd2lkdGg6IDEyMDBweDtcclxuICAgICAgbWF4LXdpZHRoOiAxMDAlO1xyXG4gICAgfVxyXG4gICAgLmZpZWxkLWNvbnRlbnQge1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChzbSkge1xyXG4gICAgICAgIHdpZHRoOiBhdXRvO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICAuY3l0YXQge1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAobGcpIHtcclxuICAgICAgICByaWdodDogNzVweDtcclxuICAgICAgICB0b3A6IDI1cHg7XHJcbiAgICAgICAgd2lkdGg6IDMwJTtcclxuICAgICAgfVxyXG4gICAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKHhsKSB7XHJcbiAgICAgICAgcmlnaHQ6IDU1cHg7XHJcbiAgICAgICAgdG9wOiAtMjVweDtcclxuICAgICAgfVxyXG4gICAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKHhsYSkge1xyXG4gICAgICAgIHJpZ2h0OiAyNDVweDtcclxuICAgICAgfVxyXG4gICAgICBpbWcge1xyXG4gICAgICAgIGRpc3BsYXk6IG5vbmU7XHJcblx0QGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChsZykge1xyXG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICAudGVrc3Qge1xyXG4gICAgICBtYXgtd2lkdGg6IDQ1MHB4O1xyXG4gICAgICBmbG9hdDogbGVmdDtcclxuICAgICAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChzbSkge1xyXG4gICAgICAgIG1heC13aWR0aDogMTAwJTtcclxuICAgICAgICBwYWRkaW5nOiBzcGFjZXIoMCAzKTtcclxuICAgICAgfVxyXG4gICAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKG1kKSB7XHJcbiAgICAgICAgbWF4LXdpZHRoOiA0NTBweDtcclxuICAgICAgfVxyXG4gICAgICBlbS50aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiA2NnB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiAxMDA7XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiA2MnB4O1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICAgICAgcGFkZGluZy10b3A6IDEwMHB4O1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IHNwYWNlcig0KTtcclxuICAgICAgICBmbG9hdDogbGVmdDtcclxuICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7XHJcbiAgICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICAgIH1cclxuICAgICAgcCwgaDIge1xyXG4gICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMjBweDtcclxuICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICBwYWRkaW5nLXRvcDogc3BhY2VyKDIpO1xyXG4gICAgICAgIHBhZGRpbmctYm90dG9tOiA2MHB4O1xyXG4gICAgICB9XHJcbiAgICAgIGEge1xyXG4gICAgICAgIEBpbmNsdWRlIG5weC1idXR0b247XHJcblx0Zm9udC1zaXplOiAxOHB4O1xyXG4gICAgICAgIHBhZGRpbmc6IDIxcHggMjVweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG4uZnJvbnRjb2xvciB7XHJcbiAgY29sb3I6ICNmZmY7XHJcbn1cclxuIiwiLy8gQnJlYWtwb2ludCB2aWV3cG9ydCBzaXplcyBhbmQgbWVkaWEgcXVlcmllcy5cbi8vXG4vLyBCcmVha3BvaW50cyBhcmUgZGVmaW5lZCBhcyBhIG1hcCBvZiAobmFtZTogbWluaW11bSB3aWR0aCksIG9yZGVyIGZyb20gc21hbGwgdG8gbGFyZ2U6XG4vL1xuLy8gICAgKHhzOiAwLCBzbTogNTc2cHgsIG1kOiA3NjhweCwgbGc6IDk5MnB4LCB4bDogMTIwMHB4KVxuLy9cbi8vIFRoZSBtYXAgZGVmaW5lZCBpbiB0aGUgYCRncmlkLWJyZWFrcG9pbnRzYCBnbG9iYWwgdmFyaWFibGUgaXMgdXNlZCBhcyB0aGUgYCRicmVha3BvaW50c2AgYXJndW1lbnQgYnkgZGVmYXVsdC5cblxuLy8gTmFtZSBvZiB0aGUgbmV4dCBicmVha3BvaW50LCBvciBudWxsIGZvciB0aGUgbGFzdCBicmVha3BvaW50LlxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbmV4dChzbSlcbi8vICAgIG1kXG4vLyAgICA+PiBicmVha3BvaW50LW5leHQoc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBtZFxuLy8gICAgPj4gYnJlYWtwb2ludC1uZXh0KHNtLCAkYnJlYWtwb2ludC1uYW1lczogKHhzIHNtIG1kIGxnIHhsKSlcbi8vICAgIG1kXG5AZnVuY3Rpb24gYnJlYWtwb2ludC1uZXh0KCRuYW1lLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzLCAkYnJlYWtwb2ludC1uYW1lczogbWFwLWtleXMoJGJyZWFrcG9pbnRzKSkge1xuICAkbjogaW5kZXgoJGJyZWFrcG9pbnQtbmFtZXMsICRuYW1lKTtcbiAgQGlmIG5vdCAkbiB7XG4gICAgQGVycm9yIFwiYnJlYWtwb2ludCBgI3skbmFtZX1gIG5vdCBmb3VuZCBpbiBgI3skYnJlYWtwb2ludHN9YFwiO1xuICB9XG4gIEByZXR1cm4gaWYoJG4gPCBsZW5ndGgoJGJyZWFrcG9pbnQtbmFtZXMpLCBudGgoJGJyZWFrcG9pbnQtbmFtZXMsICRuICsgMSksIG51bGwpO1xufVxuXG4vLyBNaW5pbXVtIGJyZWFrcG9pbnQgd2lkdGguIE51bGwgZm9yIHRoZSBzbWFsbGVzdCAoZmlyc3QpIGJyZWFrcG9pbnQuXG4vL1xuLy8gICAgPj4gYnJlYWtwb2ludC1taW4oc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICA1NzZweFxuQGZ1bmN0aW9uIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IG1hcC1nZXQoJGJyZWFrcG9pbnRzLCAkbmFtZSk7XG4gIEByZXR1cm4gaWYoJG1pbiAhPSAwLCAkbWluLCBudWxsKTtcbn1cblxuLy8gTWF4aW11bSBicmVha3BvaW50IHdpZHRoLlxuLy8gVGhlIG1heGltdW0gdmFsdWUgaXMgcmVkdWNlZCBieSAwLjAycHggdG8gd29yayBhcm91bmQgdGhlIGxpbWl0YXRpb25zIG9mXG4vLyBgbWluLWAgYW5kIGBtYXgtYCBwcmVmaXhlcyBhbmQgdmlld3BvcnRzIHdpdGggZnJhY3Rpb25hbCB3aWR0aHMuXG4vLyBTZWUgaHR0cHM6Ly93d3cudzMub3JnL1RSL21lZGlhcXVlcmllcy00LyNtcS1taW4tbWF4XG4vLyBVc2VzIDAuMDJweCByYXRoZXIgdGhhbiAwLjAxcHggdG8gd29yayBhcm91bmQgYSBjdXJyZW50IHJvdW5kaW5nIGJ1ZyBpbiBTYWZhcmkuXG4vLyBTZWUgaHR0cHM6Ly9idWdzLndlYmtpdC5vcmcvc2hvd19idWcuY2dpP2lkPTE3ODI2MVxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtbWF4KG1kLCAoeHM6IDAsIHNtOiA1NzZweCwgbWQ6IDc2OHB4LCBsZzogOTkycHgsIHhsOiAxMjAwcHgpKVxuLy8gICAgNzY3Ljk4cHhcbkBmdW5jdGlvbiBicmVha3BvaW50LW1heCgkbmFtZSwgJGJyZWFrcG9pbnRzOiAkZ3JpZC1icmVha3BvaW50cykge1xuICAkbWF4OiBtYXAtZ2V0KCRicmVha3BvaW50cywgJG5hbWUpO1xuICBAcmV0dXJuIGlmKCRtYXggYW5kICRtYXggPiAwLCAkbWF4IC0gLjAyLCBudWxsKTtcbn1cblxuLy8gUmV0dXJucyBhIGJsYW5rIHN0cmluZyBpZiBzbWFsbGVzdCBicmVha3BvaW50LCBvdGhlcndpc2UgcmV0dXJucyB0aGUgbmFtZSB3aXRoIGEgZGFzaCBpbiBmcm9udC5cbi8vIFVzZWZ1bCBmb3IgbWFraW5nIHJlc3BvbnNpdmUgdXRpbGl0aWVzLlxuLy9cbi8vICAgID4+IGJyZWFrcG9pbnQtaW5maXgoeHMsICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBcIlwiICAoUmV0dXJucyBhIGJsYW5rIHN0cmluZylcbi8vICAgID4+IGJyZWFrcG9pbnQtaW5maXgoc20sICh4czogMCwgc206IDU3NnB4LCBtZDogNzY4cHgsIGxnOiA5OTJweCwgeGw6IDEyMDBweCkpXG4vLyAgICBcIi1zbVwiXG5AZnVuY3Rpb24gYnJlYWtwb2ludC1pbmZpeCgkbmFtZSwgJGJyZWFrcG9pbnRzOiAkZ3JpZC1icmVha3BvaW50cykge1xuICBAcmV0dXJuIGlmKGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpID09IG51bGwsIFwiXCIsIFwiLSN7JG5hbWV9XCIpO1xufVxuXG4vLyBNZWRpYSBvZiBhdCBsZWFzdCB0aGUgbWluaW11bSBicmVha3BvaW50IHdpZHRoLiBObyBxdWVyeSBmb3IgdGhlIHNtYWxsZXN0IGJyZWFrcG9pbnQuXG4vLyBNYWtlcyB0aGUgQGNvbnRlbnQgYXBwbHkgdG8gdGhlIGdpdmVuIGJyZWFrcG9pbnQgYW5kIHdpZGVyLlxuQG1peGluIG1lZGlhLWJyZWFrcG9pbnQtdXAoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogYnJlYWtwb2ludC1taW4oJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWluIHtcbiAgICBAbWVkaWEgKG1pbi13aWR0aDogJG1pbikge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSBvZiBhdCBtb3N0IHRoZSBtYXhpbXVtIGJyZWFrcG9pbnQgd2lkdGguIE5vIHF1ZXJ5IGZvciB0aGUgbGFyZ2VzdCBicmVha3BvaW50LlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50IGFuZCBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LWRvd24oJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJG5hbWUsICRicmVha3BvaW50cyk7XG4gIEBpZiAkbWF4IHtcbiAgICBAbWVkaWEgKG1heC13aWR0aDogJG1heCkge1xuICAgICAgQGNvbnRlbnQ7XG4gICAgfVxuICB9IEBlbHNlIHtcbiAgICBAY29udGVudDtcbiAgfVxufVxuXG4vLyBNZWRpYSB0aGF0IHNwYW5zIG11bHRpcGxlIGJyZWFrcG9pbnQgd2lkdGhzLlxuLy8gTWFrZXMgdGhlIEBjb250ZW50IGFwcGx5IGJldHdlZW4gdGhlIG1pbiBhbmQgbWF4IGJyZWFrcG9pbnRzXG5AbWl4aW4gbWVkaWEtYnJlYWtwb2ludC1iZXR3ZWVuKCRsb3dlciwgJHVwcGVyLCAkYnJlYWtwb2ludHM6ICRncmlkLWJyZWFrcG9pbnRzKSB7XG4gICRtaW46IGJyZWFrcG9pbnQtbWluKCRsb3dlciwgJGJyZWFrcG9pbnRzKTtcbiAgJG1heDogYnJlYWtwb2ludC1tYXgoJHVwcGVyLCAkYnJlYWtwb2ludHMpO1xuXG4gIEBpZiAkbWluICE9IG51bGwgYW5kICRtYXggIT0gbnVsbCB7XG4gICAgQG1lZGlhIChtaW4td2lkdGg6ICRtaW4pIGFuZCAobWF4LXdpZHRoOiAkbWF4KSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1heCA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKCRsb3dlciwgJGJyZWFrcG9pbnRzKSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1pbiA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LWRvd24oJHVwcGVyLCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfVxufVxuXG4vLyBNZWRpYSBiZXR3ZWVuIHRoZSBicmVha3BvaW50J3MgbWluaW11bSBhbmQgbWF4aW11bSB3aWR0aHMuXG4vLyBObyBtaW5pbXVtIGZvciB0aGUgc21hbGxlc3QgYnJlYWtwb2ludCwgYW5kIG5vIG1heGltdW0gZm9yIHRoZSBsYXJnZXN0IG9uZS5cbi8vIE1ha2VzIHRoZSBAY29udGVudCBhcHBseSBvbmx5IHRvIHRoZSBnaXZlbiBicmVha3BvaW50LCBub3Qgdmlld3BvcnRzIGFueSB3aWRlciBvciBuYXJyb3dlci5cbkBtaXhpbiBtZWRpYS1icmVha3BvaW50LW9ubHkoJG5hbWUsICRicmVha3BvaW50czogJGdyaWQtYnJlYWtwb2ludHMpIHtcbiAgJG1pbjogIGJyZWFrcG9pbnQtbWluKCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbmV4dDogYnJlYWtwb2ludC1uZXh0KCRuYW1lLCAkYnJlYWtwb2ludHMpO1xuICAkbWF4OiAgYnJlYWtwb2ludC1tYXgoJG5leHQpO1xuXG4gIEBpZiAkbWluICE9IG51bGwgYW5kICRtYXggIT0gbnVsbCB7XG4gICAgQG1lZGlhIChtaW4td2lkdGg6ICRtaW4pIGFuZCAobWF4LXdpZHRoOiAkbWF4KSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH0gQGVsc2UgaWYgJG1heCA9PSBudWxsIHtcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKCRuYW1lLCAkYnJlYWtwb2ludHMpIHtcbiAgICAgIEBjb250ZW50O1xuICAgIH1cbiAgfSBAZWxzZSBpZiAkbWluID09IG51bGwge1xuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtZG93bigkbmV4dCwgJGJyZWFrcG9pbnRzKSB7XG4gICAgICBAY29udGVudDtcbiAgICB9XG4gIH1cbn1cbiIsIkBtaXhpbiBucHgtYnV0dG9uIHtcclxuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICAtd2Via2l0LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgLW1vei1ib3gtc2hhZG93OiBub25lO1xyXG4gIC13ZWJraXQtYm94LXNoYWRvdzogbm9uZTtcclxuICBib3gtc2hhZG93OiBub25lO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBmb250LXdlaWdodDogNzAwO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBjb2xvcjogIzE5MTkxOTtcclxuICBsaW5lLWhlaWdodDogMjRweDtcclxuICBwYWRkaW5nOiAxMnB4IDMwcHg7XHJcbiAgd2lkdGg6IGF1dG87XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIG1hcmdpbjogMTBweCBhdXRvIDEwcHggMDtcclxuICAtbW96LXRyYW5zaXRpb246IGFsbCAuNXM7XHJcbiAgLW8tdHJhbnNpdGlvbjogYWxsIC41cztcclxuICAtd2Via2l0LXRyYW5zaXRpb246IGFsbCAuNXM7XHJcbiAgdHJhbnNpdGlvbjogYWxsIC41cztcclxuICBiYWNrZ3JvdW5kOiAkY29sb3ItdGVydGlhcnk7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIG1heC13aWR0aDogMTAwJTtcclxuICAmOmhvdmVyIHtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmFiMWE7XHJcbiAgICBjb2xvcjogIzE5MTkxOTtcclxuICB9XHJcbn1cclxuQG1peGluIG5weC1idXR0b24tZGFyayB7XHJcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gIHBhZGRpbmc6IDEycHggMzBweDtcclxuICBtYXJnaW46IDQ4cHggYXV0byAxNnB4O1xyXG4gIG1hcmdpbi10b3A6IDQ4cHg7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGhlaWdodDogNTBweDtcclxuICBsaW5lLWhlaWdodDogMjRweDtcclxuICBjb2xvcjogI2ZmZjtcclxuICBiYWNrZ3JvdW5kOiB2YXIoLS1zZWNvbmRhcnkpO1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAtbW96LWJveC1zaGFkb3c6IDAgMCAwIHRyYW5zcGFyZW50O1xyXG4gIC13ZWJraXQtYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XHJcbiAgYm94LXNoYWRvdzogMCAwIDAgdHJhbnNwYXJlbnQ7XHJcbiAgLW1vei10cmFuc2l0aW9uOiBhbGwgNTAwbXM7XHJcbiAgLW8tdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xyXG4gIC13ZWJraXQtdHJhbnNpdGlvbjogYWxsIDUwMG1zO1xyXG4gIHRyYW5zaXRpb246IGFsbCA1MDBtcztcclxuICAtbW96LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICAtd2Via2l0LWJvcmRlci1yYWRpdXM6IDRweDtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgd2hpdGUtc3BhY2U6IG5vcm1hbDtcclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMzRiN2Q7XHJcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICB9XHJcbn1cclxuXHJcbkBtaXhpbiBwYWdlLWJnIHtcclxuICBiYWNrZ3JvdW5kOiAjMDA1NkIzO1xyXG59XHJcbkBtaXhpbiBwYWdlLWJnLW5ldyB7XHJcbiAgYmFja2dyb3VuZDogIzE1NERCMjtcclxufVxyXG5AbWl4aW4gcGFnZS1iZy1jb3ZlciB7XHJcbiAgYmFja2dyb3VuZDogI2ZmZjtcclxufVxyXG5AbWl4aW4gbGltaXRlciB7XHJcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XHJcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xyXG4gIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgcGFkZGluZzogMCAyMHB4O1xyXG4gIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoc20pIHtcclxuICAgIG1heC13aWR0aDogNDUwcHg7XHJcbiAgfVxyXG4gIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAobWQpIHtcclxuICAgIG1heC13aWR0aDogNzIwcHg7XHJcbiAgfVxyXG4gIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAobGcpIHtcclxuICAgIG1heC13aWR0aDogOTYwcHg7XHJcbiAgfVxyXG4gIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoeGwpIHtcclxuICAgIG1heC13aWR0aDogMTIwMHB4O1xyXG4gIH1cclxuICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKGxkKSB7XHJcbiAgICBtYXgtd2lkdGg6IDEzNTBweDtcclxuICB9XHJcbiAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cCh4eGwpIHtcclxuICAgIG1heC13aWR0aDogJG1heC13aWR0aC1jb250YWluZXI7XHJcbiAgfVxyXG59XHJcbkBtaXhpbiBoMW90aGVyIHtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2FlYWViMDtcclxuICBmb250LXdlaWdodDogODAwO1xyXG4gIHBhZGRpbmc6IDIwcHggMDtcclxuICBtYXJnaW46IDVweCAwIDE2cHg7XHJcbiAgdGV4dC1hbGlnbjogbGVmdDtcclxuICBjb2xvcjogIzAwMDtcclxufVxyXG5AbWl4aW4gaDJvdGhlciB7XHJcbiAgbGluZS1oZWlnaHQ6IDEwMCU7XHJcbiAgcGFkZGluZzogMCAwIDIwcHg7XHJcbiAgbWFyZ2luOiAwIDAgMTIuOHB4O1xyXG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjYWVhZWIwO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKG1kKSB7XHJcbiAgICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gIH1cclxufVxyXG5AbWl4aW4gZnVsbC13aWR0aCB7XHJcbiAgd2lkdGg6IDEwMHZ3O1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBsZWZ0OiA1MCU7XHJcbiAgcmlnaHQ6IDUwJTtcclxuICBtYXJnaW4tbGVmdDogLTUwdnc7XHJcbiAgbWFyZ2luLXJpZ2h0OiAtNTB2dztcclxufVxyXG5cclxuQG1peGluIG5weC1idXR0b24tYmlnIHtcclxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGZvbnQtd2VpZ2h0OiAkaGVhZGluZ3MtZm9udC13ZWlnaHQ7XHJcbiAgZm9udC1zaXplOiAxOHB4O1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgbGV0dGVyLXNwYWNpbmc6IDA7XHJcbiAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgaGVpZ2h0OiAzcmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gIHRleHQtdHJhbnNmb3JtOiBub25lO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuQG1peGluIG5weC1idXR0b24tYmlnLXByaW1hcnkge1xyXG4gIEBpbmNsdWRlIG5weC1idXR0b24tYmlnO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICRjb2xvci1wcmltYXJ5O1xyXG4gIGNvbG9yOiAkd2hpdGU7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogJGNvbG9yLWJsdWUtZGFya2VyO1xyXG4gICAgY29sb3I6ICR3aGl0ZTtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICB9XHJcbn1cclxuXHJcbkBtaXhpbiBucHgtYnV0dG9uLWJpZy1zZWNvbmRhcnkge1xyXG4gIEBpbmNsdWRlIG5weC1idXR0b24tYmlnO1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNFM0YwRkY7XHJcbiAgY29sb3I6ICRjb2xvci1ibHVlLWRhcmtlcjtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjQ0VFNUZGO1xyXG4gICAgY29sb3I6ICRjb2xvci1ibHVlLWRhcmtlcjtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICB9XHJcbn1cclxuXHJcbkBtaXhpbiBucHgtYnV0dG9uLWJpZy10ZXJ0aWFyeSB7XHJcbiAgQGluY2x1ZGUgbnB4LWJ1dHRvbi1iaWc7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgY29sb3I6ICRjb2xvci1wcmltYXJ5O1xyXG4gIGJvcmRlcjogMnB4IHNvbGlkICRjb2xvci1wcmltYXJ5O1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgY29sb3I6ICRjb2xvci1ibHVlLWRhcmtlcjtcclxuICAgIGJvcmRlci1jb2xvcjogJGNvbG9yLWJsdWUtZGFya2VyO1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gIH1cclxufVxyXG5cclxuQG1peGluIG5weC1idXR0b24tYmlnLW9yYW5nZSB7XHJcbiAgQGluY2x1ZGUgbnB4LWJ1dHRvbi1iaWc7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogJGNvbG9yLW9yYW5nZS1idXR0b247XHJcbiAgY29sb3I6ICR3aGl0ZTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAkY29sb3Itb3JhbmdlLWJ1dHRvbjtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkY29sb3Itb3JhbmdlLWJ1dHRvbi1ob3ZlcjtcclxuICAgIGNvbG9yOiAkd2hpdGU7XHJcbiAgICBib3JkZXItY29sb3I6ICRjb2xvci1vcmFuZ2UtYnV0dG9uLWhvdmVyO1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gIH1cclxufVxyXG5cclxuQG1peGluIHQzYiB7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGNvbG9yOiAjMTgzODgxO1xyXG4gIGxpbmUtaGVpZ2h0OiAxLjI7XHJcbiAgZm9udC13ZWlnaHQ6IDcwMDtcclxufVxyXG5cclxuQG1peGluIHQzIHtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICBmb250LXdlaWdodDogNDAwO1xyXG59XHJcblxyXG5AbWl4aW4gdDJiIHtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICBsaW5lLWhlaWdodDogMS41O1xyXG59XHJcbiIsIiNibG9jay1ib290c3RyYXA0Z3Jvdy12aWV3cy1ibG9jay1hZHZhbnRhZ2VzLWJsb2NrLTEge1xyXG4gIHdpZHRoOiA5MCU7XHJcbiAgYmFja2dyb3VuZC1pbWFnZTogbm9uZTtcclxuICBwYWRkaW5nOiBzcGFjZXIoMCA1JSA2KTtcclxuICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKG1kKSAge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICB9XHJcbiAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChsZykge1xyXG4gICAgbWF4LXdpZHRoOiA5MDBweDtcclxuICB9XHJcbiAgLnZpZXctZm9vdGVyIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNhZWFlYjA7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gICAgY29sb3I6ICNhZWFlYjA7XHJcbiAgICBwYWRkaW5nOiBzcGFjZXIoMiAwIDUgMCk7XHJcbiAgfVxyXG4gIC52aWV3LWNvbnRlbnQge1xyXG4gICAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChzbSkge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgfVxyXG4gIH1cclxuICAudmlldy1oZWFkZXIge1xyXG4gICAgZm9udC1zaXplOiAyOXB4O1xyXG4gICAgbGluZS1oZWlnaHQ6IDMzcHg7XHJcbiAgICBmb250LWZhbWlseTogJHJvYm90bztcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDY1cHggMDtcclxuICAgIGNvbG9yOiAjMDE3M2JjO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBiIHtcclxuICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgIH1cclxuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAoc20pIHtcclxuICAgICAgZm9udC1zaXplOiAyLjNlbTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDFlbTtcclxuICAgIH1cclxuICB9XHJcbiAgLnZpZXdzLXJvdyB7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICB3aWR0aDogOTUlO1xyXG4gICAgcGFkZGluZzogMCAxLjI1JSA0NXB4IDEuMjUlO1xyXG4gICAgZGlzcGxheTogaW5saW5lLXRhYmxlO1xyXG4gICAgQGluY2x1ZGUgbWVkaWEtYnJlYWtwb2ludC11cChzbSkge1xyXG4gICAgICBtYXgtd2lkdGg6IDQ2JTtcclxuICAgICAgZmxleDogMSAwIDQ2JTtcclxuICAgIH1cclxuICAgIEBpbmNsdWRlIG1lZGlhLWJyZWFrcG9pbnQtdXAobWQpIHtcclxuICAgICAgbWF4LXdpZHRoOiAzMSU7XHJcbiAgICAgIGZsZXg6IDEgMCAzMSU7XHJcbiAgICB9XHJcbiAgfVxyXG4gIC52aWV3cy1yb3cge1xyXG4gICAgJjpudGgtY2hpbGQoMSkge1xyXG4gICAgICAudmlld3MtZmllbGQtZmllbGQtYyB7XHJcbiAgICAgICAgY29sb3I6ICNlZTFjMjU7XHJcbiAgICAgIH1cclxuICAgICAgLnZpZXdzLWZpZWxkLWZpZWxkLWNpcmNsZS0ge1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICNlZTFjMjU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgICY6bnRoLWNoaWxkKDIpIHtcclxuICAgICAgLnZpZXdzLWZpZWxkLWZpZWxkLWMge1xyXG4gICAgICAgIGNvbG9yOiAjMDE3M2JjO1xyXG4gICAgICB9XHJcbiAgICAgIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjMDE3M2JjO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICAmOm50aC1jaGlsZCgzKSB7XHJcbiAgICAgIC52aWV3cy1maWVsZC1maWVsZC1jIHtcclxuICAgICAgICBjb2xvcjogI2ZmYmQxMTtcclxuICAgICAgfVxyXG4gICAgICAudmlld3MtZmllbGQtZmllbGQtY2lyY2xlLSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2ZmYmQxMTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgJjpudGgtY2hpbGQoNCkge1xyXG4gICAgICAudmlld3MtZmllbGQtZmllbGQtYyB7XHJcbiAgICAgICAgY29sb3I6ICMwMThkNGU7XHJcbiAgICAgIH1cclxuICAgICAgLnZpZXdzLWZpZWxkLWZpZWxkLWNpcmNsZS0ge1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICMwMThkNGU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgICY6bnRoLWNoaWxkKDUpIHtcclxuICAgICAgLnZpZXdzLWZpZWxkLWZpZWxkLWMge1xyXG4gICAgICAgIGNvbG9yOiAjZjU3OTIxO1xyXG4gICAgICB9XHJcbiAgICAgIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjZjU3OTIxO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICAmOm50aC1jaGlsZCg2KSB7XHJcbiAgICAgIC52aWV3cy1maWVsZC1maWVsZC1jIHtcclxuICAgICAgICBjb2xvcjogIzAxYmNmMztcclxuICAgICAgfVxyXG4gICAgICAudmlld3MtZmllbGQtZmllbGQtY2lyY2xlLSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogIzAxYmNmMztcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuICAudmlld3MtZmllbGQtYm9keSB7XHJcbiAgICBmb250LXNpemU6ICRmb250LXNpemUtYmFzZTtcclxuICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICBsaW5lLWhlaWdodDogMTkuMnB4O1xyXG4gICAgcGFkZGluZy10b3A6IHNwYWNlcig1KTtcclxuICAgIG1heC13aWR0aDogMTAwJTtcclxuICAgIG1pbi1oZWlnaHQ6IDkwcHg7XHJcbiAgfVxyXG4gIC52aWV3cy1maWVsZC1maWVsZC1jaXJjbGUtIHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIHdpZHRoOiAxNTBweDtcclxuICAgIGhlaWdodDogMTUwcHg7XHJcbiAgICBib3JkZXItcmFkaXVzOiA3NXB4O1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBmb250LXNpemU6IDQ4cHg7XHJcbiAgICBmb250LXdlaWdodDogODAwO1xyXG4gICAgbWFyZ2luOiBhdXRvO1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgdG9wOiAtMjVweDtcclxuICAgICY6OmFmdGVyIHtcclxuICAgICAgcG9zaXRpb246IHN0YXRpYztcclxuICAgICAgY29udGVudDogXCJcIjtcclxuICAgICAgd2lkdGg6IDE1MHB4O1xyXG4gICAgICBoZWlnaHQ6IDgwcHg7XHJcbiAgICAgIGJhY2tncm91bmQ6IHVybChcIi4uL2ltYWdlcy9jaXJjbGUtc2hhZG93LnBuZ1wiKTtcclxuICAgICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcclxuICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyIDIwcHg7XHJcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICB9XHJcbiAgICAuZmllbGQtY29udGVudCBkaXYge1xyXG4gICAgICBwYWRkaW5nLXRvcDogc3BhY2VyKDcpO1xyXG4gICAgfVxyXG4gIH1cclxuICAudmlld3MtZmllbGQtZmllbGQtYyB7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBtYXJnaW4tbGVmdDogLTI1cHg7XHJcbiAgICBmb250LWZhbWlseTogJHJvYm90bztcclxuICAgIGZvbnQtd2VpZ2h0OiA4MDA7XHJcbiAgICBmbG9hdDogbGVmdDtcclxuICAgIHdpZHRoOiAxMDBweDtcclxuICAgIHRleHQtYWxpZ246IHJpZ2h0O1xyXG4gICAgbWFyZ2luLXRvcDogLTIwcHg7XHJcbiAgICBAaW5jbHVkZSBtZWRpYS1icmVha3BvaW50LXVwKHNtKSB7XHJcbiAgICAgIG1hcmdpbi1sZWZ0OiAtNTBweDtcclxuICAgICAgZm9udC1zaXplOiAxOHB4O1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXX0= */
