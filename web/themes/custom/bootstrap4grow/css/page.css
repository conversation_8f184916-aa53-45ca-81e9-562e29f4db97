/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.node--type-page.page-node-44 #page #main-wrapper {
  background: 0 0;
}
.node--type-page.page-node-44 #block-bootstrap4grow-content, .node--type-page.page-node-44 #block-bootstrap4grow-content a, .node--type-page.page-node-44 h1.title {
  color: #fff;
}
@media (min-width: 48rem) {
  .node--type-page.page-node-44 .field--name-body {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
  }
  .node--type-page.page-node-44 .kolumna {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
  }
}
@media (min-width: 62rem) {
  .node--type-page.page-node-44 .kolumna1, .node--type-page.page-node-44 .kolumna2 {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 30%;
        -ms-flex: 1 0 30%;
            flex: 1 0 30%;
  }
  .node--type-page.page-node-44 .kolumna3 {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 40%;
        -ms-flex: 1 0 40%;
            flex: 1 0 40%;
  }
}

.node--type-page:not(.path-frontpage) {
  color: #000;
}
.node--type-page:not(.path-frontpage) #page {
  background: #0056B3;
}
.node--type-page:not(.path-frontpage) .papper_wrapper, .node--type-page:not(.path-frontpage) #main-wrapper {
  background: #fff;
}
.node--type-page:not(.path-frontpage) h1 {
  border-bottom: 1px solid #aeaeb0;
  font-weight: 800;
  padding: 1.25rem 0;
  margin: 0.3125rem 0 1rem;
  text-align: left;
  color: #000;
}
.node--type-page:not(.path-frontpage) #block-bootstrap4grow-content h2 {
  line-height: 100%;
  padding: 0 0 1.25rem;
  text-align: center;
  margin-top: 2rem;
}
.node--type-page:not(.path-frontpage) #block-bootstrap4grow-content h3 {
  text-align: center;
  margin-top: 0.5rem;
}
@media (min-width: 48rem) {
  .node--type-page:not(.path-frontpage) #block-bootstrap4grow-content h3 {
    text-align: left;
  }
}
.node--type-page:not(.path-frontpage) .npx-link-button-style a {
  width: 100%;
}
@media (min-width: 36rem) {
  .node--type-page:not(.path-frontpage) .npx-link-button-style a {
    width: auto;
  }
}
.node--type-page:not(.path-frontpage) a:not(.btn-blue) {
  color: #4c4c4c;
}
.node--type-page:not(.path-frontpage) .npx-link-button-style a {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  width: 100%;
}
.node--type-page:not(.path-frontpage) .npx-link-button-style a:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}
@media (min-width: 36rem) {
  .node--type-page:not(.path-frontpage) .npx-link-button-style a {
    width: 90%;
    max-width: 400px;
  }
}

.field--name-field-teaser-image {
  text-align: center;
}

.field--name-field-gallery-images {
  text-align: center;
}
@media (min-width: 48rem) {
  .field--name-field-gallery-images {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
    max-width: 880px;
  }
}
@media (min-width: 48rem) {
  .field--name-field-gallery-images .field__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 62rem) {
  .field--name-field-gallery-images .field__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}

.btn-blue {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  margin: 1rem auto;
  display: table;
}
.btn-blue:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}

.fc-title {
  white-space: normal;
}

.page-node-5545 .notification {
  background: #fff;
}
.page-node-5545 .notification .toast-wrapper {
  display: block !important;
  margin: 0 auto;
  max-width: 1415px !important;
}
.page-node-5545 .notification .toast.fade:not(.show) {
  opacity: 1;
  border-radius: 3px;
  padding: 1rem 1rem 1rem 2rem;
  border: #c9e1bd 1px solid;
  background-color: #f3faef;
  background-image: url(/core/themes/classy/css/components/../../../../misc/icons/73b355/check.svg);
  background-repeat: no-repeat;
  background-position-y: center;
  margin-top: 3rem;
  background-position-x: 0.5rem;
}
.page-node-5545 .notification .toast-header {
  display: none;
}

#block-views-block-strona-o-firmie-zespol-zajawki-block-1 .views-field-field-npxtrainer-position {
  font-size: 0.9375rem;
  text-align: left;
  color: #939598;
  line-height: 1rem;
}
#block-views-block-strona-o-firmie-zespol-zajawki-block-1 .views-field-title a {
  color: #000;
  font-size: 1.125rem;
  font-weight: 500;
}
@media (min-width: 48rem) {
  #block-views-block-strona-o-firmie-zespol-zajawki-block-1 .view-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
}
#block-views-block-strona-o-firmie-zespol-zajawki-block-1 .views-row {
  margin-bottom: 2.5rem;
}
@media (min-width: 48rem) {
  #block-views-block-strona-o-firmie-zespol-zajawki-block-1 .views-row {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
    max-width: 33%;
  }
}

#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .image {
  margin-bottom: 1rem;
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .referencja {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .referencja {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
    border: 1px solid #e8e8e8;
    padding: 2.125rem;
    margin: 0 0 1.375rem;
  }
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .ds-3col-stacked-fluid > .group-right {
  width: 75%;
  margin-left: auto;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .ds-3col-stacked-fluid > .group-right {
    width: 25%;
  }
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .ds-3col-stacked-fluid.group-one-sidebar > .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .ds-3col-stacked-fluid.group-one-sidebar > .group-middle {
    width: 75%;
  }
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 a {
  color: #4c4c4c;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .group-right {
    padding-left: 1rem;
  }
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .group-middle {
    border-right: 1px solid #f59c21;
  }
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .group-middle::after {
  content: "";
  display: block;
  width: 100%;
  height: 33px;
  margin: 1.5625rem 0 0.625rem;
  background: url(../images/orange-border.png) no-repeat right;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .group-middle::after {
    display: none;
  }
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .opinia.view-mode-trener {
  border: 1px solid #e8e8e8;
  padding: 0 2.125rem 2.125rem;
  margin: 0 0 1.375rem;
}
#block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .opinia.view-mode-trener .group-footer {
  display: none;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .field-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
  }
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .field-content .left {
    width: 75%;
    padding-right: 1rem;
  }
  #block-bootstrap4grow-views-block-strona-o-firmie-zespol-zajawki-block-2 .field-content .right {
    width: 25%;
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

#block-bootstrap4grow-logotypyanimacja img {
  padding: 0 1rem;
  width: 49%;
}
@media (min-width: 36rem) {
  #block-bootstrap4grow-logotypyanimacja img {
    width: 32%;
  }
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-logotypyanimacja img {
    width: 24%;
  }
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-logotypyanimacja img {
    width: 19.5%;
  }
}
@media (min-width: 75rem) {
  #block-bootstrap4grow-logotypyanimacja img {
    width: 9.5%;
  }
}

#block-bootstrap4grow-listaklientow {
  margin-bottom: 1.875rem;
}
#block-bootstrap4grow-listaklientow h2 {
  display: block;
  line-height: 3.125rem;
  font-weight: 700;
  margin: 0;
  padding: 1.25rem 0;
  color: #1e3850;
}
#block-bootstrap4grow-listaklientow table {
  display: block;
  margin: 0 1.5625rem;
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-listaklientow table {
    display: table;
    margin: inherit;
  }
}
#block-bootstrap4grow-listaklientow tbody, #block-bootstrap4grow-listaklientow tr, #block-bootstrap4grow-listaklientow td {
  display: block;
  width: 100%;
}
#block-bootstrap4grow-listaklientow td {
  vertical-align: top;
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-listaklientow td {
    width: 33%;
    float: left;
  }
}
#block-bootstrap4grow-listaklientow ul {
  margin: 0.625rem 0.625rem 1.25rem 1.875rem;
  padding: 0;
}
#block-bootstrap4grow-listaklientow li {
  background: transparent url("../images/punktor1a.png") no-repeat scroll left center;
  font-size: 1.125rem;
  line-height: 130%;
  list-style: outside none none;
  margin: 0 0 0.625rem;
  padding: 0.1875rem 0 0.1875rem 1.25rem;
  position: relative;
}
#block-bootstrap4grow-listaklientow li strong {
  position: absolute;
  right: 100%;
  top: 0;
  font-size: 2.125rem;
  font-weight: normal;
  color: #1e3850;
  display: block;
  margin-top: -0.0625rem;
  margin-right: 0.625rem;
}

@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 .view-content.row, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .view-content.row {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
  }
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 .views-row, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .views-row {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 75rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 .views-row, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .views-row {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
    max-width: 33%;
  }
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-field-image img, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-field-image img {
  width: 100%;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-node-title h2, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-node-title h2 {
    text-align: left;
  }
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-node-title h2 a, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-node-title h2 a {
  color: #005cb9;
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-node-link, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-node-link {
  text-align: center;
  font-weight: 700;
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-node-link a, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-node-link a {
  color: #005cb9;
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 img, #block-bootstrap4grow-views-block-npx-blog-list-block-2 img {
    padding-right: 1rem;
  }
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 img.align-left, #block-bootstrap4grow-views-block-npx-blog-list-block-2 img.align-left {
    max-width: 50%;
  }
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 img.align-left, #block-bootstrap4grow-views-block-npx-blog-list-block-2 img.align-left {
    max-width: 70%;
  }
}
@media (min-width: 48rem) {
  #block-bootstrap4grow-views-block-npx-blog-list-block-1 h3, #block-bootstrap4grow-views-block-npx-blog-list-block-2 h3 {
    clear: both;
    padding-top: 1rem;
  }
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 ul, #block-bootstrap4grow-views-block-npx-blog-list-block-2 ul {
  padding-left: 0;
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-field-image, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-field-image {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 auto;
      -ms-flex: 1 0 auto;
          flex: 1 0 auto;
}
#block-bootstrap4grow-views-block-npx-blog-list-block-1 .field--name-field-trailer p, #block-bootstrap4grow-views-block-npx-blog-list-block-2 .field--name-field-trailer p {
  font-size: 1.25rem;
}

.pagination {
  margin-top: 1rem;
  display: block;
}
@media (min-width: 62rem) {
  .pagination {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.pagination .sr-only {
  display: none;
}
.pagination .page-item, .pagination .pager__item--next {
  display: inline-block;
  padding: 0.5rem;
}

#pagination-heading {
  display: none;
}

.simple-popup-blocks-global .spb_center {
  margin-left: -25rem;
  margin-top: -12.5rem;
}

#block-bootstrap4grow-freshmailpopupexitstronazcytatami {
  display: none;
}

.popover {
  z-index: 999999 !important;
}
.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
}
.popover a {
  text-decoration: underline;
}

.npx-freshmail-block-layout-2-wrapper {
  max-width: 1200px;
  color: #000;
}
.npx-freshmail-block-layout-2-wrapper .form-type-email, .npx-freshmail-block-layout-2-wrapper .form-type-textfield {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
.npx-freshmail-block-layout-2-wrapper .form-type-email {
  margin-right: 1rem;
}
.npx-freshmail-block-layout-2-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.npx-freshmail-block-layout-2-wrapper .nxp-top-container h2 {
  margin: 0;
  color: var(--secondary);
  font-size: 3rem;
  font-weight: normal;
  line-height: 3rem;
}
.npx-freshmail-block-layout-2-wrapper .nxp-top-container h3 {
  margin: 1rem 0 !important;
  color: #000 !important;
  font-size: 1.3125rem !important;
}
.npx-freshmail-block-layout-2-wrapper .nxp-top-container p {
  color: #000;
}
.npx-freshmail-block-layout-2-wrapper .nxp-top-container hr {
  width: 60%;
  margin-bottom: 0;
}
.npx-freshmail-block-layout-2-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-freshmail-block-layout-2-wrapper .npx-left-column img {
  max-width: 200px;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .form-submit,
.npx-freshmail-block-layout-2-wrapper .npx-right-column form #edit-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .form-submit:hover,
.npx-freshmail-block-layout-2-wrapper .npx-right-column form #edit-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper .form-item {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper .form-text {
  color: #808080;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper .form-email {
  color: #808080;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-input-fields-wrapper input.error {
  border: 2px solid red !important;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-freshmail-list-id .fieldset-legend {
  margin-bottom: 0.625rem;
  display: inline-block;
  font-size: 1.1rem;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .js-form-type-checkbox input {
  display: none;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .js-form-type-checkbox {
  margin: 0;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: normal;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on-blue.png");
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form fieldset.npx-freshmail-list-id {
  margin-bottom: 0;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-freshmail-list-id legend span {
  color: #000;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-freshmail-list-id .form-checkboxes input.error + label {
  color: red;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .npx-freshmail-accept.form-checkbox.error + label a {
  color: red;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 15px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: bold;
  pointer-events: none;
}
.npx-freshmail-block-layout-2-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 320px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: normal;
  line-height: 0.875rem;
  font-size: 0.75rem;
}
.npx-freshmail-block-layout-2-wrapper .npx-msg {
  display: none;
}
.npx-freshmail-block-layout-2-wrapper .ajax-progress.ajax-progress-throbber {
  background: transparent;
}

.npx-freshmail-block-layout-2-wrapper.npx-freshmail-form-sent .npx-msg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  min-height: 300px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1.5rem;
  background: url("../images/Icon_exit_TYP.png") center 60px no-repeat;
  color: #909090;
}
.npx-freshmail-block-layout-2-wrapper.npx-freshmail-form-sent .npx-msg h3 {
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: normal;
  font-style: italic;
  color: #909090;
}
.npx-freshmail-block-layout-2-wrapper.npx-freshmail-form-sent .npx-msg p {
  margin-top: 0;
  font-size: 1.2rem;
}
.npx-freshmail-block-layout-2-wrapper.npx-freshmail-form-sent .nxp-top-container {
  display: none !important;
}
.npx-freshmail-block-layout-2-wrapper.npx-freshmail-form-sent .nxp-columns-container {
  display: none !important;
}

.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
}

.npx-freshmail-block-layout-1-background {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#00336b+0,005ec5+100 */
  background: #00336b;
  /* Old browsers */
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, #00336b 0%, #005ec5 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: -webkit-gradient(linear, left top, right top, from(#00336b), to(#005ec5));
  background: -o-linear-gradient(left, #00336b 0%, #005ec5 100%);
  background: linear-gradient(to right, #00336b 0%, #005ec5 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#00336b", endColorstr="#005ec5",GradientType=1 );
  /* IE6-9 */
}

.npx-freshmail-block-layout-1-wrapper {
  max-width: 1200px;
}
.npx-freshmail-block-layout-1-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-freshmail-block-layout-1-wrapper .npx-left-column img {
  max-width: 200px;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column h4 {
  margin: 0.25rem 0;
  color: #69b2fd;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column h2 {
  margin: 0.25rem 0;
  color: #fff;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .form-submit,
.npx-freshmail-block-layout-1-wrapper .npx-right-column form #edit-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .form-submit:hover,
.npx-freshmail-block-layout-1-wrapper .npx-right-column form #edit-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper .form-item {
  max-width: 200px;
  margin-right: 1.25rem;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper .form-text {
  color: #808080;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper .form-email {
  color: #808080;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-input-fields-wrapper input.error {
  border: 2px solid red !important;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-freshmail-list-id .fieldset-legend {
  margin-bottom: 0.625rem;
  display: inline-block;
  font-size: 1.1rem;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .js-form-type-checkbox {
  margin: 0;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .js-form-type-checkbox input {
  display: none;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on-blue.png");
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form fieldset.npx-freshmail-list-id {
  margin-bottom: 0;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-freshmail-list-id legend span {
  color: #69b2fd;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-freshmail-list-id .form-checkboxes input.error + label {
  color: red;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-freshmail-accept + label a {
  color: #fff;
  text-decoration: underline;
  cursor: pointer;
}
.npx-freshmail-block-layout-1-wrapper .npx-right-column form .npx-freshmail-accept.form-checkbox.error + label a {
  color: red;
}
.npx-freshmail-block-layout-1-wrapper .npx-msg {
  display: none;
}

.npx-freshmail-block-layout-1-wrapper.npx-freshmail-form-sent .npx-msg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  min-height: 300px;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1.5rem;
  background: url("../images/Icon_newsletter_scroll_TYP.png") center 60px no-repeat;
}
.npx-freshmail-block-layout-1-wrapper.npx-freshmail-form-sent .npx-msg h3 {
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: normal;
  font-style: italic;
  color: #909090;
}
.npx-freshmail-block-layout-1-wrapper.npx-freshmail-form-sent .npx-msg p {
  margin-top: 0;
  font-size: 1.2rem;
}
.npx-freshmail-block-layout-1-wrapper.npx-freshmail-form-sent .npx-left-column, .npx-freshmail-block-layout-1-wrapper.npx-freshmail-form-sent .npx-right-column {
  display: none;
}

.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
  background: 0;
  border: 0;
}
.popover .popover-body {
  font-size: 0.8125rem;
}

.spb-popup-main-wrapper.spb_center {
  width: 800px !important;
  margin: 0 auto !important;
  -webkit-transform: translate(-50%, -45%);
       -o-transform: translate(-50%, -45%);
          transform: translate(-50%, -45%);
  background-color: #eceeef;
  border: #666 1px solid;
  border-radius: 8px;
  max-height: 100vh;
  overflow-y: auto;
}
@media (max-width: 47.99875rem) {
  .spb-popup-main-wrapper.spb_center {
    max-width: 98%;
  }
}

.spb_close {
  border: none;
  background: transparent;
  font-size: 1.875rem;
}

@media (max-width: 61.99875rem) {
  .block-bootstrap4grow-freshmailpopupexitstronazcytatami-modal.spb_overlay {
    display: none;
  }
}

.sekcja.wyznaczamy-standardy .npx-form-button-wrapper, body.node--type-page .npx-form-button-wrapper {
  text-align: center;
}
.sekcja.wyznaczamy-standardy a.npx-form-button.npx-autolink, .sekcja.wyznaczamy-standardy a.npx-form-button-inline.npx-autolink, body.node--type-page a.npx-form-button.npx-autolink, body.node--type-page a.npx-form-button-inline.npx-autolink {
  margin-top: 0;
}
.sekcja.wyznaczamy-standardy .field--name-field-extra-tekst-g3 p.npx-hidden-text, body.node--type-page .field--name-field-extra-tekst-g3 p.npx-hidden-text {
  font-size: 1rem;
  font-weight: 300;
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
  width: 100%;
  height: 100px;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 128px;
    height: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 182px;
    height: 182px;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
  max-width: 100px;
  display: inline-block;
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    display: inherit;
    max-width: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    max-width: 100%;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, .sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::after {
  position: absolute;
  content: " ";
  display: block;
  left: -20px;
  top: 0;
  width: calc(100% + 40px);
  height: 100%;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
  z-index: 0;
  background: transparent url(../images/wyznaczamy-standardy-1.png) repeat-y 0 0;
  -webkit-background-size: 150% auto;
          background-size: 150% auto;
}
@media (max-width: 74.99875rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    background-position: -100px 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    position: absolute;
  }
}
@media (min-width: 75rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    -webkit-background-size: 100% auto;
            background-size: 100% auto;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::after {
  z-index: 1;
  background: transparent url("../images/wyznaczamy-standardy-2.png") repeat-x left bottom;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item, body.node--type-page .field--name-field-cechy .field__item {
  z-index: 10;
  clear: both;
  margin-bottom: 1.5625rem;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
    margin-bottom: 1.5625rem;
  }
}
.sekcja.wyznaczamy-standardy .paragraph--type-cecha-par, body.node--type-page .paragraph--type-cecha-par {
  max-width: 900px;
}

.field_cechy_field_item_even .paragraph--type-cecha-par {
  margin-left: auto;
}

.paragraph--type-cecha-par .field--name-field-opis-trenera {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 128px);
    padding: 0 0 0 3.125rem;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 182px);
    padding: 0 0 0 6.25rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera p {
  display: inline-block;
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
  position: relative;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.625rem;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -2.8125rem;
    text-align: left;
    margin-bottom: 0;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -5rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
  display: block;
  max-width: 100px;
  margin: 1.875rem auto;
  padding: 0 0.625rem;
  vertical-align: middle;
  line-height: 1.25rem;
  font-size: 0.625rem;
  color: #fff;
  background-color: #f73965;
  position: relative;
  height: 20px;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
    display: inline-block;
    margin-top: 0;
    margin-left: 0.3125rem;
    vertical-align: sub;
  }
}

a.benefits-linker {
  font-weight: bold;
  line-height: 1.9375rem;
  color: #000;
}
a.benefits-linker:hover, a.benefits-linker:active {
  color: #000;
}
@media (min-width: 36rem) {
  a.benefits-linker {
    margin-left: -2.8125rem;
  }
}
@media (min-width: 62rem) {
  a.benefits-linker {
    margin-left: -5rem;
  }
}

.g17.sekcja.szybki-kontakt {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}

#szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .no-margin .js-form-type-checkbox {
  margin-left: 0.75rem;
}
@media (max-width: 35.99875rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item {
    text-align: center;
  }
}
@media (min-width: 48rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-top: 0.3125rem;
  }
}
@media (min-width: 68.8125rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item, #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > input {
    width: 33%;
  }
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-left: 1.5rem;
    margin-top: 2rem;
  }
}

#npx-contact-form-wrapper {
  max-width: 1100px;
}
#npx-contact-form-wrapper .form-type-textfield label {
  width: 100%;
}
#npx-contact-form-wrapper #edit-right-col-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-contact-form-wrapper #edit-right-col-row fieldset {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    margin-left: 0;
    margin-right: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset.js-form-item-name {
    padding-left: 0;
  }
}
#npx-contact-form-wrapper .no-margin {
  margin: 0;
  padding: 0;
}
#npx-contact-form-wrapper fieldset {
  max-width: 250px;
}
#npx-contact-form-wrapper .error + div .field-suffix {
  display: block;
}
#npx-contact-form-wrapper .error {
  border-color: red;
}
#npx-contact-form-wrapper .form-checkbox.error + label {
  color: red;
}
#npx-contact-form-wrapper .field-suffix {
  display: none;
  font-size: 0.8125rem;
}
#npx-contact-form-wrapper .npx-contact-thx img {
  height: 60px;
  margin-right: 1.25rem;
}
#npx-contact-form-wrapper .npx-contact-thx .n-big {
  font-size: 1.5rem;
}
#npx-contact-form-wrapper .npx-contact-txt-info {
  margin-bottom: 1rem;
  margin-top: 2.2rem;
  font-size: 1.375rem;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .npx-contact-txt-info {
    margin-bottom: inherit;
  }
}
#npx-contact-form-wrapper .npx-contact-txt-info .n-sm {
  font-size: 1.125rem;
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col {
    position: relative;
  }
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col::before {
    content: "";
    position: absolute;
    top: 45px;
    left: -75px;
    width: 73px;
    height: 78px;
    background: url("../images/call_question_icon.svg") left center no-repeat;
  }
}
#npx-contact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
#npx-contact-form-wrapper .right-col .row {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .right-col .row {
    -webkit-box-pack: inherit;
    -webkit-justify-content: inherit;
        -ms-flex-pack: inherit;
            justify-content: inherit;
  }
}
#npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  max-width: 210px;
}
#npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 1rem;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-right: 0;
    margin-left: calc(var(--bs-gutter-x) * 0.5);
  }
}
#npx-contact-form-wrapper .button.form-submit::hover {
  background-color: #034b7d;
  text-decoration: none;
}
#npx-contact-form-wrapper .messages {
  display: none !important;
}
#npx-contact-form-wrapper .ajax-progress-throbber {
  background: transparent;
}
#npx-contact-form-wrapper .form-item-phone input {
  background: #fff url("../images/phone_icon.svg") left center no-repeat;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
#npx-contact-form-wrapper .form-item-name input {
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: 8px;
}
#npx-contact-form-wrapper .required-info {
  font-size: 0.75rem;
  text-align: right;
  max-width: 135px;
  padding-right: 0;
}
#npx-contact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
#npx-contact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3 {
    margin-bottom: 1.5625rem;
  }
}

.field__item:nth-of-type(even) .paragraph--type-cecha-par {
  margin-left: auto;
}

.field__item .paragraph--type-cecha-par {
  margin-bottom: 1.5625rem;
}

body.node--type-page .block-npx-contact-block {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding-top: 4rem;
  background: #ecedef;
}
body.node--type-page .block-npx-contact-block #npx-contact-form-wrapper .button.form-submit {
  margin-top: 0;
}
body.node--type-page .field--name-body {
  padding-bottom: 3rem;
}

.n-breadcrumb.inner {
  padding-left: 0;
  padding-right: 0;
}

.field--name-dynamic-block-fieldnode-ds-npx-breadcrumb {
  border-bottom: 1px solid #d0d8db;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
