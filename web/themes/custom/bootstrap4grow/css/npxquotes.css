/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.block-quotes-menu-block {
  margin-bottom: 1rem;
}
.block-quotes-menu-block .quotes-title {
  margin: 3rem 0 1.5rem 0;
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1;
}
.block-quotes-menu-block .item-list {
  margin-top: 3rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
  border-bottom: 1px solid #dee2e6;
}
.block-quotes-menu-block ul.list-group {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 2rem;
}
@media (max-width: 61.99875rem) {
  .block-quotes-menu-block ul.list-group {
    gap: 0.75rem;
  }
}
@media (max-width: 59.375rem) and (min-width: 48.0625rem) {
  .block-quotes-menu-block ul.list-group {
    gap: 0.25rem;
  }
}
@media (max-width: 47.99875rem) {
  .block-quotes-menu-block ul.list-group {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0.5rem;
  }
}
.block-quotes-menu-block ul.list-group .list-group-item {
  border: none;
  position: relative;
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
  min-width: 0;
}
.block-quotes-menu-block ul.list-group .list-group-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -1rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background-color: #dee2e6;
}
@media (max-width: 61.99875rem) {
  .block-quotes-menu-block ul.list-group .list-group-item:not(:last-child)::after {
    right: -0.375rem;
  }
}
@media (max-width: 59.375rem) and (min-width: 48.0625rem) {
  .block-quotes-menu-block ul.list-group .list-group-item:not(:last-child)::after {
    right: -0.125rem;
  }
}
@media (max-width: 47.99875rem) {
  .block-quotes-menu-block ul.list-group .list-group-item::after {
    display: none;
  }
}
.block-quotes-menu-block ul.list-group .list-group-item a {
  text-decoration: none;
  color: #343a40;
  text-transform: uppercase;
  font-size: 0.875rem;
  font-weight: 600;
  display: block;
  text-align: center;
  padding: 1rem 0;
  white-space: nowrap;
}
@media (max-width: 74.99875rem) {
  .block-quotes-menu-block ul.list-group .list-group-item a {
    font-size: 0.8125rem;
  }
}
@media (max-width: 61.99875rem) {
  .block-quotes-menu-block ul.list-group .list-group-item a {
    font-size: 0.75rem;
    white-space: normal;
    line-height: 1.2;
    padding: 0.75rem 0.25rem;
  }
}
@media (max-width: 59.375rem) and (min-width: 48.0625rem) {
  .block-quotes-menu-block ul.list-group .list-group-item a {
    font-size: 0.6875rem;
    padding: 0.5rem 0.125rem;
  }
  .block-quotes-menu-block ul.list-group .list-group-item a .menu-icon, .block-quotes-menu-block ul.list-group .list-group-item a .menu-text {
    display: block;
    text-align: center;
  }
  .block-quotes-menu-block ul.list-group .list-group-item a .menu-icon img {
    margin-right: 0;
    margin-bottom: 0.125rem;
  }
}
@media (max-width: 47.99875rem) {
  .block-quotes-menu-block ul.list-group .list-group-item a {
    padding: 0.5rem 0;
    font-size: 0.875rem;
  }
  .block-quotes-menu-block ul.list-group .list-group-item a .menu-icon, .block-quotes-menu-block ul.list-group .list-group-item a .menu-text {
    display: inline-block;
  }
  .block-quotes-menu-block ul.list-group .list-group-item a .menu-icon img {
    margin-right: 0.5rem;
    margin-bottom: 0;
  }
}
.block-quotes-menu-block ul.list-group .list-group-item a .menu-icon {
  position: relative;
  bottom: 2px;
}
.block-quotes-menu-block ul.list-group .list-group-item a .menu-icon img {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
}
.block-quotes-menu-block ul.list-group .list-group-item a .menu-text {
  display: inline-block;
}
.block-quotes-menu-block ul.list-group .list-group-item a:hover, .block-quotes-menu-block ul.list-group .list-group-item a.active {
  color: #183881;
}
.block-quotes-menu-block ul.list-group .list-group-item a:hover .menu-icon img, .block-quotes-menu-block ul.list-group .list-group-item a.active .menu-icon img {
  -webkit-filter: brightness(0) saturate(100%) invert(31%) sepia(92%) saturate(2108%) hue-rotate(205deg) brightness(89%) contrast(94%);
          filter: brightness(0) saturate(100%) invert(31%) sepia(92%) saturate(2108%) hue-rotate(205deg) brightness(89%) contrast(94%);
}
.block-quotes-menu-block .quotes-info-text {
  margin: 1.5rem 0 2rem 0;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0;
  color: #343a40;
}
.block-quotes-menu-block .quotes-info-text a {
  color: #343a40;
  text-decoration: underline;
}

.quotes-banner {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  height: 200px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.quotes-banner.banner-blue {
  background-image: url("/themes/custom/bootstrap4grow/images/banners/blue-pattern.png");
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
}
.quotes-banner.banner-yellow {
  background-image: url("/themes/custom/bootstrap4grow/images/banners/yellow-pattern.png");
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
}
.quotes-banner .banner-container {
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 10rem;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (min-width: 100rem) {
  .quotes-banner .banner-container {
    max-width: 1415px;
  }
}
@media (max-width: 47.99875rem) {
  .quotes-banner .banner-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
    text-align: center;
    gap: 2rem;
    padding: 0;
  }
}
.quotes-banner .banner-content {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 47.99875rem) {
  .quotes-banner .banner-content {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
    text-align: center;
  }
}
.quotes-banner .banner-content .banner-text-and-button {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 1.5rem;
}
@media (max-width: 47.99875rem) {
  .quotes-banner .banner-content .banner-text-and-button {
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
  }
}
.quotes-banner .banner-content .banner-text-and-button .banner-title {
  font-size: 2rem;
  font-weight: 700;
  color: #000000;
  line-height: 1.1;
  margin: 0;
}
.quotes-banner .banner-content .banner-text-and-button .banner-button-with-logo {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1rem;
}
.quotes-banner .banner-content .banner-text-and-button .banner-button-with-logo .btn-banner {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 0.5rem 1rem;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  background-color: #79B5FF;
  color: #183881;
  margin: 0;
}
.quotes-banner .banner-content .banner-text-and-button .banner-button-with-logo .banner-logo-img {
  height: 3rem;
  width: auto;
}
.quotes-banner.banner-yellow .banner-content .btn-banner {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 0.5rem 1rem;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  background-color: #FF9900 !important;
  color: #fff !important;
  border: 1px solid #FF9900 !important;
}

.view-npx-quotes .views-field-rendered-entity,
.view-quotes-per-categories .view-content.row {
  margin-left: 0;
}
.view-npx-quotes .views-field-rendered-entity .views-row,
.view-quotes-per-categories .view-content.row .views-row {
  margin: 0.75rem 0;
  line-height: 1.4;
  padding: 1rem 0.5rem;
  font-size: 1rem;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto auto;
  gap: 0 0.5rem;
  background-color: #F5F5F5;
  border-left: 4px solid #F2CA10;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.view-npx-quotes .views-field-rendered-entity .views-field,
.view-quotes-per-categories .view-content.row .views-field {
  margin: 0;
}
.view-npx-quotes .views-field-rendered-entity .views-field-body,
.view-quotes-per-categories .view-content.row .views-field-body {
  grid-column: 2;
  grid-row: 1;
}
.view-npx-quotes .views-field-rendered-entity .views-field-body .field-content,
.view-quotes-per-categories .view-content.row .views-field-body .field-content {
  font-size: 1.125rem;
  font-weight: 700;
  line-height: 1.5;
}
.view-npx-quotes .views-field-rendered-entity .views-field-field-quote-author,
.view-quotes-per-categories .view-content.row .views-field-field-quote-author {
  grid-column: 2;
  grid-row: 2;
  margin-top: 0.3125rem;
  text-align: left;
}
.view-npx-quotes .views-field-rendered-entity .views-field-field-quote-author .field-content,
.view-quotes-per-categories .view-content.row .views-field-field-quote-author .field-content {
  font-size: 1.125rem;
  line-height: 1.5;
  font-weight: 400;
}

.quotes-category-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.1;
}

.quotes-category-section .view-content {
  position: relative;
}
.quotes-category-section:has(.more-quotes-button-wrapper) .view-content .views-row:last-child, .quotes-category-section.has-more-button .view-content .views-row:last-child {
  position: relative;
}
.quotes-category-section:has(.more-quotes-button-wrapper) .view-content .views-row:last-child .views-field, .quotes-category-section.has-more-button .view-content .views-row:last-child .views-field {
  -webkit-filter: blur(0.3px);
          filter: blur(0.3px);
  opacity: 0.8;
}
.quotes-category-section:has(.more-quotes-button-wrapper) .view-content .views-row:last-child::after, .quotes-category-section.has-more-button .view-content .views-row:last-child::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), color-stop(30%, rgba(255, 255, 255, 0.2)), color-stop(70%, rgba(255, 255, 255, 0.6)), to(rgba(255, 255, 255, 0.8)));
  background: -webkit-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.6) 70%, rgba(255, 255, 255, 0.8) 100%);
  background: -o-linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.6) 70%, rgba(255, 255, 255, 0.8) 100%);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.6) 70%, rgba(255, 255, 255, 0.8) 100%);
  pointer-events: none;
  z-index: 1;
}
.quotes-category-section.loading-ajax {
  position: relative;
}
.quotes-category-section.loading-ajax::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 10;
  pointer-events: none;
}

.view-npx-quotes .btn-trainings.btn-quotes, .view-quotes-per-categories .btn-trainings.btn-quotes {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 0.5rem 1rem;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  background-color: #183881;
  color: #fff;
  margin-right: 1rem;
  margin-bottom: 1rem;
  position: relative;
  padding-right: 3rem;
}
.view-npx-quotes .btn-trainings.btn-quotes:hover, .view-quotes-per-categories .btn-trainings.btn-quotes:hover {
  background-color: #0056B3;
  color: #fff;
  text-decoration: none;
}
.view-npx-quotes .btn-trainings.btn-quotes::after, .view-quotes-per-categories .btn-trainings.btn-quotes::after {
  content: "";
  position: absolute;
  right: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-image: url("/themes/custom/bootstrap4grow/images/icons/arrow-right.svg");
  background-repeat: no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
  background-position: center;
}
.view-npx-quotes .btn-trainings.btn-quotes.disabled, .view-npx-quotes .btn-trainings.btn-quotes:disabled, .view-quotes-per-categories .btn-trainings.btn-quotes.disabled, .view-quotes-per-categories .btn-trainings.btn-quotes:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
.view-npx-quotes .btn-download.btn-quotes,
.view-npx-quotes .btn-more.btn-quotes, .view-quotes-per-categories .btn-download.btn-quotes,
.view-quotes-per-categories .btn-more.btn-quotes {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 0.5rem 1rem;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  background-color: #E3F0FF;
  color: #0056B3;
  margin-bottom: 1rem;
}
.view-npx-quotes .btn-download.btn-quotes:hover,
.view-npx-quotes .btn-more.btn-quotes:hover, .view-quotes-per-categories .btn-download.btn-quotes:hover,
.view-quotes-per-categories .btn-more.btn-quotes:hover {
  background-color: #CEE5FF;
  color: #0056B3;
  text-decoration: none;
}
.view-npx-quotes .btn-download.btn-quotes.disabled, .view-npx-quotes .btn-download.btn-quotes:disabled,
.view-npx-quotes .btn-more.btn-quotes.disabled,
.view-npx-quotes .btn-more.btn-quotes:disabled, .view-quotes-per-categories .btn-download.btn-quotes.disabled, .view-quotes-per-categories .btn-download.btn-quotes:disabled,
.view-quotes-per-categories .btn-more.btn-quotes.disabled,
.view-quotes-per-categories .btn-more.btn-quotes:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
.view-npx-quotes .download-all-quotes-button, .view-quotes-per-categories .download-all-quotes-button {
  margin-top: 1rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 1rem;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
@media (max-width: 35.99875rem) {
  .view-npx-quotes .download-all-quotes-button, .view-quotes-per-categories .download-all-quotes-button {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .view-npx-quotes .download-all-quotes-button .btn-quotes, .view-quotes-per-categories .download-all-quotes-button .btn-quotes {
    margin-right: 0;
  }
}
.view-npx-quotes .vocabulary-quotes-categories > h2,
.view-npx-quotes .vocabulary-quotes-categories .field--name-name, .view-quotes-per-categories .vocabulary-quotes-categories > h2,
.view-quotes-per-categories .vocabulary-quotes-categories .field--name-name {
  display: none;
}

.more-quotes-button-wrapper {
  text-align: center;
  margin-top: 1rem;
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 0.5rem 1rem;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  white-space: nowrap;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: pointer;
  background-color: transparent;
  color: #183881;
  border: 2px solid #183881;
  border-width: 2px;
  margin: 0;
  position: relative;
  padding-right: 3rem;
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes:hover {
  background-color: transparent;
  color: #0056B3;
  border-color: #0056B3;
  text-decoration: none;
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes::after {
  content: "";
  position: absolute;
  right: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-image: url("/themes/custom/bootstrap4grow/images/icons/arrow-down.svg");
  background-repeat: no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
  background-position: center;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes:hover::after {
  background-image: url("/themes/custom/bootstrap4grow/images/icons/arrow-down-hover.svg");
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes.disabled, .more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
.more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes.disabled::after, .more-quotes-button-wrapper .btn.btn-quotes.load-more-quotes:disabled::after {
  -webkit-animation: spin 1s linear infinite;
       -o-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: translateY(-50%) rotate(0deg);
            transform: translateY(-50%) rotate(0deg);
  }
  to {
    -webkit-transform: translateY(-50%) rotate(360deg);
            transform: translateY(-50%) rotate(360deg);
  }
}

@-o-keyframes spin {
  from {
    -o-transform: translateY(-50%) rotate(0deg);
       transform: translateY(-50%) rotate(0deg);
  }
  to {
    -o-transform: translateY(-50%) rotate(360deg);
       transform: translateY(-50%) rotate(360deg);
  }
}

@keyframes spin {
  from {
    -webkit-transform: translateY(-50%) rotate(0deg);
         -o-transform: translateY(-50%) rotate(0deg);
            transform: translateY(-50%) rotate(0deg);
  }
  to {
    -webkit-transform: translateY(-50%) rotate(360deg);
         -o-transform: translateY(-50%) rotate(360deg);
            transform: translateY(-50%) rotate(360deg);
  }
}
.webform-submission-quotes-download-form-form .js-form-type-checkbox label .npx-popover-trigger {
  text-decoration: underline;
  cursor: pointer;
}
.webform-submission-quotes-download-form-form .js-form-type-checkbox label:after {
  content: "";
  vertical-align: super;
  display: inline-block;
  background-image: url("/themes/custom/bootstrap4grow/images/required.svg");
  background-repeat: no-repeat;
  -webkit-background-size: 7px 7px;
          background-size: 7px 7px;
  width: 7px;
  height: 7px;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
