@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
}
@media (min-width: 62rem) {
  .training-terms-block {
    width: 100%;
  }
}
.training-terms-block-wrapper {
  overflow-x: scroll;
}
@media (min-width: 62rem) {
  .training-terms-block-wrapper {
    overflow-x: auto;
  }
}
.training-terms-block-td-1 {
  width: 16%;
}
.training-terms-block-td-2 {
  width: 20%;
}
.training-terms-block-td-3 {
  width: 12%;
}
.training-terms-block-td-4 {
  width: 6%;
}
.training-terms-block-td-5 {
  width: 12%;
}
.training-terms-block-h4 {
  font-size: 1rem;
}
.training-terms-block-with-sustable-table {
  margin: 0.3125rem 0 0.625rem 0;
}
.training-terms-block-th {
  padding-bottom: 0.625rem;
}
.training-terms-block-td-clickable::after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
  margin-left: 0.625rem;
  -o-transition: -o-transform 300ms ease;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
     transform: scaleY(-1);
}
.training-terms-block-td-clickable.open::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
.training-terms-block-npx-form-button {
  padding: 0.625rem 0.9375rem;
  white-space: nowrap;
  min-width: 125px;
}
.training-terms-block .ask-for-course-closed {
  padding: 0.625rem 0.9375rem;
}

.load-more-terms {
  border: 2px solid #0053B3;
  margin: -1.5rem auto 1.875rem;
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
}
.load-more-terms-bg {
  height: 0;
  background: #d0d8db;
  margin-top: 4.375rem;
}
.load-more-terms-wrapper {
  position: absolute;
  width: calc(100vw - 40px);
}
@media (min-width: 62rem) {
  .load-more-terms-wrapper {
    position: relative;
    width: auto;
  }
}

#szkolenie-grupa-1 {
  background: #154DB2;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
}
#szkolenie-grupa-1 .field__label {
  display: none;
}
#szkolenie-grupa-1 a.npx-form-button-inline.npx-autolink {
  padding: 0;
  border: 0;
  background: transparent;
  color: #fecc09;
  font-weight: bold;
  text-transform: none;
  margin-top: 0.75rem;
  display: inline-block;
  font-size: inherit;
}
#szkolenie-grupa-1 .field--name-field-ts-opis {
  font-size: 1.125rem;
  line-height: 1.5rem;
  margin: 0;
  position: relative;
  z-index: 9;
  padding-top: 0.5rem;
}
#szkolenie-grupa-1 .field--name-field-ts-opis p {
  font-size: 1.125rem;
  line-height: 1.5rem;
}
#szkolenie-grupa-1 .inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}
#szkolenie-grupa-1 h2 {
  margin-top: 0;
}
#szkolenie-grupa-1 h1 {
  line-height: 3.125rem;
  font-weight: bold;
  font-size: 2.5rem;
  margin-top: 2.75rem;
  z-index: 9;
  position: relative;
}
#szkolenie-grupa-1 a, #szkolenie-grupa-1 span.h1, #szkolenie-grupa-1 h1, #szkolenie-grupa-1 h2, #szkolenie-grupa-1 h3, #szkolenie-grupa-1 h4, #szkolenie-grupa-1 h5, #szkolenie-grupa-1 p, #szkolenie-grupa-1 li {
  color: #fff;
}
#szkolenie-grupa-1 a.npx-program-button {
  color: #191919;
}
#szkolenie-grupa-1 ul, #szkolenie-grupa-1 ol {
  list-style-image: url("/themes/custom/bootstrap4grow/images/check-white.png");
}
#szkolenie-grupa-1 .group-right .obraz img {
  padding-left: 0.625rem;
}
#szkolenie-grupa-1 .obraz {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
#szkolenie-grupa-1 .obraz img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  color: transparent;
  min-height: 220px;
}
#szkolenie-grupa-1.full-width-image h1 {
  margin-top: 5.75rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
}
@media (max-width: 61.99875rem) {
  #szkolenie-grupa-1.full-width-image h1 {
    margin-top: 3rem;
  }
}
@media (min-width: 75rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    max-width: 100%;
    padding-right: 0;
    padding-left: 1.875rem;
  }
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    padding-left: calc(50vw - 44.21875rem + 1.875rem);
  }
}
#szkolenie-grupa-1.half-width-image h1 {
  margin-top: 2rem;
}
#szkolenie-grupa-1.half-width-image span.h1 {
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 2rem;
  line-height: 2.25rem;
  display: block;
  font-weight: 700;
}
#szkolenie-grupa-1 .inner-absolute {
  bottom: 10px;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 50%;
  width: 100%;
  z-index: 3;
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 1.5rem;
  line-height: 2rem;
  display: block;
  font-weight: 700;
  color: #fff;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-1 .inner-absolute {
    font-size: 2.5rem;
    line-height: 3.5rem;
  }
}

#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile {
  display: block;
  position: relative;
}
@media (min-width: 75rem) {
  #szkolenie-grupa-1.half-width-image .obraz--gradient-mobile, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile {
    display: none;
  }
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile img, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile img {
  position: relative;
  z-index: 1;
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile::before, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: -webkit-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: -o-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: linear-gradient(76.43deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  z-index: 2;
  pointer-events: none;
}
#szkolenie-grupa-1.half-width-image .obraz--gradient-mobile .inner-absolute, #szkolenie-grupa-1.top-title-right-image .obraz--gradient-mobile .inner-absolute {
  z-index: 3;
}

@media (max-width: 61.99875rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (min-width: 75rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
    padding-right: 1.5rem;
  }
}

.node--type-landing-page .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.node--type-landing-page .paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.node--type-landing-page .paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
  padding-bottom: 2rem !important;
}
@media (min-width: 75rem) {
  .node--type-landing-page #szkolenie-grupa-1.half-width-image h1 {
    margin-top: 5rem;
  }
}

@media (min-width: 75rem) {
  .paragraph--type-tytul-szkolenia-video.ds-2col-fluid {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
        -ms-flex-align: stretch;
            align-items: stretch;
  }
}
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .field--name-field-ts-video-embed,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .video-embed-field-lazy,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid .video-embed-field-lazy img {
  height: 100%;
}

#szkolenie-grupa-1 .video-mobile {
  position: relative;
  padding-top: 56.25%;
  width: 100%;
}
#szkolenie-grupa-1 .video-mobile .video-embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#szkolenie-grupa-1 .video-mobile .video-embed iframe,
#szkolenie-grupa-1 .video-mobile .video-embed video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.video-embed-field-responsive-video {
  position: static !important;
  width: 100%;
}

.video-embed-field-lazy-play {
  border: none;
}

.paragraph--type-tytul-szkolenia-iii {
  position: relative;
  overflow: hidden;
}
.paragraph--type-tytul-szkolenia-iii::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: -webkit-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: -o-linear-gradient(13.57deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  background: linear-gradient(76.43deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
  z-index: 3;
  pointer-events: none;
}
.paragraph--type-tytul-szkolenia-iii .field--name-field-ts-obraz {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.paragraph--type-tytul-szkolenia-iii .field--name-field-ts-obraz img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.paragraph--type-tytul-szkolenia-iii .ds-2col-fluid > .group-left {
  position: relative;
  z-index: 4;
}
@media (min-width: 62rem) {
  .paragraph--type-tytul-szkolenia-iii .ds-2col-fluid > .group-left {
    -webkit-box-flex: 0 !important;
    -webkit-flex: 0 0 50% !important;
        -ms-flex: 0 0 50% !important;
            flex: 0 0 50% !important;
    max-width: 50% !important;
  }
}
.paragraph--type-tytul-szkolenia-iii .field--name-field-ts-tytul,
.paragraph--type-tytul-szkolenia-iii .field--name-field-ts-podtytul,
.paragraph--type-tytul-szkolenia-iii .field--name-field-ts-opis {
  position: relative;
  z-index: 9;
}
@media (min-width: 62rem) {
  .paragraph--type-tytul-szkolenia-iii:not(:has(.field--name-field-ts-obraz img)) .ds-2col-fluid > .group-left {
    -webkit-box-flex: 0 !important;
    -webkit-flex: 0 0 100% !important;
        -ms-flex: 0 0 100% !important;
            flex: 0 0 100% !important;
    max-width: 100% !important;
    text-align: center;
  }
}
.paragraph--type-tytul-szkolenia-iii .ds-2col-fluid > .group-right {
  display: none;
}

#szkolenie-grupa-2 .slick__arrow {
  bottom: 0;
  top: unset;
}
#szkolenie-grupa-2 .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
#szkolenie-grupa-2 .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
#szkolenie-grupa-2 .slick-prev:hover, #szkolenie-grupa-2 .slick-prev:focus, #szkolenie-grupa-2 .slick-next:hover, #szkolenie-grupa-2 .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
#szkolenie-grupa-2 .slick {
  max-width: 600px;
  margin: 0 auto;
}
#szkolenie-grupa-2 .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-2 .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

.npx-counter-wrapper {
  margin-left: -0.25rem;
}

.npx-tabs {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  .npx-tabs {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 75rem) {
  .npx-box-left {
    padding-right: 1rem;
  }
}
@media (max-width: 74.99875rem) {
  .npx-box-left {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
  }
}

.npx-counter-info {
  color: red;
}

.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  -webkit-background-size: cover;
          background-size: cover;
  margin-right: 0.375rem;
  margin-left: 0.3125rem;
}

#npx-price-info-wrapper {
  font-size: 0.875rem;
}
#npx-price-info-wrapper .form-item-npx-discount-code {
  position: relative;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code input[type=text] {
  margin: 0;
  height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 1px;
  width: 100%;
  max-width: 200px;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix {
  background-color: #fff;
  border: 0;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix a {
  display: block;
  position: absolute;
  z-index: 10;
  height: 34px;
  background: transparent url("../images/przelicz.png") no-repeat center center;
  width: 20px;
  text-align: left;
  text-indent: -9990px;
  right: 10px;
  top: 0;
  outline: 0;
  border: 0;
  margin-right: 0.25rem;
}
#npx-price-info-wrapper #npx-expand-bottom-wrapper {
  width: 100%;
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper #npx-expand-bottom-wrapper {
    width: auto;
    margin-right: 1rem;
  }
}
#npx-price-info-wrapper .list-group-item {
  border: none;
  display: list-item;
  margin-left: 1.25rem;
  padding: 0;
}
#npx-price-info-wrapper .item-list {
  padding-top: 3rem;
}
#npx-price-info-wrapper li {
  list-style-image: url("../images/li.png");
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper .npx-social-colorbox-link {
    top: -15px;
  }
}
#npx-price-info-wrapper .npx-social-colorbox-link a::before {
  vertical-align: sub;
  height: 20px;
  width: 20px;
  margin: 0 0.3125rem 0 0;
  background: transparent url("../images/price-tag.png") no-repeat center center;
  -webkit-background-size: auto auto;
          background-size: auto;
  background-size: auto;
  -webkit-background-size: cover;
          background-size: cover;
  display: inline-block;
  content: "";
}

@media (min-width: 48rem) {
  .npx-box-left .npx-price-b {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -0.9375rem;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0.9375rem;
  }
}
@media (min-width: 75rem) {
  .npx-box-left .npx-price-b {
    margin-top: 0;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0;
  }
}

@media (max-width: 35.99875rem) {
  .npx-price {
    min-height: 150px;
  }
}
.npx-price-a {
  padding: 1.875rem 2.5rem 0 0;
}
.npx-price-a-a {
  line-height: 1.25rem;
  font-size: 0.875rem;
}
.npx-price-a-b {
  font-size: 1.25rem;
}
.npx-price-b {
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-price-b {
    width: 60%;
    padding: inherit;
    padding-top: 3.125rem;
  }
}
@media (min-width: 62rem) {
  .npx-price-b {
    padding-top: 3.125rem;
    width: auto;
  }
}
.npx-price-b-a {
  font-size: 1.25rem;
}
.npx-price-b-b {
  font-size: 1.125rem;
}
.npx-price-b-c {
  color: #a2a2a2;
}
@media (max-width: 47.99875rem) {
  .npx-price-b-c {
    font-size: 0.8125rem;
  }
}
@media (min-width: 75rem) {
  .npx-price-b-c {
    top: 5px;
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
}
@media (min-width: 48rem) {
  .npx-counter-wrapper {
    top: -10px;
  }
}

.npx-calculation-box {
  padding: 1.875rem 1.875rem 0;
  margin: 0 -1.875rem;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
@media (min-width: 36rem) {
  .npx-calculation-box {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
}
.npx-calculation-box .list-group-item {
  background: transparent;
  padding-left: 0;
}
.npx-calculation-box input {
  max-width: 200px;
}

#npx-participants-amount-wrapper .description {
  font-size: 1em;
}
#npx-participants-amount-wrapper small.text-muted {
  max-width: calc(100% - 149px);
  float: right;
  color: #000 !important;
  font-size: 1rem;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper small.text-muted {
    float: none;
    max-width: 100%;
  }
}
#npx-participants-amount-wrapper span.ui-spinner {
  display: inline-block;
  position: relative;
  border: 1px solid #d0d8db;
  padding: 0 2.8125rem;
  border-radius: 0;
  margin: 0 0.625rem 0 0;
}
#npx-participants-amount-wrapper span.ui-spinner .form-control:focus {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#npx-participants-amount-wrapper span.ui-spinner input {
  border: 0;
  height: 44px;
  line-height: 2.8125rem;
  padding: 0 0.625rem;
  margin: 0;
  border-radius: 0;
  width: 45px;
  text-align: center;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper span.ui-spinner input {
    height: 44px;
  }
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button {
  border: 0;
  outline: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
  background-color: transparent;
  opacity: 0.85;
  padding: 0;
  margin: 0;
  right: auto;
  background-image: url("../images/spinner-min.png");
  background-position: center center;
  background-repeat: no-repeat;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button:hover {
  background-color: #ebeff2;
  cursor: pointer;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-tr {
  background-image: url("../images/spinner-plus.png");
  left: auto;
  right: 0;
  border-left: 1px solid #ddd;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-br {
  border-right: 1px solid #ddd;
}

#szkolenie-grupa-8 a.npx-form-tab {
  margin: 0.9375rem 0;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-8 a.npx-form-tab {
    padding: 1.0625rem;
    margin: 1.25rem 0.9375rem;
  }
}
#szkolenie-grupa-8 .form-item-npx-training {
  display: none;
}

a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
@media (min-width: 48rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 45%;
        -ms-flex: 1 0 45%;
            flex: 1 0 45%;
    max-width: 45%;
  }
}
@media (min-width: 75rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 29%;
        -ms-flex: 1 0 29%;
            flex: 1 0 29%;
    max-width: 29%;
  }
}
a.npx-form-tab:hover {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 101;
}
a.npx-form-tab.npx-active-tab {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 100;
}

.npx-form-outer-wrapper {
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}

#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 0.3rem;
}
@media (min-width: 36rem) {
  #npx-top-wrapper > div:not(#npx-tabs) {
    padding: 0 2.5rem;
  }
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.74) 1px;
  border-radius: 4px;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper {
    height: 100%;
  }
}

li a.active .npx-blocks-program-tab-wrapper, li a:hover .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

#npx-regular-box-wrapper {
  opacity: 0.65;
}

.npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
  opacity: 0.65;
}
.npx-box-right:not(.npx-active-box) .npx-price-b-a, .npx-box-left:not(.npx-active-box) .npx-price-b-a {
  text-decoration: line-through;
}
@media (max-width: 74.99875rem) {
  .npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
    display: none !important;
  }
}

#npx-regular-box-wrapper .npx-price-b-c {
  top: 0;
}

.npx-active-tab .npx-training-form-tab-wrapper, .npx-form-tab:hover .npx-training-form-tab-wrapper {
  -webkit-box-shadow: 0 0 15px 0 #54534f;
  box-shadow: 0 0 15px 0 #54534f;
  border-radius: 4px;
}

.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
  width: 100%;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
    width: auto;
  }
}
.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word-last {
  padding-left: 0.625rem;
}
.npx-training-form-type-info-wrapper .npx-spoiler-content {
  font-size: inherit;
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm ul li {
  list-style-image: url("../images/online-li-yellow.png");
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm:nth-child(1) ul li {
  list-style-image: url("../images/online-li-blue.png");
}
.npx-training-form-type-info-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  background-color: #fff;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.show-icon::before {
  content: "ROZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.hide-icon::before {
  content: "ZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
  font-weight: 600;
}
.npx-training-form-type-info-wrapper .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background: var(--secondary);
}

.tr-form-stationary {
  padding: 0.1875rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  font-weight: 800;
  color: #000;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}
.tr-form-online {
  padding: 0.1875rem 1rem;
  border-radius: 20px;
  font-weight: 800;
  background: var(--secondary);
  color: #fff;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.npx-variant .fieldset-legend {
  color: #000;
  margin: 2rem 0;
  display: block;
}
.npx-variant h4 {
  line-height: 1.5rem;
}

.npx-training-form-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.44) 1px;
  border-radius: 4px;
  height: 100%;
}
.npx-training-form-tab-wrapper .n-tab-header-inner {
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header-inner {
  border-radius: 4px 4px 0 0;
  min-height: 183px;
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header {
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
  border-radius: 4px 4px 0 0;
}
.npx-training-form-tab-header-hours {
  text-shadow: none;
  font-size: 0.75rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
.npx-training-form-tab-header-type {
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  font-size: 0.625rem;
}
.npx-training-form-tab-header-type .stationary {
  padding: 0.125rem 0.5rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
}
.npx-training-form-tab-header-type .live-online {
  padding: 0.125rem 0.5rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .webinar, .npx-training-form-tab-header-type .online {
  padding: 0.125rem 0.5rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .stationary, .npx-training-form-tab-header-type .live-online, .npx-training-form-tab-header-type .online {
  max-height: 20px;
  display: inline-block;
}
.npx-training-form-tab-header-title h3 {
  font-size: 1.3rem;
  text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
  text-transform: none;
  color: #fff;
  font-weight: 600;
  margin: 2rem 0 1.2rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.npx-training-form-tab-content {
  text-transform: none;
}
.npx-training-form-tab-content p {
  font-size: 0.9375rem;
}
.npx-training-form-tab-content ul {
  padding-left: 0.9375rem;
}
.npx-training-form-tab-content ul li {
  font-size: 0.9375rem;
  text-transform: none;
  font-weight: normal;
  text-align: left;
  list-style-image: url("../images/li_checkmark.png");
  line-height: 1.3rem;
}
.npx-training-form-tab-more {
  margin: auto 0.9375rem 0.625rem 0;
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content .tab-pane {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}

.field--name-field-question {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-left: 2rem;
}
.field--name-field-question.active:before {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 28px;
}
.field--name-field-question:before {
  position: absolute;
  top: 22px;
  cursor: pointer;
  content: "";
  display: inline-block;
  width: 11px;
  height: 11px;
  border-right: 2px solid #343a40;
  border-top: 2px solid #343a40;
  -webkit-transform: rotate(135deg);
  -o-transform: rotate(135deg);
     transform: rotate(135deg);
  margin-right: 0.5em;
  margin-left: 1em;
  max-width: 12px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-item-align: center;
  -webkit-align-self: center;
          align-self: center;
  left: -12px;
  -webkit-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}
.field--name-field-question h3, .field--name-field-question p, .field--name-field-question h4 {
  margin: 0.625rem 0;
}

.field--name-field-answer h3, .field--name-field-answer h4 {
  margin: 0 0 0.625rem 0;
}

#szkolenie-grupa-6 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .item-list {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 5.625rem auto;
}

.field--name-dynamic-block-fieldnode-ds-training-program-block .field-label-above:first-of-type:not(:last-of-type):after {
  display: none;
}

.ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::before, .ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::after {
  background-color: #000;
}

.pdf-program-link img {
  width: 48px;
  height: auto;
}

.program-accordion ul {
  padding-left: 1.2rem;
}
.program-accordion li {
  margin: 0.5rem 0 0.5rem 1.1rem;
}
.program-accordion .pdf-program {
  z-index: 5;
  position: relative;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program {
    float: right;
  }
}
.program-accordion .pdf-program-link {
  margin-left: 0.625rem;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link {
    margin-left: 1.25rem;
  }
}
.program-accordion .pdf-program-link img {
  width: 38px;
  display: inline-block;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link img {
    width: 48px;
  }
}
.program-accordion .pdf-program-link a {
  margin: 0.9375rem;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program span {
    display: block;
    margin-bottom: 0.625rem;
  }
}
@media (min-width: 62rem) {
  .program-accordion h2.field-label-above {
    margin-right: 10.625rem;
  }
}
.program-accordion .ckeditor-accordion-container h4 {
  font-size: 1.5rem;
  font-weight: 700;
}
.program-accordion .ckeditor-accordion-container > dl dt > a, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button) {
  color: inherit;
  border-bottom: #dcdddf 1px solid;
  text-decoration: none;
}
.program-accordion .ckeditor-accordion-container > dl dt > a:hover, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button):hover {
  color: inherit;
  text-decoration: underline;
}
.program-accordion .ckeditor-accordion-toggler {
  background-color: transparent !important;
  color: inherit;
  font-size: 1.2rem;
}
.program-accordion dl dt > a {
  background-color: #ecedef;
  color: #000;
  border-bottom: #dcdddf 1px solid;
}
.program-accordion dl {
  border: #dcdddf 1px solid;
}

#szkolenie-grupa-6.sekcja.w100.w100limitContent .tab-content {
  background: #f1fbfc;
  padding: 1.875rem 0;
}

.npx-training-form-tab-header {
  position: relative;
}
.npx-training-form-tab-header-inner {
  z-index: 9;
  position: relative;
}
.npx-training-form-tab-header img {
  position: absolute;
  z-index: 2;
}

.paragraph--type-arguments {
  margin-top: 3.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  .paragraph--type-arguments {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.paragraph--type-arguments .group-left {
  width: 100%;
  height: 100px;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-left {
    width: 45%;
  }
}
.paragraph--type-arguments .group-right {
  width: 100%;
  height: auto;
  background: transparent;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-right {
    width: 55%;
    height: 100px;
    background: #f4f7f5;
    border-radius: 100px 0 0 100px;
  }
}
.paragraph--type-arguments a.n-get-pdf {
  padding-right: 2.5rem;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments a.n-get-pdf {
    padding-right: 0;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right {
  padding-left: 1.25rem;
  max-width: 400px;
  margin-left: auto;
  border-left: #000 1px solid;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .field--name-field-arguments-right {
    padding-left: 2.5rem;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right p {
  font-size: 0.7rem;
  line-height: 1rem;
}
.paragraph--type-arguments .field--name-field-arguments-left {
  max-width: 300px;
  padding-left: 3.125rem;
  font-size: 1.2rem;
}
.paragraph--type-arguments .field--name-field-arguments-left::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  z-index: -1;
  background: #f4f7f5;
  border-radius: 100px;
}

.field-oni-juz-byli_item_even {
  margin-top: 1.25rem;
  margin-left: auto;
  margin-right: 0;
}

.field--name-field-oni-juz-byli_item {
  max-width: 660px;
  clear: both;
}
.field--name-field-oni-juz-byli_item .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-oni-juz-byli_item .group-middle {
    width: calc(100% - 110px);
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

@media (min-width: 62rem) {
  .field--name-field-loga-firm {
    max-height: 106px;
    overflow: hidden;
  }
}
.field--name-field-loga-firm .field__item.col-auto {
  -webkit-transform: scale(0.7);
       -o-transform: scale(0.7);
          transform: scale(0.7);
}
@media (min-width: 36rem) {
  .field--name-field-loga-firm .field__item.col-auto {
    -webkit-transform: none;
         -o-transform: none;
            transform: none;
  }
}
.field--name-field-loga-firm img {
  opacity: 0.5;
  height: 40px;
  width: auto;
  margin: 0.1875rem auto;
}

.field--name-field-logo-1-ref {
  padding-bottom: 0.25rem;
}

.field--name-field-logo-2-ref, .field--name-field-logo-1-ref {
  min-height: 60px;
}

h2.field-label-above p {
  font-size: inherit;
  font-weight: inherit;
  margin-bottom: inherit;
}

@media (min-width: 62rem) {
  .field--name-field-blog-posts {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .field--name-field-blog-posts .node--type-article.node--view-mode-grow3 {
    height: 100%;
    display: block;
    position: relative;
    padding-bottom: 3.5rem;
  }
  .field--name-field-blog-posts .field--name-node-link {
    margin-left: auto;
    margin-right: auto;
  }
  .field--name-field-blog-posts .field__item {
    max-width: 92%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 32%;
        -ms-flex: 1 0 32%;
            flex: 1 0 32%;
  }
}
.field--name-field-blog-posts .field__item {
  text-align: center;
}
.field--name-field-blog-posts h3 {
  margin: 0;
  font-size: 1.25rem;
}
.field--name-field-blog-posts img {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  -o-transition: -o-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s, -o-transform 0.2s;
}
.field--name-field-blog-posts img:hover {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
}
.field--name-field-blog-posts .field--name-field-image {
  overflow: hidden;
}
.field--name-field-blog-posts .node--type-article {
  max-width: 450px;
  margin: 0 auto;
}
@media (max-width: 61.99875rem) {
  .field--name-field-blog-posts .node--type-article {
    margin-bottom: 2rem;
  }
}
.field--name-field-blog-posts .node--type-article h3 a {
  color: #000;
}

.npx-training-form-tab-content,
.npx-tile-textbox-content,
.tile-opinion-content {
  position: relative;
  overflow: hidden;
}
.npx-training-form-tab-content span, .npx-training-form-tab-content p,
.npx-tile-textbox-content span,
.npx-tile-textbox-content p,
.tile-opinion-content span,
.tile-opinion-content p {
  font-size: 0.875rem;
  color: #343A40;
  line-height: 1.5;
  font-weight: normal;
}

.npx-training-form-tab-content.has-overflow::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4.5rem;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(60%, rgba(255, 255, 255, 0.8)), to(white));
  background: -webkit-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.8) 60%, white 100%);
  background: -o-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.8) 60%, white 100%);
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.8) 60%, white 100%);
  pointer-events: none;
  opacity: 1;
  -webkit-transition: opacity 0.3s ease;
  -o-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.npx-blocks-program-tab-wrapper.expanded .npx-training-form-tab-content.has-overflow::after,
.npx-tile-textbox-wrapper.expanded.has-overflow::after,
.npx-tile-opinion-wrapper.expanded.has-overflow::after {
  opacity: 0;
}

.npx-tile-header h2.field-label-above::after {
  height: 6px;
  border-radius: 16px;
  width: 70px;
  background: #f2ca10;
}

.npx-tile-sections-container .npx-tile-section {
  position: relative;
  margin-bottom: 2rem;
  padding-bottom: 3.75rem;
}
.npx-tile-sections-container .tile-row {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}
@media (min-width: 48rem) {
  .npx-tile-sections-container .tile-row {
    max-height: 300px;
  }
}
@media (max-width: 47.99875rem) {
  .npx-tile-sections-container .tile-row {
    max-height: none;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.npx-tile-sections-container .tile-row .col-md-4 {
  position: relative;
}
.npx-tile-sections-container .tile-row .col-md-4 .nav-item {
  position: relative;
  z-index: 1;
}
.npx-tile-sections-container .tile-row .col-md-4 .nav-item:hover {
  z-index: 10;
}
@media (min-width: 48rem) {
  .npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-blocks-program-tab-wrapper.expanded,
.npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-tile-textbox-wrapper.expanded,
.npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-tile-opinion-wrapper.expanded {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 100% !important;
    max-width: none !important;
  }
}
@media (max-width: 47.99875rem) {
  .npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-blocks-program-tab-wrapper.expanded,
.npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-tile-textbox-wrapper.expanded,
.npx-tile-sections-container .tile-row .col-md-4 .nav-item .npx-tile-opinion-wrapper.expanded {
    position: relative !important;
    z-index: 1 !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
    width: auto !important;
    max-width: 100% !important;
  }
}
.npx-tile-sections-container .show-more-container {
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 1;
}
.npx-tile-sections-container .show-more-container .show-more-btn {
  position: relative;
  z-index: 1;
}

.icon-calendar,
.icon-location,
.icon-price,
.icon-envelope {
  display: inline-block;
  width: 14px;
  height: 14px;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar {
  background-image: url("../images/icons/calendar.svg");
}

.icon-location {
  background-image: url("../images/icons/location.svg");
}

.icon-price {
  background-image: url("../images/icons/price.svg");
}

.icon-envelope {
  width: 10px;
  height: 10px;
  background-image: url("../images/icons/envelope.svg");
  position: relative;
  top: 1px;
}

.icon-arrow-ne {
  width: 12px;
  height: 12px;
  background-image: url("../images/icons/arrow-ne.svg");
}

.icon-arrow-down {
  width: 13px;
  height: 8px;
  background-image: url("../images/icons/arrow-down.svg");
}

.icons-column {
  width: 16px;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.icons-column .icon-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 16px;
}

.npx-blocks-program-tab-wrapper.expanded .show-more-link,
.npx-tile-textbox-wrapper.expanded .show-more-link,
.npx-tile-opinion-wrapper.expanded .show-more-link {
  display: none;
}

.show-more-link {
  position: absolute !important;
  bottom: 5px !important;
  left: 50% !important;
  -webkit-transform: translateX(-50%) !important;
       -o-transform: translateX(-50%) !important;
          transform: translateX(-50%) !important;
  background: transparent !important;
  padding: 0 !important;
  border: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  color: #2A7DE3 !important;
  text-decoration: none !important;
  z-index: 20 !important;
  font-weight: 600 !important;
}
.show-more-link:hover {
  text-decoration: underline !important;
}
.show-more-link::after {
  content: "" !important;
  display: inline-block !important;
  width: 8px !important;
  height: 8px !important;
  margin-left: 0.3125rem !important;
  background-image: url("../images/icons/arrow-down.svg") !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  -webkit-background-size: contain !important;
          background-size: contain !important;
  vertical-align: middle !important;
}

.npx-tile-textbox-wrapper .show-more-link,
.npx-tile-opinion-wrapper .show-more-link {
  position: absolute !important;
  bottom: 5px !important;
  left: 50% !important;
  -webkit-transform: translateX(-50%) !important;
       -o-transform: translateX(-50%) !important;
          transform: translateX(-50%) !important;
}

.npx-no-autolink {
  text-decoration: none !important;
}
.npx-no-autolink:hover {
  text-decoration: none !important;
}
.npx-no-autolink:focus {
  text-decoration: none !important;
}
.npx-no-autolink:visited {
  text-decoration: none !important;
}

.npx-tile-opinion-wrapper {
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: #f8f9fa;
  -webkit-box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
          box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
  position: relative;
}
.npx-tile-opinion-wrapper.expanded {
  height: auto !important;
  min-height: 300px;
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
}
@media (min-width: 48rem) {
  .npx-tile-opinion-wrapper:hover {
    -webkit-transform: scale(1.02);
         -o-transform: scale(1.02);
            transform: scale(1.02);
  }
}
.npx-tile-opinion-wrapper::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 12px;
  background: -webkit-linear-gradient(180.18deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  background: -o-linear-gradient(180.18deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  background: linear-gradient(269.82deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  pointer-events: none;
}
.npx-tile-opinion-wrapper .tile-opinion-title {
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.1;
  color: #183881;
  margin: 0 0 1.25rem 0.5rem;
  position: relative;
  padding-left: 6rem;
  min-height: 32px;
}
.npx-tile-opinion-wrapper .tile-opinion-title::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 0;
  width: 50px;
  height: 32px;
  background: url("../images/quotes.svg") no-repeat center center/contain;
}
.npx-tile-opinion-wrapper .tile-opinion-title::after {
  content: "";
  position: absolute;
  left: 8px;
  top: 40px;
  width: 50px;
  height: 6px;
  border-radius: 16px;
  background: #f2ca10;
}
.npx-tile-opinion-wrapper .tile-opinion-image img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  border: 2px solid #ffd817;
}
.npx-tile-opinion-wrapper .tile-opinion-text {
  margin-left: 0.5rem;
}
.npx-tile-opinion-wrapper .tile-opinion-author-name {
  color: #343a40;
  font-weight: 700;
  font-size: 0.75rem;
  line-height: 1.2;
}
.npx-tile-opinion-wrapper .tile-opinion-author-company {
  color: #343a40;
}
.npx-tile-opinion-wrapper .tile-opinion-linkedin {
  position: relative;
  text-indent: -9999px;
  display: inline-block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}
.npx-tile-opinion-wrapper .tile-opinion-linkedin::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}
.npx-tile-opinion-wrapper.has-overflow::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 8px;
  height: 4.5rem;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(60%, rgba(255, 255, 255, 0.6)), to(rgba(255, 255, 255, 0.9)));
  background: -webkit-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  background: -o-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  pointer-events: none;
  opacity: 1;
  -webkit-transition: opacity 0.3s ease;
  -o-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  z-index: 1;
}
.npx-tile-opinion-wrapper.expanded.has-overflow::after {
  opacity: 0;
}

.tile-opinion-cta a {
  color: #0056B3;
  text-decoration: underline;
  font-size: 0.875rem;
  line-height: 1.5;
  background: transparent !important;
}

.tile-opinion-content,
.tile-opinion-author {
  margin-left: 0.5rem;
}

.tile-opinion-content {
  font-size: 0.875rem;
  color: #343A40;
  line-height: 1.5;
}

.tile-opinion-author-name {
  font-size: 0.75rem !important;
  line-height: 1.3 !important;
  white-space: normal !important;
}
.tile-opinion-author-name br {
  display: block !important;
  line-height: 1.3 !important;
  margin: 0 !important;
}
.tile-opinion-author-name br + br {
  margin-top: 0 !important;
}

.npx-tile-textbox-wrapper {
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: #f8f9fa;
  -webkit-box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
          box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
  position: relative;
}
.npx-tile-textbox-wrapper.expanded {
  height: auto !important;
  min-height: 300px;
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
}
@media (min-width: 48rem) {
  .npx-tile-textbox-wrapper:hover {
    -webkit-transform: scale(1.02);
         -o-transform: scale(1.02);
            transform: scale(1.02);
  }
}
.npx-tile-textbox-wrapper::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 12px;
  background: -webkit-linear-gradient(180.18deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  background: -o-linear-gradient(180.18deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  background: linear-gradient(269.82deg, rgba(0, 86, 179, 0.8) 0.18%, #0053B3 99.87%);
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  pointer-events: none;
}
.npx-tile-textbox-wrapper h3 {
  margin-top: 0;
}
.npx-tile-textbox-wrapper .npx-tile-textbox-content h3 p {
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.1;
  color: #183881;
  margin-bottom: 0;
}
.npx-tile-textbox-wrapper .npx-tile-textbox-content h3::after {
  content: "";
  display: block;
  width: 50px;
  height: 6px;
  border-radius: 16px;
  background: #f2ca10;
  margin-top: 0.5rem;
}
.npx-tile-textbox-wrapper .npx-tile-textbox-content {
  margin-left: 1rem;
}
.npx-tile-textbox-wrapper .npx-tile-textbox-content span, .npx-tile-textbox-wrapper .npx-tile-textbox-content p {
  font-size: 0.875rem;
  color: #343A40;
  line-height: 1.5;
}
.npx-tile-textbox-wrapper .npx-tile-textbox-content a span {
  color: #0056B3;
  text-decoration: underline;
}
.npx-tile-textbox-wrapper.has-overflow::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 8px;
  height: 4.5rem;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(60%, rgba(255, 255, 255, 0.6)), to(rgba(255, 255, 255, 0.9)));
  background: -webkit-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  background: -o-linear-gradient(top, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
  pointer-events: none;
  opacity: 1;
  -webkit-transition: opacity 0.3s ease;
  -o-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  z-index: 1;
}
.npx-tile-textbox-wrapper.expanded.has-overflow::after {
  opacity: 0;
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: #f8f9fa;
  -webkit-box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
          box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
}
.npx-blocks-program-tab-wrapper.expanded {
  height: auto !important;
  min-height: 300px;
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper:hover {
    -webkit-transform: scale(1.02);
         -o-transform: scale(1.02);
            transform: scale(1.02);
  }
}
.npx-blocks-program-tab-wrapper .npx-training-form-tab-header img {
  border-top-left-radius: 8px;
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper.d-flex.flex-md-row .n-tab-header {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 220px;
        -ms-flex: 0 0 220px;
            flex: 0 0 220px;
    max-width: 220px;
    min-width: 220px;
  }
  .npx-blocks-program-tab-wrapper.d-flex.flex-md-row .npx-training-form-tab-content {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
        -ms-flex: 1;
            flex: 1;
    min-width: 0;
  }
  .npx-blocks-program-tab-wrapper.d-flex.flex-md-row .npx-training-form-tab-content ul li {
    font-size: 0.875rem;
  }
}
@media (max-width: 47.99875rem) {
  .npx-blocks-program-tab-wrapper.d-flex.flex-md-row {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -webkit-flex-direction: column !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important;
  }
  .npx-blocks-program-tab-wrapper.d-flex.flex-md-row .n-tab-header {
    -webkit-box-flex: 0;
    -webkit-flex: none;
        -ms-flex: none;
            flex: none;
    max-width: none;
    min-width: none;
  }
}

.tile-training-cta a {
  color: #0056B3;
  text-decoration: underline;
  font-size: 0.875rem;
  line-height: 1.5;
  background: transparent !important;
}

.npx-training-form-tab-header-info .t3,
.npx-training-form-tab-header-info .t3b {
  color: #fff;
  font-size: 0.875rem;
  line-height: 1.5;
}
.npx-training-form-tab-header-info .t3b {
  font-weight: 700;
}
.npx-training-form-tab-header-info-note.t3 {
  line-height: 1.2;
}

.npx-training-form-tab-header-inner {
  border-bottom-left-radius: 8px;
}

.npx-training-form-tab-header-info {
  height: 100%;
  padding: 0.625rem;
  background-color: #0056B3;
}

.npx-training-form-tab-header-top {
  padding: 0.625rem;
  position: relative;
}

.npx-training-form-tab-header img {
  height: 120px;
}

.npx-training-form-tab-header-title h3 {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  text-shadow: none;
}

.npx-training-form-tab-header-type {
  text-align: left !important;
  position: absolute;
  bottom: 0.5rem;
  left: 10px;
  right: 10px;
}

.npx-program-tabs-wrapper .nav-tabs > li:first-child {
  padding-left: 0;
}

.npx-program-tabs-wrapper .nav-tabs > li:last-child {
  padding-right: 0;
}

.npx-training-form-tab-header-info-row.long-text .t3b {
  white-space: normal;
  line-height: 1.2;
}

.training-inquiry-link {
  display: inline-block;
  color: #fff;
  text-decoration: none;
  font-size: 0.75rem;
  line-height: 1.2;
  font-weight: 700;
}
.training-inquiry-link:hover {
  color: #fff;
  text-decoration: none;
}
.training-inquiry-link:focus {
  text-decoration: none;
}
.training-inquiry-link:visited {
  color: #fff;
}

.content-column {
  min-width: 0;
}
.content-column .content-row .t3 {
  white-space: nowrap;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.content-column .content-row .t3b {
  text-align: left;
  -webkit-flex-shrink: 1;
      -ms-flex-negative: 1;
          flex-shrink: 1;
  min-width: 0;
}

.npx-training-form-tab-header-info-row .t3 {
  width: 90px;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.npx-training-form-tab-header-info-row i {
  margin-right: 0.1875rem;
}

.narrow {
  max-width: 1200px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}
.narrow h2 {
  margin-left: 0;
  margin-right: 0;
}
@media (min-width: 81.25rem) {
  .narrow h2 {
    margin-left: -3vw;
    margin-right: -3vw;
  }
}
@media (min-width: 100rem) {
  .narrow h2 {
    margin-left: -6.625rem;
    margin-right: -6.625rem;
  }
}

.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.n-breadcrumb {
  display: inline-block;
}

.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

div.node--type-landing-page .field--name-body {
  padding-top: 1.5rem;
}

#szkolenie-grupa-9 .slick__arrow {
  bottom: 0;
  top: unset;
}
#szkolenie-grupa-9 .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
#szkolenie-grupa-9 .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
#szkolenie-grupa-9 .slick-prev:hover, #szkolenie-grupa-9 .slick-prev:focus, #szkolenie-grupa-9 .slick-next:hover, #szkolenie-grupa-9 .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
#szkolenie-grupa-9 .slick {
  max-width: 600px;
  margin: 0 auto;
}
#szkolenie-grupa-9 .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-9 .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

.node--type-landing-page .field--name-field-referencje-i-opinie .slick__arrow {
  bottom: 0;
  top: unset;
}
.node--type-landing-page .field--name-field-referencje-i-opinie .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
.node--type-landing-page .field--name-field-referencje-i-opinie .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
.node--type-landing-page .field--name-field-referencje-i-opinie .slick-prev:hover, .node--type-landing-page .field--name-field-referencje-i-opinie .slick-prev:focus, .node--type-landing-page .field--name-field-referencje-i-opinie .slick-next:hover, .node--type-landing-page .field--name-field-referencje-i-opinie .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
.node--type-landing-page .field--name-field-referencje-i-opinie .slick {
  max-width: 600px;
  margin: 0 auto;
}
.node--type-landing-page .field--name-field-referencje-i-opinie .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  .node--type-landing-page .field--name-field-referencje-i-opinie .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

body h2 {
  margin-top: 0;
  padding-top: 2rem;
}

body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -1.25rem;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
}
@media (min-width: 62rem) {
  body h2.field-label-above::after {
    left: 0;
    margin-left: 0;
  }
}

@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 33.333%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33.333%;
        -ms-flex: 1 0 33.333%;
            flex: 1 0 33.333%;
  }
}

.npx-training-form-tab-header {
  -webkit-background-size: 100% 140px;
          background-size: 100% 140px;
}

.npx-training-form-tab-header, .npx-training-form-tab-content {
  border-radius: 8px;
}
@media (min-width: 48rem) {
  .npx-training-form-tab-header, .npx-training-form-tab-content {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
  }
}

.npx-training-form-tab-header-inner {
  height: 100%;
}

.npx-training-form-tab-header-top {
  height: 120px;
}

.npx-training-form-tab-header-info-inner.h5 {
  font-weight: bold;
  font-size: 0.9375rem;
}
.npx-training-form-tab-header-info-inner span {
  color: #ffc60c;
}

@media (min-width: 48rem) {
  .npx-training-form-tab-header-inner {
    border-top-right-radius: 0;
  }
}

#szkolenie-grupa-6 {
  background: #f1fbfc;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
}
.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link .n-tab-header {
  height: 100%;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-content a:not(.npx-no-autolink) {
  background: transparent;
  text-decoration: underline;
  margin-bottom: 0;
}

.load-more-terms-wrapper {
  position: relative;
}

@media (max-width: 74.99875rem) {
  #szkolenie-grupa-1 .field--name-field-ts-opis {
    display: inline-block;
  }
}

.npx-tile-textbox-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  height: 100%;
  border-radius: 8px;
  padding: 1rem;
}
.npx-tile-textbox-wrapper:hover {
  -webkit-transform: scale(1.02);
       -o-transform: scale(1.02);
          transform: scale(1.02);
}

.npx-tile-opinion-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  height: 100%;
  border-radius: 8px;
  padding: 1rem;
}
.npx-tile-opinion-wrapper:hover {
  -webkit-transform: scale(1.02);
       -o-transform: scale(1.02);
          transform: scale(1.02);
}
.npx-tile-opinion-wrapper .tile-opinion-rating {
  text-align: center;
}
.npx-tile-opinion-wrapper .tile-opinion-rating .fas, .npx-tile-opinion-wrapper .tile-opinion-rating .far {
  font-size: 1.2rem;
  margin: 0 0.125rem;
}
.npx-tile-opinion-wrapper .tile-opinion-image {
  text-align: center;
}
.npx-tile-opinion-wrapper .tile-opinion-image img {
  width: 60px;
  height: 60px;
  -o-object-fit: cover;
     object-fit: cover;
}

.npx-training-form-tab-header-type .online,
.npx-training-form-tab-header-type .online-live {
  background: var(--secondary);
  color: #fff;
}
.npx-training-form-tab-header-type .stationary {
  border: none;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
