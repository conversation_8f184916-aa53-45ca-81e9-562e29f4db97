@charset "UTF-8";
@import url(https://fonts.googleapis.com/css?family=Muli:300,300i,400,600,700,800&subset=latin-ext&display=optional);
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

header {
  background: #fff;
  z-index: 10;
  max-width: 1415px;
  max-height: 66px;
}

.site-logo img {
  max-height: 66px;
  width: auto;
}

#block-bootstrap4grow-login {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
  padding-right: 4.75rem;
  padding-top: 1.375rem;
}
#block-bootstrap4grow-login a {
  z-index: 2;
}
#block-bootstrap4grow-login img {
  max-width: 25px;
  opacity: 0.6;
}

#block-markawitryny {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 70%;
      -ms-flex: 1 0 70%;
          flex: 1 0 70%;
}
@media (min-width: 65.625rem) {
  #block-markawitryny {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 auto;
        -ms-flex: 1 0 auto;
            flex: 1 0 auto;
  }
}

#block-grow3menu .sf-accordion-toggle {
  position: absolute;
  right: 0;
  top: 0;
  width: 70px;
  height: 70px;
  z-index: 100;
}

a#superfish-grow3menu-toggle {
  position: absolute;
  right: 0;
}
a#superfish-grow3menu-toggle span {
  display: block;
  text-indent: -9900px;
  background: transparent url(/themes/custom/bootstrap4grow/images/hamburger.png) no-repeat center center;
  width: 70px;
  height: 70px;
}

a#superfish-grow3menu-toggle.sf-expanded span {
  background-image: url(/themes/custom/bootstrap4grow/images/hamburgerON.png);
}

ul.sf-menu .sf-sub-indicator {
  display: none;
}

ul#superfish-grow3menu {
  max-width: 920px;
  margin-left: auto;
}
ul#superfish-grow3menu li.sf-depth-1 {
  position: static;
}
ul#superfish-grow3menu a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu a.sf-depth-1 {
  color: #000;
  font-size: 1.06rem;
  padding: 0 0.47rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-transform: uppercase;
  font-weight: 600;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 0.75rem;
  }
}
@media (min-width: 75rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem;
  }
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 1.5rem;
  }
}
ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
  content: " ";
  width: 32px;
  height: 2px;
  display: block;
  left: 50%;
  margin-left: -1rem;
  top: calc(100% + 23px);
  background-color: #ffab1a;
  position: absolute;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
    top: calc(100% + 7px);
  }
}
ul#superfish-grow3menu a {
  color: #000;
}

.n-breadcrumb {
  padding-left: 0.75rem;
  color: #000;
}
.n-breadcrumb a, .n-breadcrumb a:hover, .n-breadcrumb a:active, .n-breadcrumb a:focus {
  color: #000;
}
.n-breadcrumb li::before {
  content: " »";
  font-size: 1.2rem;
  padding: 0 0.5rem 0 0.25rem;
}
.n-breadcrumb li:first-child::before {
  content: none;
}
.n-breadcrumb.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1rem;
}

.node--type-landing-page .n-breadcrumb,
.node--type-npxtraining .n-breadcrumb {
  font-size: 0.875rem;
  color: #183881;
  line-height: 1.2;
  font-weight: 700;
  color: #183881;
}
.node--type-landing-page .n-breadcrumb a,
.node--type-landing-page .n-breadcrumb a:hover,
.node--type-landing-page .n-breadcrumb a:active,
.node--type-landing-page .n-breadcrumb a:focus,
.node--type-npxtraining .n-breadcrumb a,
.node--type-npxtraining .n-breadcrumb a:hover,
.node--type-npxtraining .n-breadcrumb a:active,
.node--type-npxtraining .n-breadcrumb a:focus {
  color: inherit;
  text-decoration: none;
}
.node--type-landing-page .n-breadcrumb li::before,
.node--type-npxtraining .n-breadcrumb li::before {
  content: "> ";
  font-size: 1.1em;
  width: 8px;
  padding: 0 0.5rem 0 0.5rem;
  color: inherit;
}
.node--type-landing-page .n-breadcrumb li:first-child::before,
.node--type-npxtraining .n-breadcrumb li:first-child::before {
  content: none;
}

#main-wrapper {
  border-top: 1px solid #ebedec;
}

#cookies-overlay.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: black;
  opacity: 0.5;
  z-index: 55557;
}

.cookiesjsr--app .cookiesjsr-banner {
  z-index: 55558;
}

.cookiesjsr-banner--text,
.cookiesjsr-links.links--row li a {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: #000;
}

.cookiesjsr-banner--text a {
  color: #2A7DE3 !important;
  text-decoration: none;
}

.cookiesjsr-links.links--row li a {
  font-weight: 600;
}

.cookiesjsr--app .cookiesjsr-banner {
  background: #fff;
  -webkit-box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
          box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before {
  content: "";
  background-color: #000;
  border-radius: 100%;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
  content: "";
}
@media (max-width: 47.99875rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before,
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
    content: "";
    background-color: #000;
    border-radius: 100%;
    height: 0.3em;
    left: 0;
    position: absolute;
    top: 45%;
    width: 0.3em;
  }
}
.cookiesjsr--app .cookiesjsr-banner button {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  background: var(--secondary);
  border: 0;
  border-radius: 5px;
  text-transform: none;
}
.cookiesjsr--app .cookiesjsr-banner button:hover {
  background-color: #034b7d;
}
.cookiesjsr--app .cookiesjsr-banner.active {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr--app .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}

.cookiesjsr-banner .cookiesjsr-btn.important.denyAll,
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings {
  color: black;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll {
  background-color: #fecc09;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll:hover {
  background-color: #ffab1a;
}
.cookiesjsr-banner .cookiesjsr-btn.important.denyAll {
  background-color: transparent;
  border: 2px solid #CED4DA;
}
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings:hover {
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

.cookiesjsr-banner .cookiesjsr-banner--info {
  margin-bottom: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 1rem;
}
@media (min-width: 48rem) {
  .cookiesjsr-banner .cookiesjsr-banner--info {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.cookiesjsr-banner .cookiesjsr-banner--info .cookiesjsr-banner--links.links--row {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr-links, .cookiesjsr-banner--links.links--row {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-top: 0;
}

.cookiesjsr-banner--text {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  -webkit-flex-direction: column-reverse !important;
      -ms-flex-direction: column-reverse !important;
          flex-direction: column-reverse !important;
}
@media (min-width: 48rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -webkit-flex-direction: row-reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important;
    min-width: 60%;
    width: auto;
  }
}

.cookiesjsr-layer {
  border-radius: 5px;
}

.cookiesjsr-layer--body {
  margin: 0 1rem;
  bottom: 7rem !important;
}
.cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab, .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
  background: #0056B3;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}
@media (max-width: 47.99875rem) {
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab {
    display: none;
  }
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
    border-radius: 4px;
  }
}

.cookiesjsr-layer {
  padding-bottom: 7rem !important;
}
.cookiesjsr-layer .cookiesjsr-layer--header, .cookiesjsr-layer .cookiesjsr-layer--footer, .cookiesjsr-layer .cookiesjsr-layer--body {
  background: #fff;
  color: black;
  border: none;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn {
  text-transform: none;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  width: 100%;
  color: black;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn.allowAll {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer {
  border: none !important;
  width: 100%;
  height: 7rem !important;
}
@media (max-width: 47.99875rem) {
  .cookiesjsr-layer .cookiesjsr-layer--footer {
    padding-left: 0;
  }
}
@media (min-width: 48rem) {
  .cookiesjsr-layer .cookiesjsr-layer--footer {
    padding-right: 0;
  }
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--label-all {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 5px;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn {
  margin: 0;
  width: 100%;
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
  padding: 0.5rem 1rem;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.save {
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.denyAll {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
  background-color: transparent;
  border: 2px solid #CED4DA;
  color: black;
}

.cookiesjsr-layer--actions .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert {
  background-color: #0066cc;
  border: none;
  color: #fff;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert:hover {
  background-color: #034b7d;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert {
  background: #fecc09;
  border: none;
  color: black;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert:hover {
  background-color: #ffab1a;
}

.cookiesjsr-btn {
  border-radius: 5px;
}

.cookiesjsr-service-group:not(.active) .cookiesjsr-service-group--tab {
  background: #fff;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: black;
}

.cookiesjsr-service-group button {
  border: none !important;
}
.cookiesjsr-service-group--intro, .cookiesjsr-service-group--service {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  line-height: 1.3;
}
.cookiesjsr-service-group--content {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -webkit-flex-direction: column !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}
.cookiesjsr-service-group--content .cookiesjsr-service-group--intro {
  -webkit-box-ordinal-group: 3 !important;
  -webkit-order: 2 !important;
      -ms-flex-order: 2 !important;
          order: 2 !important;
  padding-top: 0;
}
.cookiesjsr-service-group--content li.cookiesjsr-service:hover {
  background-color: #0056B3;
}

.cookiesjsr-service--always-on span {
  display: none;
}
.cookiesjsr-service--always-on::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch {
  background-color: transparent !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  margin-top: 0.3125rem !important;
  margin-left: 0.9375rem !important;
}
.cookiesjsr-layer .cookiesjsr-switch::after {
  display: none !important;
}
.cookiesjsr-layer .cookiesjsr-switch::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-off.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch.active::before {
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
}

@media (min-width: 48rem) {
  .cookiesjsr-service-group--tab, .cookiesjsr-service-group--content {
    width: 50% !important;
  }
}

.cookiesjsr-layer button.denyAll {
  display: block !important;
}

.sticky-info-default {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.625rem 1.25rem;
  background: #fecc09;
}
.sticky-info-default p {
  margin-bottom: 0;
}
.sticky-info-default img {
  display: inline;
}

.block-npx-sticky-info {
  right: 0;
  z-index: 999;
}

.path-frontpage .sticky-info-default p {
  margin-top: 0;
}
.path-frontpage .sticky-info-default a {
  color: #0053B3;
}

.npx-sticky-instance:not(:last-of-type) {
  padding-bottom: 0.625rem;
  position: relative;
}
.npx-sticky-instance:not(:last-of-type):after {
  content: "";
  position: absolute;
  left: -20px;
  bottom: 0;
  width: 100vw;
  height: 1px;
  background-color: #000;
}
.npx-sticky-instance:not(:first-of-type) {
  padding-top: 0.625rem;
}

/* GENERAL STYLES
-------------------------------------------------*/
.npx-bg-lazy-load {
  background-image: none !important;
}

body {
  font-family: "Muli", sans-serif;
  font-size: 1rem;
  line-height: 150%;
  color: #000;
  font-weight: 300;
}

body a {
  text-decoration: underline;
  outline: 0;
  color: #0053B3;
}
body a:hover, body a.npx-program-button {
  text-decoration: none;
}

p {
  font-size: 1rem;
}

body h1 {
  font-size: 2.5rem;
  margin: 0.66875rem 0;
}
body h1.page-title {
  color: #000;
  border-bottom: 1px solid #000;
  padding: 1.25rem 0;
  margin: 0.3125rem 0 1rem;
  font-weight: 800;
  line-height: 2.5rem;
  text-align: left;
}

body h2 {
  font-size: 2rem;
  line-height: 1.5;
  margin: 5rem 0 2.8125rem;
}
body h2.field-label-above {
  position: relative;
}

body h3, span.h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1.2rem 0;
  line-height: 150%;
  color: #000;
  font-weight: 700;
}

body h4, span.h4 {
  font-size: 1.2rem;
  margin: 1.4rem 0 0.8rem;
}

.npx-program-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  margin-right: 0.5rem;
}
.npx-program-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-form-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  display: table;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer;
}
.npx-form-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-program-button-dark {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
}
.npx-program-button-dark:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}

a.npx-form-button.npx-autolink, a.npx-form-button-inline.npx-autolink {
  text-transform: uppercase;
}

.npx-form-button-wrapper {
  text-align: center;
}

.limiter {
  margin-left: auto;
  margin-right: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .limiter {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  .limiter {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  .limiter {
    max-width: 960px;
  }
}
@media (min-width: 75rem) {
  .limiter {
    max-width: 1200px;
  }
}
@media (min-width: 87.5rem) {
  .limiter {
    max-width: 1350px;
  }
}
@media (min-width: 100rem) {
  .limiter {
    max-width: 1415px;
  }
}

#page {
  max-width: 100vw;
}

img {
  max-width: 100%;
  height: auto;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
