@charset "UTF-8";
@import url(https://fonts.googleapis.com/css?family=Muli:300,300i,400,600,700,800&subset=latin-ext&display=optional);
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

header {
  background: #fff;
  z-index: 10;
  max-width: 1415px;
  max-height: 66px;
}

.site-logo img {
  max-height: 66px;
  width: auto;
}

#block-bootstrap4grow-login {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
  padding-right: 4.75rem;
  padding-top: 1.375rem;
}
#block-bootstrap4grow-login a {
  z-index: 2;
}
#block-bootstrap4grow-login img {
  max-width: 25px;
  opacity: 0.6;
}

#block-markawitryny {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 70%;
      -ms-flex: 1 0 70%;
          flex: 1 0 70%;
}
@media (min-width: 65.625rem) {
  #block-markawitryny {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 auto;
        -ms-flex: 1 0 auto;
            flex: 1 0 auto;
  }
}

#block-grow3menu .sf-accordion-toggle {
  position: absolute;
  right: 0;
  top: 0;
  width: 70px;
  height: 70px;
  z-index: 100;
}

a#superfish-grow3menu-toggle {
  position: absolute;
  right: 0;
}
a#superfish-grow3menu-toggle span {
  display: block;
  text-indent: -9900px;
  background: transparent url(/themes/custom/bootstrap4grow/images/hamburger.png) no-repeat center center;
  width: 70px;
  height: 70px;
}

a#superfish-grow3menu-toggle.sf-expanded span {
  background-image: url(/themes/custom/bootstrap4grow/images/hamburgerON.png);
}

ul.sf-menu .sf-sub-indicator {
  display: none;
}

ul#superfish-grow3menu {
  max-width: 920px;
  margin-left: auto;
}
ul#superfish-grow3menu li.sf-depth-1 {
  position: static;
}
ul#superfish-grow3menu a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu a.sf-depth-1 {
  color: #000;
  font-size: 1.06rem;
  padding: 0 0.47rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-transform: uppercase;
  font-weight: 600;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 0.75rem;
  }
}
@media (min-width: 75rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem;
  }
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 1.5rem;
  }
}
ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
  content: " ";
  width: 32px;
  height: 2px;
  display: block;
  left: 50%;
  margin-left: -1rem;
  top: calc(100% + 23px);
  background-color: #ffab1a;
  position: absolute;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
    top: calc(100% + 7px);
  }
}
ul#superfish-grow3menu a {
  color: #000;
}

.n-breadcrumb {
  padding-left: 0.75rem;
  color: #000;
}
.n-breadcrumb a, .n-breadcrumb a:hover, .n-breadcrumb a:active, .n-breadcrumb a:focus {
  color: #000;
}
.n-breadcrumb li::before {
  content: " »";
  font-size: 1.2rem;
  padding: 0 0.5rem 0 0.25rem;
}
.n-breadcrumb li:first-child::before {
  content: none;
}
.n-breadcrumb.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1rem;
}

#main-wrapper {
  border-top: 1px solid #ebedec;
}

#cookies-overlay.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: black;
  opacity: 0.5;
  z-index: 55557;
}

.cookiesjsr--app .cookiesjsr-banner {
  z-index: 55558;
}

.cookiesjsr-banner--text,
.cookiesjsr-links.links--row li a {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: #000;
}

.cookiesjsr-banner--text a {
  color: #2A7DE3 !important;
  text-decoration: none;
}

.cookiesjsr-links.links--row li a {
  font-weight: 600;
}

.cookiesjsr--app .cookiesjsr-banner {
  background: #fff;
  -webkit-box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
          box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before {
  content: "";
  background-color: #000;
  border-radius: 100%;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
  content: "";
}
@media (max-width: 47.99875rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before,
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
    content: "";
    background-color: #000;
    border-radius: 100%;
    height: 0.3em;
    left: 0;
    position: absolute;
    top: 45%;
    width: 0.3em;
  }
}
.cookiesjsr--app .cookiesjsr-banner button {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  background: var(--secondary);
  border: 0;
  border-radius: 5px;
  text-transform: none;
}
.cookiesjsr--app .cookiesjsr-banner button:hover {
  background-color: #034b7d;
}
.cookiesjsr--app .cookiesjsr-banner.active {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr--app .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}

.cookiesjsr-banner .cookiesjsr-btn.important.denyAll,
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings {
  color: black;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll {
  background-color: #fecc09;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll:hover {
  background-color: #ffab1a;
}
.cookiesjsr-banner .cookiesjsr-btn.important.denyAll {
  background-color: transparent;
  border: 2px solid #CED4DA;
}
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings:hover {
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

.cookiesjsr-banner .cookiesjsr-banner--info {
  margin-bottom: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 1rem;
}
@media (min-width: 48rem) {
  .cookiesjsr-banner .cookiesjsr-banner--info {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.cookiesjsr-banner .cookiesjsr-banner--info .cookiesjsr-banner--links.links--row {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr-links, .cookiesjsr-banner--links.links--row {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-top: 0;
}

.cookiesjsr-banner--text {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  -webkit-flex-direction: column-reverse !important;
      -ms-flex-direction: column-reverse !important;
          flex-direction: column-reverse !important;
}
@media (min-width: 48rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -webkit-flex-direction: row-reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important;
    min-width: 60%;
    width: auto;
  }
}

.cookiesjsr-layer {
  border-radius: 5px;
}

.cookiesjsr-layer--body {
  margin: 0 1rem;
}
.cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab, .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
  background: #0056B3;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}
@media (max-width: 47.99875rem) {
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab {
    display: none;
  }
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
    border-radius: 4px;
  }
}

.cookiesjsr-layer .cookiesjsr-layer--header, .cookiesjsr-layer .cookiesjsr-layer--footer, .cookiesjsr-layer .cookiesjsr-layer--body {
  background: #fff;
  color: black;
  border: none;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn {
  text-transform: none;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  width: 100%;
  color: black;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn.allowAll {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer {
  border: none !important;
  width: 100%;
}
@media (min-width: 48rem) {
  .cookiesjsr-layer .cookiesjsr-layer--footer {
    padding-right: 0;
  }
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--label-all {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 10px;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn {
  margin: 0;
  width: 100%;
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.save {
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.denyAll {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
}

.cookiesjsr-layer--actions .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert {
  background-color: #0066cc;
  border: none;
  color: #fff;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert:hover {
  background-color: #034b7d;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert {
  background: #fecc09;
  border: none;
  color: black;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert:hover {
  background-color: #ffab1a;
}

.cookiesjsr-btn {
  border-radius: 5px;
}

.cookiesjsr-service-group:not(.active) .cookiesjsr-service-group--tab {
  background: #fff;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: black;
}

.cookiesjsr-service-group button {
  border: none !important;
}
.cookiesjsr-service-group--intro, .cookiesjsr-service-group--service {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}
.cookiesjsr-service-group--content {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -webkit-flex-direction: column !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}
.cookiesjsr-service-group--content .cookiesjsr-service-group--intro {
  -webkit-box-ordinal-group: 3 !important;
  -webkit-order: 2 !important;
      -ms-flex-order: 2 !important;
          order: 2 !important;
  padding-top: 0;
}
.cookiesjsr-service-group--content li.cookiesjsr-service:hover {
  background-color: #0056B3;
}

.cookiesjsr-service--always-on span {
  display: none;
}
.cookiesjsr-service--always-on::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch {
  background-color: transparent !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  margin-top: 0.3125rem !important;
  margin-left: 0.9375rem !important;
}
.cookiesjsr-layer .cookiesjsr-switch::after {
  display: none !important;
}
.cookiesjsr-layer .cookiesjsr-switch::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-off.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch.active::before {
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
}

@media (min-width: 48rem) {
  .cookiesjsr-service-group--tab, .cookiesjsr-service-group--content {
    width: 50% !important;
  }
}

.sticky-info-default {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.625rem 1.25rem;
  background: #fecc09;
}
.sticky-info-default p {
  margin-bottom: 0;
}
.sticky-info-default img {
  display: inline;
}

.block-npx-sticky-info {
  right: 0;
  z-index: 999;
}

.path-frontpage .sticky-info-default p {
  margin-top: 0;
}
.path-frontpage .sticky-info-default a {
  color: #0053B3;
}

.npx-sticky-instance:not(:last-of-type) {
  padding-bottom: 0.625rem;
  position: relative;
}
.npx-sticky-instance:not(:last-of-type):after {
  content: "";
  position: absolute;
  left: -20px;
  bottom: 0;
  width: 100vw;
  height: 1px;
  background-color: #000;
}
.npx-sticky-instance:not(:first-of-type) {
  padding-top: 0.625rem;
}

/* GENERAL STYLES
-------------------------------------------------*/
.npx-bg-lazy-load {
  background-image: none !important;
}

body {
  font-family: "Muli", sans-serif;
  font-size: 1rem;
  line-height: 150%;
  color: #000;
  font-weight: 300;
}

body a {
  text-decoration: underline;
  outline: 0;
  color: #0053B3;
}
body a:hover, body a.npx-program-button {
  text-decoration: none;
}

p {
  font-size: 1rem;
}

body h1 {
  font-size: 2.5rem;
  margin: 0.66875rem 0;
}
body h1.page-title {
  color: #000;
  border-bottom: 1px solid #000;
  padding: 1.25rem 0;
  margin: 0.3125rem 0 1rem;
  font-weight: 800;
  line-height: 2.5rem;
  text-align: left;
}

body h2 {
  font-size: 2rem;
  line-height: 1.5;
  margin: 5rem 0 2.8125rem;
}
body h2.field-label-above {
  position: relative;
}

body h3, span.h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1.2rem 0;
  line-height: 150%;
  color: #000;
  font-weight: 700;
}

body h4, span.h4 {
  font-size: 1.2rem;
  margin: 1.4rem 0 0.8rem;
}

.npx-program-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  margin-right: 0.5rem;
}
.npx-program-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-form-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  display: table;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer;
}
.npx-form-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-program-button-dark {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
}
.npx-program-button-dark:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}

a.npx-form-button.npx-autolink, a.npx-form-button-inline.npx-autolink {
  text-transform: uppercase;
}

.npx-form-button-wrapper {
  text-align: center;
}

.limiter {
  margin-left: auto;
  margin-right: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .limiter {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  .limiter {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  .limiter {
    max-width: 960px;
  }
}
@media (min-width: 75rem) {
  .limiter {
    max-width: 1200px;
  }
}
@media (min-width: 87.5rem) {
  .limiter {
    max-width: 1350px;
  }
}
@media (min-width: 100rem) {
  .limiter {
    max-width: 1415px;
  }
}

#page {
  max-width: 100vw;
}

img {
  max-width: 100%;
  height: auto;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
