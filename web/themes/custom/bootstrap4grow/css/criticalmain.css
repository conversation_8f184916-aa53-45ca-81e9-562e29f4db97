@charset "UTF-8";
@import url(https://fonts.googleapis.com/css?family=Muli:300,300i,400,600,700,800&subset=latin-ext&display=optional);
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

header {
  background: #fff;
  z-index: 10;
  max-width: 1415px;
  max-height: 66px;
}

.site-logo img {
  max-height: 66px;
  width: auto;
}

#block-bootstrap4grow-login {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
  padding-right: 4.75rem;
  padding-top: 1.375rem;
}
#block-bootstrap4grow-login a {
  z-index: 2;
}
#block-bootstrap4grow-login img {
  max-width: 25px;
  opacity: 0.6;
}

#block-markawitryny {
  -webkit-box-flex: 1;
  -webkit-flex: 1 0 70%;
      -ms-flex: 1 0 70%;
          flex: 1 0 70%;
}
@media (min-width: 65.625rem) {
  #block-markawitryny {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 auto;
        -ms-flex: 1 0 auto;
            flex: 1 0 auto;
  }
}

#block-grow3menu .sf-accordion-toggle {
  position: absolute;
  right: 0;
  top: 0;
  width: 70px;
  height: 70px;
  z-index: 100;
}

a#superfish-grow3menu-toggle {
  position: absolute;
  right: 0;
}
a#superfish-grow3menu-toggle span {
  display: block;
  text-indent: -9900px;
  background: transparent url(/themes/custom/bootstrap4grow/images/hamburger.png) no-repeat center center;
  width: 70px;
  height: 70px;
}

a#superfish-grow3menu-toggle.sf-expanded span {
  background-image: url(/themes/custom/bootstrap4grow/images/hamburgerON.png);
}

ul.sf-menu .sf-sub-indicator {
  display: none;
}

ul#superfish-grow3menu {
  max-width: 920px;
  margin-left: auto;
}
ul#superfish-grow3menu li.sf-depth-1 {
  position: static;
}
ul#superfish-grow3menu a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu a.sf-depth-1 {
  color: #000;
  font-size: 1.06rem;
  padding: 0 0.47rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-transform: uppercase;
  font-weight: 600;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 0.75rem;
  }
}
@media (min-width: 75rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem;
  }
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu a.sf-depth-1 {
    padding: 1rem 1.5rem;
  }
}
ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
  content: " ";
  width: 32px;
  height: 2px;
  display: block;
  left: 50%;
  margin-left: -1rem;
  top: calc(100% + 23px);
  background-color: #ffab1a;
  position: absolute;
  text-decoration: none;
}
@media (min-width: 68.8125rem) {
  ul#superfish-grow3menu a.sf-depth-1:hover::before, ul#superfish-grow3menu a.sf-depth-1.is-active::before {
    top: calc(100% + 7px);
  }
}
ul#superfish-grow3menu a {
  color: #000;
}

.n-breadcrumb {
  padding-left: 0.75rem;
  color: #000;
}
.n-breadcrumb a, .n-breadcrumb a:hover, .n-breadcrumb a:active, .n-breadcrumb a:focus {
  color: #000;
}
.n-breadcrumb li::before {
  content: " »";
  font-size: 1.2rem;
  padding: 0 0.5rem 0 0.25rem;
}
.n-breadcrumb li:first-child::before {
  content: none;
}
.n-breadcrumb.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1rem;
}

#main-wrapper {
  border-top: 1px solid #ebedec;
}

#cookies-overlay.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: black;
  opacity: 0.5;
  z-index: 55557;
}

.cookiesjsr--app .cookiesjsr-banner {
  z-index: 55558;
}

.cookiesjsr-banner--text,
.cookiesjsr-links.links--row li a {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: #000;
}

.cookiesjsr-banner--text a {
  color: #2A7DE3 !important;
  text-decoration: none;
}

.cookiesjsr-links.links--row li a {
  font-weight: 600;
}

.cookiesjsr--app .cookiesjsr-banner {
  background: #fff;
  -webkit-box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
          box-shadow: 0px -4px 36px -5px rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before {
  content: "";
  background-color: #000;
  border-radius: 100%;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
  content: "";
}
@media (max-width: 47.99875rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li::before,
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-links.links--row li:first-child::before {
    content: "";
    background-color: #000;
    border-radius: 100%;
    height: 0.3em;
    left: 0;
    position: absolute;
    top: 45%;
    width: 0.3em;
  }
}
.cookiesjsr--app .cookiesjsr-banner button {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  background: var(--secondary);
  border: 0;
  border-radius: 5px;
  text-transform: none;
}
.cookiesjsr--app .cookiesjsr-banner button:hover {
  background-color: #034b7d;
}
.cookiesjsr--app .cookiesjsr-banner.active {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr--app .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}

.cookiesjsr-banner .cookiesjsr-btn.important.denyAll,
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings {
  color: black;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll {
  background-color: #fecc09;
}
.cookiesjsr-banner .cookiesjsr-btn.important.allowAll:hover {
  background-color: #ffab1a;
}
.cookiesjsr-banner .cookiesjsr-btn.important.denyAll {
  background-color: transparent;
  border: 2px solid #CED4DA;
}
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings,
.cookiesjsr-banner .cookiesjsr-btn.cookiesjsr-settings:hover {
  background-color: transparent;
  border: none;
  text-decoration: underline;
}

.cookiesjsr-banner .cookiesjsr-banner--info {
  margin-bottom: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 1rem;
}
@media (min-width: 48rem) {
  .cookiesjsr-banner .cookiesjsr-banner--info {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.cookiesjsr-banner .cookiesjsr-banner--info .cookiesjsr-banner--links.links--row {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.cookiesjsr-links, .cookiesjsr-banner--links.links--row {
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-top: 0;
}

.cookiesjsr-banner--text {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  -webkit-flex-direction: column-reverse !important;
      -ms-flex-direction: column-reverse !important;
          flex-direction: column-reverse !important;
}
@media (min-width: 48rem) {
  .cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -webkit-flex-direction: row-reverse !important;
        -ms-flex-direction: row-reverse !important;
            flex-direction: row-reverse !important;
    min-width: 60%;
    width: auto;
  }
}

.cookiesjsr-layer {
  border-radius: 5px;
}

.cookiesjsr-layer--body {
  margin: 0 1rem;
  bottom: 6em !important;
}
.cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab, .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
  background: #0056B3;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}
@media (max-width: 47.99875rem) {
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--tab {
    display: none;
  }
  .cookiesjsr-layer--body .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
    border-radius: 4px;
  }
}

.cookiesjsr-layer {
  padding-bottom: 6em !important;
}
.cookiesjsr-layer .cookiesjsr-layer--header, .cookiesjsr-layer .cookiesjsr-layer--footer, .cookiesjsr-layer .cookiesjsr-layer--body {
  background: #fff;
  color: black;
  border: none;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn {
  text-transform: none;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  width: 100%;
  color: black;
}
.cookiesjsr-layer .cookiesjsr-layer--header .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-btn.allowAll, .cookiesjsr-layer .cookiesjsr-layer--body .cookiesjsr-btn.allowAll {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer {
  border: none !important;
  width: 100%;
  height: 6em !important;
}
@media (min-width: 48rem) {
  .cookiesjsr-layer .cookiesjsr-layer--footer {
    padding-right: 0;
  }
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--label-all {
  display: none;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 5px;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn {
  margin: 0;
  width: 100%;
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
  padding: 0.5rem 1rem;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.save {
  -webkit-box-ordinal-group: 2;
  -webkit-order: 1;
      -ms-flex-order: 1;
          order: 1;
}
.cookiesjsr-layer .cookiesjsr-layer--footer .cookiesjsr-layer--actions .cookiesjsr-btn.denyAll {
  -webkit-box-ordinal-group: 3;
  -webkit-order: 2;
      -ms-flex-order: 2;
          order: 2;
}

.cookiesjsr-layer--actions .cookiesjsr-btn:hover {
  -webkit-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1);
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert {
  background-color: #0066cc;
  border: none;
  color: #fff;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.invert:hover {
  background-color: #034b7d;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert {
  background: #fecc09;
  border: none;
  color: black;
}
.cookiesjsr-layer--actions .cookiesjsr-btn.important.invert:hover {
  background-color: #ffab1a;
}

.cookiesjsr-btn {
  border-radius: 5px;
}

.cookiesjsr-service-group:not(.active) .cookiesjsr-service-group--tab {
  background: #fff;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
  color: black;
}

.cookiesjsr-service-group button {
  border: none !important;
}
.cookiesjsr-service-group--intro, .cookiesjsr-service-group--service {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 400;
}
.cookiesjsr-service-group--content {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -webkit-flex-direction: column !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}
.cookiesjsr-service-group--content .cookiesjsr-service-group--intro {
  -webkit-box-ordinal-group: 3 !important;
  -webkit-order: 2 !important;
      -ms-flex-order: 2 !important;
          order: 2 !important;
  padding-top: 0;
}
.cookiesjsr-service-group--content li.cookiesjsr-service:hover {
  background-color: #0056B3;
}

.cookiesjsr-service--always-on span {
  display: none;
}
.cookiesjsr-service--always-on::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch {
  background-color: transparent !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  margin-top: 0.3125rem !important;
  margin-left: 0.9375rem !important;
}
.cookiesjsr-layer .cookiesjsr-switch::after {
  display: none !important;
}
.cookiesjsr-layer .cookiesjsr-switch::before {
  content: "";
  display: inline-block;
  width: 32px;
  height: 16px;
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-off.svg");
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
}

.cookiesjsr-layer .cookiesjsr-switch.active::before {
  background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
}

@media (min-width: 48rem) {
  .cookiesjsr-service-group--tab, .cookiesjsr-service-group--content {
    width: 50% !important;
  }
}

.sticky-info-default {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  text-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.625rem 1.25rem;
  background: #fecc09;
}
.sticky-info-default p {
  margin-bottom: 0;
}
.sticky-info-default img {
  display: inline;
}

.block-npx-sticky-info {
  right: 0;
  z-index: 999;
}

.path-frontpage .sticky-info-default p {
  margin-top: 0;
}
.path-frontpage .sticky-info-default a {
  color: #0053B3;
}

.npx-sticky-instance:not(:last-of-type) {
  padding-bottom: 0.625rem;
  position: relative;
}
.npx-sticky-instance:not(:last-of-type):after {
  content: "";
  position: absolute;
  left: -20px;
  bottom: 0;
  width: 100vw;
  height: 1px;
  background-color: #000;
}
.npx-sticky-instance:not(:first-of-type) {
  padding-top: 0.625rem;
}

/* GENERAL STYLES
-------------------------------------------------*/
.npx-bg-lazy-load {
  background-image: none !important;
}

body {
  font-family: "Muli", sans-serif;
  font-size: 1rem;
  line-height: 150%;
  color: #000;
  font-weight: 300;
}

body a {
  text-decoration: underline;
  outline: 0;
  color: #0053B3;
}
body a:hover, body a.npx-program-button {
  text-decoration: none;
}

p {
  font-size: 1rem;
}

body h1 {
  font-size: 2.5rem;
  margin: 0.66875rem 0;
}
body h1.page-title {
  color: #000;
  border-bottom: 1px solid #000;
  padding: 1.25rem 0;
  margin: 0.3125rem 0 1rem;
  font-weight: 800;
  line-height: 2.5rem;
  text-align: left;
}

body h2 {
  font-size: 2rem;
  line-height: 1.5;
  margin: 5rem 0 2.8125rem;
}
body h2.field-label-above {
  position: relative;
}

body h3, span.h3 {
  font-size: 1.5rem;
  margin: 2rem 0 1.2rem 0;
  line-height: 150%;
  color: #000;
  font-weight: 700;
}

body h4, span.h4 {
  font-size: 1.2rem;
  margin: 1.4rem 0 0.8rem;
}

.npx-program-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  margin-right: 0.5rem;
}
.npx-program-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-form-button {
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 1.5rem;
  padding: 0.75rem 1.875rem;
  width: auto;
  border: none;
  margin: 0.625rem auto 0.625rem 0;
  -o-transition: all 0.5s;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  background: #fecc09;
  display: inline-block;
  max-width: 100%;
  display: table;
  margin-left: auto;
  margin-right: auto;
  cursor: pointer;
}
.npx-form-button:hover {
  text-decoration: none;
  background-color: #ffab1a;
  color: #191919;
}

.npx-program-button-dark {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
}
.npx-program-button-dark:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}

a.npx-form-button.npx-autolink, a.npx-form-button-inline.npx-autolink {
  text-transform: uppercase;
}

.npx-form-button-wrapper {
  text-align: center;
}

.limiter {
  margin-left: auto;
  margin-right: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .limiter {
    max-width: 450px;
  }
}
@media (min-width: 48rem) {
  .limiter {
    max-width: 720px;
  }
}
@media (min-width: 62rem) {
  .limiter {
    max-width: 960px;
  }
}
@media (min-width: 75rem) {
  .limiter {
    max-width: 1200px;
  }
}
@media (min-width: 87.5rem) {
  .limiter {
    max-width: 1350px;
  }
}
@media (min-width: 100rem) {
  .limiter {
    max-width: 1415px;
  }
}

#page {
  max-width: 100vw;
}

img {
  max-width: 100%;
  height: auto;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
