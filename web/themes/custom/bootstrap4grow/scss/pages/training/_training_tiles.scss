.npx-blocks-program-tab-wrapper {
  transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;

  &.expanded {
    height: auto !important;
    min-height: 300px;
    transition: all .3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  }

  @include media-breakpoint-up(md) {
    &:hover {
      transform: scale(1.02);
    }
  }

  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: $gray-100;
  box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);

  .npx-training-form-tab-header img {
    border-top-left-radius: 8px;
    width: 100%;
  }

  &.d-flex.flex-md-row {
    @include media-breakpoint-up(md) {
      .n-tab-header {
        flex: 0 0 220px;
        max-width: 220px;
        min-width: 220px;
      }

      .npx-training-form-tab-content {
        flex: 1;
        min-width: 0;

        ul li {
          font-size: 14px;
        }
      }
    }

    @include media-breakpoint-down(md) {
      flex-direction: column !important;

      .n-tab-header {
        flex: none;
        max-width: none;
        min-width: none;
      }
    }
  }
}

.tile-training-cta a {
  color: $color-link;
  text-decoration: underline;
  font-size: $font-size-content;
  line-height: 1.5;
  background: transparent !important;
}

.npx-training-form-tab-header-info {
  .t3,
  .t3b {
    color: $white;
    font-size: $font-size-content;
    line-height: 1.5;
  }
  .t3b {
    font-weight: $font-weight-bolder;
  }
  &-note.t3 {
    line-height: 1.2;
  }
}

.npx-training-form-tab-header-inner {
  border-bottom-left-radius: 8px;
}

.npx-training-form-tab-header-info {
  height:100%;
  padding: 10px;
  background-color: $color-link;
}

.npx-training-form-tab-header-top {
  padding: 10px;
  position: relative;
}

.npx-training-form-tab-header img {
  height: 120px;
}

.npx-training-form-tab-header-title h3 {
  font-size: $font-size-title;
  font-weight: $font-weight-bolder;
  line-height: 1.2;
  color: $white;
  text-shadow: none;
}

.npx-training-form-tab-header-type {
  text-align: left !important;
  position: absolute;
  bottom: 0.5rem;
  left: 10px;
  right: 10px;
}

.npx-program-tabs-wrapper .nav-tabs > li:first-child {
  padding-left: 0;
}

.npx-program-tabs-wrapper .nav-tabs > li:last-child {
  padding-right: 0;
}

.npx-training-form-tab-header-info-row.long-text .t3b {
  white-space: normal;
  line-height: 1.2;
}

.training-inquiry-link {
  display: inline-block;
  color: $white;
  text-decoration: none;
  font-size: $font-size-small;
  line-height: 1.2;
  font-weight: $font-weight-bolder;

  &:hover {
    color: $white;
    text-decoration: none;
  }

  &:focus {
    text-decoration: none;
  }

  &:visited {
    color: $white;
  }
}

.content-column {
  min-width: 0;

  .content-row {
    .t3 {
      white-space: nowrap;
      flex-shrink: 0;
    }

    .t3b {
      text-align: left;
      flex-shrink: 1;
      min-width: 0;
    }
  }
}

.npx-training-form-tab-header-info-row {
  .t3 {
    width: 90px;
    flex-shrink: 0;
  }

  i {
    margin-right: 3px;
  }
}
