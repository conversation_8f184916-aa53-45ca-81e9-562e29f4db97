.npx-tile-textbox-wrapper {
  transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;

  &.expanded {
    height: auto !important;
    min-height: 300px;
    transition: all .3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  }

  @include media-breakpoint-up(md) {
    &:hover {
      transform: scale(1.02);
    }
  }

  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: $gray-100;
  box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 12px;
    background: linear-gradient(269.82deg, rgba(0, 86, 179, 0.8) 0.18%, $link-color 99.87%);
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    pointer-events: none;
  }

  h3 {
    margin-top: 0;
  }

  .npx-tile-textbox-content h3 p {
    font-weight: $font-weight-bolder;
    font-size: $font-size-title;
    line-height: 1.1;
    color: $color-primary;
    margin-bottom: 0;
  }

  .npx-tile-textbox-content h3::after {
    content: "";
    display: block;
    width: 50px;
    height: 6px;
    border-radius: 16px;
    background: $color-accent;
    margin-top: 8px;
  }
  .npx-tile-textbox-content {
    margin-left: 1rem;

    span, p {
      font-size: $font-size-content;
      color: $color-text;
      line-height: 1.5;
    }

    a span {
      color: $color-link;
      text-decoration: underline;
    }
  }

  &.has-overflow::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 8px;
    height: 4.5rem;
    background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  &.expanded.has-overflow::after {
    opacity: 0;
  }
}
