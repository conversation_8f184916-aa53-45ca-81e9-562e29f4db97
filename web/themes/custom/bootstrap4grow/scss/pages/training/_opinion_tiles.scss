.npx-tile-opinion-wrapper {
  transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 300px !important;
  overflow: hidden;
  position: relative;

  &.expanded {
    height: auto !important;
    min-height: 300px;
    transition: all .3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s ease-in-out;
  }

  @include media-breakpoint-up(md) {
    &:hover {
      transform: scale(1.02);
    }
  }

  border-radius: 8px;
  border-style: solid;
  border-width: 1px;
  border-color: $gray-100;
  box-shadow: 0px 8px 24px 0px rgba(125, 125, 125, 0.15);

  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 12px;
    background: linear-gradient(269.82deg, rgba(0, 86, 179, 0.8) 0.18%, $link-color 99.87%);
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    pointer-events: none;
  }

  .tile-opinion-title {
    font-weight: $font-weight-bolder;
    font-size: $font-size-title;
    line-height: 1.1;
    color: $color-primary;
    margin: 0 0 20px 8px;
    position: relative;
    padding-left: 6rem;
    min-height: 32px;
  }
  .tile-opinion-title::before {
    content: "";
    position: absolute;
    left: 8px;
    top: 0;
    width: 50px;
    height: 32px;
    background: url("../images/quotes.svg") no-repeat center center / contain;
  }
  .tile-opinion-title::after {
    content: "";
    position: absolute;
    left: 8px;
    top: 40px;
    width: 50px;
    height: 6px;
    border-radius: 16px;
    background: $color-accent;
  }

  .tile-opinion-image img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid $color-accent-alt;
  }
  .tile-opinion-text {
    margin-left: 8px;
  }
  .tile-opinion-author-name {
    color: $body-color;
    font-weight: $font-weight-bolder;
    font-size: $font-size-small;
    line-height: 1.2;
  }
  .tile-opinion-author-company {
    color: $body-color;
  }
  .tile-opinion-linkedin {
    position: relative;
    text-indent: -9999px;
    display: inline-block;
    width: 46px;
    height: 11px;
    margin-top: 5px;
    background-size: contain;
    background-repeat: no-repeat;
  }
  .tile-opinion-linkedin::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("../images/linkedin-logo.png");
    filter: grayscale(100%);
    background-size: contain;
    background-repeat: no-repeat;
  }

  &.has-overflow::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 8px;
    height: 4.5rem;
    background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.6) 60%, rgba(255, 255, 255, 0.9) 100%);
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  &.expanded.has-overflow::after {
    opacity: 0;
  }
}

.tile-opinion-cta a {
  color: $color-link;
  text-decoration: underline;
  font-size: $font-size-content;
  line-height: 1.5;
  background: transparent !important;
}

.tile-opinion-content,
.tile-opinion-author  {
  margin-left: 8px;
}

.tile-opinion-content {
  font-size: $font-size-content;
  color: $color-text;
  line-height: 1.5;
}

.tile-opinion-author-name {
  font-size: $font-size-small !important;
  line-height: 1.3 !important;
  white-space: normal !important;

  br {
    display: block !important;
    line-height: 1.3 !important;
    margin: 0 !important;
  }

  br + br {
    margin-top: 0 !important;
  }
}
