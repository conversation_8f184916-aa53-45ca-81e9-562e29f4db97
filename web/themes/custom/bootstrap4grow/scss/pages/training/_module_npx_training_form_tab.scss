.npx-counter-wrapper {
  margin-left: -4px;
}
.npx-tabs {
  justify-content: center;
  align-content: stretch;
  flex-direction: column;
  @include media-breakpoint-up(md) {
    justify-content: flex-start;
    flex-direction: row;
  }
}
.npx-box-left {
  @include media-breakpoint-up(xl) {
    padding-right: 1rem;
  }
  @include media-breakpoint-down(xl) {
    order: 2;
  }
}
.npx-counter-info {
  color: red;
}
.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: cover;
  margin-right: 6px;
  margin-left: 5px;
}
#npx-price-info-wrapper {
  font-size: 14px;
  .form-item-npx-discount-code {
    position: relative;
    display: inline-block;
    input[type=text] {
      margin: 0;
      height: 32px;
      -moz-box-sizing: border-box;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      border-width: 1px;
      width: 100%;
      max-width: 200px;
    }
    .field-suffix {
      background-color: #fff;
      border: 0;
      display: inline-block;
      a {
        display: block;
        position: absolute;
        z-index: 10;
        height: 34px;
        background: transparent url("../images/przelicz.png") no-repeat center center;
        width: 20px;
        text-align: left;
        text-indent: -9990px;
        right: 10px;
        top: 0;
        outline: 0;
        border: 0;
        margin-right: 4px;
      }
    }
  }
  #npx-expand-bottom-wrapper {
    width: 100%;
    @include media-breakpoint-up(md) {
      width: auto;
      margin-right: 16px;
    }
  }
  .list-group-item {
    border: none;
    display: list-item;
    margin-left: spacer(5);
    padding: 0;
  }
  .item-list {
    padding-top: 48px;
  }
  li {
    list-style-image: url("../images/li.png");
  }
  .npx-social-colorbox-link {
    @include media-breakpoint-up(md) {
      top: -15px;
    }
    a {
      &::before {
        vertical-align: sub;
        height: 20px;
        width: 20px;
        margin: 0 5px 0 0;
        background: transparent url("../images/price-tag.png") no-repeat center center;
        background-size: auto;
        background-size: auto;
        background-size: cover;
        display: inline-block;
        content: "";
      }
    }
  }
}
.npx-box-left .npx-price-b {
  @include media-breakpoint-up(md) {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -15px;
    &-a {
      margin-top: 15px;
    }
  }
  @include media-breakpoint-up(xl) {
    margin-top: 0;
    &-a {
      margin-top: 0
    }
  }
}
.npx-price {
  @include media-breakpoint-down(sm) {
    min-height: 150px;
  }
  &-a {
    padding: spacer(6 7 0 0);
    &-a {
      line-height: 20px;
      font-size: 14px;
    }
    &-b {
      font-size: 20px;
    }
  }
  &-b {
    width: 100%;
    @include media-breakpoint-up(md) {
      width: 60%;
      padding: inherit;
      padding-top: 50px;
    }
    @include media-breakpoint-up(lg) {
      padding-top: 50px;
      width: auto;
    }
    &-a {
      font-size: 20px;
    }
    &-b {
      font-size: 18px;
    }
    &-c {
      color: #a2a2a2;
      @include media-breakpoint-down(md) {
        font-size: 13px;
      }
      @include media-breakpoint-up(xl) {
        top: 5px;
      }
    }
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
  @include media-breakpoint-up(md) {
    top: -10px;
  }
}
.npx-calculation-box {
  padding: spacer(6 6 0);
  margin: 0 -30px;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
  @include media-breakpoint-up(sm) {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
  .list-group-item {
    background: transparent;
    padding-left: 0;
  }
  input {
    max-width: 200px;
  }
}
#npx-participants-amount-wrapper {
  .description {
    font-size: 1em;
  }
  small.text-muted {
    max-width: calc(100% - 149px);
    float: right;
    color: #000!important;
    font-size: $font-size-base;
    @include media-breakpoint-up(sm) {
      float: none;
      max-width: 100%;
    }
  }
  span.ui-spinner {
    display: inline-block;
    position: relative;
    border: 1px solid #d0d8db;
    padding: 0 45px;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    margin: spacer(0 2 0 0);
    .form-control:focus {
      background-color: #fff;
      border-color: transparent;
      -webkit-box-shadow: none;
      box-shadow: none;
    }
    input {
      border: 0;
      height: 44px;
      line-height: 45px;
      padding: spacer(0 2);
      margin: 0;
      -moz-border-radius: 0;
      -webkit-border-radius: 0;
      border-radius: 0;
      width: 45px;
      text-align: center;
      @include media-breakpoint-up(sm) {
        height: 44px;
      }
    }
    a.ui-spinner-button {
      border: 0;
      outline: 0;
      position: absolute;
      left: 0;
      top: 0;
      height: 45px;
      width: 45px;
      background-color: transparent;
      opacity: .85;
      padding: 0;
      margin: 0;
      right: auto;
      background-image: url("../images/spinner-min.png");
      background-position: center center;
      background-repeat: no-repeat;
      &:hover {
        background-color: #ebeff2;
        cursor: pointer;
      }
      &.ui-corner-tr {
        background-image: url("../images/spinner-plus.png");
        left: auto;
        right: 0;
        border-left: 1px solid #ddd;
      }
      &.ui-corner-br {
        border-right: 1px solid #ddd;
      }
    }
  }
}
#szkolenie-grupa-8 {
  a.npx-form-tab {
    margin: spacer(3 0);
    @include media-breakpoint-up(md) {
      padding: 17px;
      margin: spacer(5 3);
    }
  }
  .form-item-npx-training {
    display: none;
  }
}
a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  transition: all .2s ease-in-out;
  @include media-breakpoint-up(md) {
    flex: 1 0 45%;
    max-width: 45%;
  }
  @include media-breakpoint-up(xl) {
    flex: 1 0 29%;
    max-width: 29%;
  }
  &:hover {
    transform: scale(1.1);
    z-index: 101;
  }
  &.npx-active-tab {
    transform: scale(1.1);
    z-index: 100;
  }
}
.npx-form-outer-wrapper {
  -moz-box-shadow: 0 0 50px 5px #f1f1f1;
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}
#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 4.8px;
  @include media-breakpoint-up(sm) {
    padding: spacer(0 7);
  }
}
.npx-blocks-program-tab-wrapper {
  transition: all .2s ease-in-out;
  border: solid rgba(128,128,128,.74) 1px;
  border-radius: 4px;
  @include media-breakpoint-up(md) {
    height: 100%;
  }
}
li a.active, li a:hover {
  .npx-blocks-program-tab-wrapper {
    transform: scale(1.05);
    box-shadow: 0 0 15px 0 #888;
  }
}
.npx-program-tabs-wrapper {
  border-radius: 4px;
  .list-group-item {
    border: 0;
  }
  .nav-tabs {
    list-style: none;
    border-bottom: 0;
    & > li {
      @include media-breakpoint-up(md) {
        flex: 1 0 48%;
        max-width: 48%;
      }
      @include media-breakpoint-up(lg) {
        flex: 1 0 31%;
        max-width: 31%;
      }
      @include media-breakpoint-up(xl) {
        flex: 1 0 25%;
        max-width: 25%;
      }
    }
    li a {
      text-align: left;
      background: #f3f3f5;
      margin-bottom: spacer(3);
    }
    a {
      text-decoration: none;
      color: #000;
      &:hover {
        text-decoration: none;
      }
    }
    a.active {
      text-transform: none;
      .npx-blocks-program-tab-wrapper {
        transform: scale(1.05);
        box-shadow: 0 0 15px 0 #888;
      }
    }
  }
}
#npx-regular-box-wrapper {
  opacity: 0.65;
}
.npx-box-right, .npx-box-left {
  &:not(.npx-active-box) {
    .npx-price {
      opacity: 0.65;
      &-b-a {
        text-decoration: line-through;
      }
      @include media-breakpoint-down(xl) {
        display: none!important;
      }
    }
  }
}
#npx-regular-box-wrapper {
  .npx-price-b-c {
    top: 0;
  }
}
.npx-active-tab, .npx-form-tab:hover {
  .npx-training-form-tab-wrapper {
    -webkit-box-shadow: 0 0 15px 0 #54534f;
    -moz-box-shadow: 0 0 15px 0 #54534f;
    box-shadow: 0 0 15px 0 #54534f;
    border-radius: 4px;
  }
}
.npx-training-form-type-info-wrapper {
  .n-type-header-inner {
    .n-type-word {
      width: 100%;
      padding: spacer(0 5);
      @include media-breakpoint-up(sm) {
        width: auto;
      }
    }
    .n-type-word-last {
      padding-left: spacer(2);
    }
  }
  .npx-spoiler-content {
    font-size: inherit;
  }
  .two-columns-template .col-sm ul li {
    list-style-image: url("../images/online-li-yellow.png");
  }
  .two-columns-template .col-sm:nth-child(1) ul li {
    list-style-image: url("../images/online-li-blue.png");
  }
  .n-spoiler-toggle {
    color: #034b7d;
    line-height: 30px;
    font-size: 10px;
    z-index: 20;
    border: 1px solid #034b7d;
    background-color: #fff;
    cursor: pointer;
  }
  .npx-spoiler-toggle {
    color: #034b7d;
    line-height: 30px;
    font-size: 10px;
    z-index: 20;
    border: 1px solid #034b7d;
    cursor: pointer;
    &.show-icon::before {
      content: "ROZWIŃ OPIS";
    }
    &.hide-icon::before {
      content: "ZWIŃ OPIS";
    }
  }
  .stationary {
    padding: spacer(2px 4);
    background: #ffc60c;
    border-radius: 20px;
    border: #ffc60c 2px solid;
    font-weight: 600;
  }
  .live-online {
    padding: spacer(2px 4);
    border-radius: 20px;
    font-weight: 600;
    background: var(--secondary);
  }
}
.tr-form {
  &-stationary {
    padding: spacer(3px 4);
    background: #ffc60c;
    border-radius: 20px;
    font-weight: 800;
    color: #000;
    margin-left: spacer(2);
    text-transform: uppercase;
    font-size: 0.625rem;
  }
  &-online {
    padding: spacer(3px 4);
    border-radius: 20px;
    font-weight: 800;
    background: var(--secondary);
    color: #fff;
    margin-left: spacer(2);
    text-transform: uppercase;
    font-size: 0.625rem;
  }
}
.npx-variant {
  .fieldset-legend {
    color: #000;
    margin: 2rem 0;
    display: block;
  }
  h4 {
    line-height: 1.5rem;
  }
}
.npx-training-form-tab {
  &-wrapper {
    transition: all .2s ease-in-out;
    border: solid rgba(128,128,128,.44) 1px;
    border-radius: 4px;
    height: 100%;
    .n-tab-header-inner {
      background: rgba(0,0,0,.55);
    }
  }
  &-header-inner {
    border-radius: 4px 4px 0 0;
    min-height: 183px;
    background: rgba(0,0,0,.55);
  }
  &-header {
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 4px 4px 0 0;
  }
  &-header-hours {
    text-shadow: none;
    font-size: 12px;
    flex: 1;
  }
  &-header-type {
    flex-wrap: wrap;
    font-size: 10px;
    .stationary {
      padding: spacer(2px 8px);
      background: #ffc60c;
      border-radius: 20px;
      border: #ffc60c 2px solid;
    }
    .live-online {
      padding: spacer(2px 8px);
      border-radius: 20px;
    }
    .webinar, .online {
      padding: spacer(2px 8px);
      border-radius: 20px;
    }

    .stationary, .live-online, .online {
      max-height: 20px;
      display: inline-block;
    }
  }
  &-header-title h3 {
    font-size: 20.8px;
    text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
    text-transform: none;
    color: #fff;
    font-weight: 600;
    margin: 2rem 0 1.2rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  &-content {
    text-transform: none;
    p {
      font-size: 15px;
    }
    ul {
      padding-left: spacer(3);
      li {
        font-size: 15px;
        text-transform: none;
        font-weight: normal;
        text-align: left;
        list-style-image: url("../images/li_checkmark.png");
        line-height: 20.8px;
      }
    }
  }
  &-more {
    margin: spacer(auto 3 2 0);
  }
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: spacer(6 0);
  .tab-pane {
    max-width: $max-width-container;
    padding-left: spacer(6);
    padding-right: spacer(6);
    margin: 0 auto;
  }
}
.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}
