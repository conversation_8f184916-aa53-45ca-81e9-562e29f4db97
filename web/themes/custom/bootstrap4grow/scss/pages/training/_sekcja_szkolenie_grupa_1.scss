#szkolenie-grupa-1 {
  @include page-bg-new;
  background-position: center;
  background-size: cover;
  .field__label {
    display: none;
  }
  a.npx-form-button-inline.npx-autolink {
    padding: 0;
    border: 0;
    background: transparent;
    color: $color-tertiary;
    font-weight: bold;
    text-transform: none;
    margin-top: 12px;
    display: inline-block;
    font-size: inherit;
  }
  .field--name-field-ts-opis {
    font-size: 18px;
    line-height: 24px;
    margin: 0;
    position: relative;
    z-index: 9;
    padding-top: 0.5rem;
    p {
      font-size: 18px;
      line-height: 24px;
    }
  }
  .inner {
    max-width: $max-width-container;
    padding-left: spacer(6);
    padding-right: spacer(6);
    margin: 0 auto;
  }
  h2 {
    margin-top: 0;
  }
  h1 {
    line-height: 50px;
    font-weight: bold;
    font-size: 40px;
    margin-top: 44px;
    z-index: 9;
    position: relative;
  }
  a, span.h1, h1, h2, h3, h4, h5, p, li {
    color: #fff;
  }
  a.npx-program-button {
    color: #191919;
  }
  ul, ol {
    list-style-image: url("/themes/custom/bootstrap4grow/images/check-white.png");
  }
  .group-right {
    .obraz img {
      padding-left: 10px;
    }
  }
  .obraz {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      color: transparent;
      min-height: 220px;
    }
  }
  &.full-width-image {
    h1 {
      margin-top: 5.75rem;
      text-shadow: -1px -1px 14px #000,1px -1px 14px #000,-1px 1px 14px #000,1px 1px 14px #000;
      @include media-breakpoint-down(lg) {
        margin-top: 3rem;
      }
    }
  }
  &.half-width-image {
    @include media-breakpoint-up(xl) {
      .inner {
        max-width: 100%;
        padding-right: 0;
        padding-left: 1.875rem;
      }
    }
    @include media-breakpoint-up(topimagehalf,(topimagehalf:1400px)) {
      .inner {
        padding-left: calc( 50vw - 707.5px + 1.875rem );
      }
    }
    h1 {
      margin-top: 2rem;
    }
    span.h1 {
      margin-top: 3rem;
      text-shadow: -1px -1px 14px #000,1px -1px 14px #000,-1px 1px 14px #000,1px 1px 14px #000;
      font-size: 2rem;
      line-height: 2.25rem;
      display: block;
      font-weight: 700;
    }
  }
  .inner-absolute {
    bottom: 10px;
    transform: translateX(-50%);
    left: 50%;
    width: 100%;
    z-index: 3;
    margin-top: 3rem;
    text-shadow: -1px -1px 14px #000,1px -1px 14px #000,-1px 1px 14px #000,1px 1px 14px #000;
    font-size: 1.5rem;
    line-height: 2rem;
    display: block;
    font-weight: 700;
    color: #fff;
    @include media-breakpoint-up(md) {
      font-size: 2.5rem;
      line-height: 3.5rem;
    }
  }
}
#szkolenie-grupa-1 {
  &.half-width-image,
  &.top-title-right-image {
    .obraz--gradient-mobile {
      display: block;
      @include media-breakpoint-up(xl) {
        display: none;
      }
      position: relative;

      img {
        position: relative;
        z-index: 1;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: linear-gradient(76.43deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
        z-index: 2;
        pointer-events: none;
      }

      .inner-absolute {
        z-index: 3;
      }
    }
  }
}
.paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
.paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
  @include media-breakpoint-down(lg) {
    flex: 0 0 100%;
    max-width: 100%;
  }
  @include media-breakpoint-up(xl) {
    padding-right: 1.5rem;
  }
}
.node--type-landing-page {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left,
  .paragraph--type-tytul-szkolenia-iii.ds-2col-fluid > .group-left,
  .paragraph--type-tytul-szkolenia-video.ds-2col-fluid > .group-left {
    padding-bottom: 2rem!important;
  }
  #szkolenie-grupa-1.half-width-image h1 {
    @include media-breakpoint-up(xl) {
      margin-top: 5rem;
    }
  }
}

.paragraph--type-tytul-szkolenia-video {
  &.ds-2col-fluid {
    @include media-breakpoint-up(xl) {
      display: flex;
      align-items: stretch;
    }

    .field--name-field-ts-video-embed,
    .video-embed-field-lazy,
    .video-embed-field-lazy img {
      height: 100%;
    }
  }
}

#szkolenie-grupa-1 {
  .video-mobile {
    position: relative;
    padding-top: 56.25%;
    width: 100%;

    .video-embed {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      iframe,
      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.video-embed-field-responsive-video {
  position: static !important;
  width: 100%;
}

.video-embed-field-lazy-play {
  border: none;
}
