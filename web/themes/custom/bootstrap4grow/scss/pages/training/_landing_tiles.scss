.npx-training-form-tab-content,
.npx-tile-textbox-content,
.tile-opinion-content {
  position: relative;
  overflow: hidden;

  span, p {
    font-size: $font-size-content;
    color: $color-text;
    line-height: 1.5;
    font-weight: $font-weight-normal;
  }
}

.npx-training-form-tab-content {
  &.has-overflow::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4.5rem;
    background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.8) 60%, white 100%);
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
}

.npx-blocks-program-tab-wrapper.expanded .npx-training-form-tab-content.has-overflow::after,
.npx-tile-textbox-wrapper.expanded.has-overflow::after,
.npx-tile-opinion-wrapper.expanded.has-overflow::after {
  opacity: 0;
}

.npx-tile-header {
  h2.field-label-above::after {
    height: 6px;
    border-radius: 16px;
    width: 70px;
    background: $color-accent;
  }
}

.npx-tile-sections-container {
  .npx-tile-section {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 60px;
  }

  .tile-row {
    position: relative;
    display: flex;
    align-items: flex-start;

    @include media-breakpoint-up(md) {
      max-height: 300px;
    }

    @include media-breakpoint-down(md) {
      max-height: none;
      flex-direction: column;
    }

    .col-md-4 {
      position: relative;

      .nav-item {
        position: relative;
        z-index: 1;

        &:hover {
          z-index: 10;
        }

        .npx-blocks-program-tab-wrapper,
        .npx-tile-textbox-wrapper,
        .npx-tile-opinion-wrapper {
          &.expanded {
            @include media-breakpoint-up(md) {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              z-index: 10;
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
              width: 100% !important;
              max-width: none !important;
            }

            @include media-breakpoint-down(md) {
              position: relative !important;
              z-index: 1 !important;
              box-shadow: none !important;
              width: auto !important;
              max-width: 100% !important;
            }
          }
        }
      }
    }
  }

  .show-more-container {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;

    .show-more-btn {
      position: relative;
      z-index: 1;
    }
  }
}

.icon-calendar,
.icon-location,
.icon-price,
.icon-envelope {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar {
  background-image: url("../images/icons/calendar.svg");
}

.icon-location {
  background-image: url("../images/icons/location.svg");
}

.icon-price {
  background-image: url("../images/icons/price.svg");
}

.icon-envelope {
  width: 10px;
  height: 10px;
  background-image: url("../images/icons/envelope.svg");
  position: relative;
  top: 1px;
}

.icon-arrow-ne {
  width: 12px;
  height: 12px;
  background-image: url("../images/icons/arrow-ne.svg");
}

.icon-arrow-down {
  width: 13px;
  height: 8px;
  background-image: url("../images/icons/arrow-down.svg");
}

.icons-column {
  width: 16px;
  flex-shrink: 0;

  .icon-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 16px;
  }
}

.npx-blocks-program-tab-wrapper.expanded .show-more-link,
.npx-tile-textbox-wrapper.expanded .show-more-link,
.npx-tile-opinion-wrapper.expanded .show-more-link {
  display: none;
}

.show-more-link {
  position: absolute !important;
  bottom: 5px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: transparent !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: $color-blue !important;
  text-decoration: none !important;
  z-index: 20 !important;
  font-weight: 600 !important;

  &:hover {
    text-decoration: underline !important;
  }

  &::after {
    content: "" !important;
    display: inline-block !important;
    width: 8px !important;
    height: 8px !important;
    margin-left: 5px !important;
    background-image: url("../images/icons/arrow-down.svg") !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: contain !important;
    vertical-align: middle !important;
  }
}

.npx-tile-textbox-wrapper .show-more-link,
.npx-tile-opinion-wrapper .show-more-link {
  position: absolute !important;
  bottom: 5px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
}

.npx-no-autolink {
  text-decoration: none !important;

  &:hover {
    text-decoration: none !important;
  }

  &:focus {
    text-decoration: none !important;
  }

  &:visited {
    text-decoration: none !important;
  }
}

