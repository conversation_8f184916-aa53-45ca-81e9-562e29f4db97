.paragraph--type-tytul-szkolenia-iii {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(76.43deg, #154DB2 13.77%, #3A78E6 41.25%, rgba(66, 151, 255, 0) 94.27%);
    z-index: 3;
    pointer-events: none;
  }

  .field--name-field-ts-obraz {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .ds-2col-fluid > .group-left {
    position: relative;
    z-index: 4;

    @include media-breakpoint-up(lg) {
      flex: 0 0 50% !important;
      max-width: 50% !important;
    }
  }

  .field--name-field-ts-tytul,
  .field--name-field-ts-podtytul,
  .field--name-field-ts-opis {
    position: relative;
    z-index: 9;
  }

  &:not(:has(.field--name-field-ts-obraz img)) {
    .ds-2col-fluid > .group-left {
      @include media-breakpoint-up(lg) {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        text-align: center;
      }
    }
  }

  .ds-2col-fluid > .group-right {
    display: none;
  }

}
