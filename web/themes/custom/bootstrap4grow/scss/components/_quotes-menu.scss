.block-quotes-menu-block {
  margin-bottom: map-get($spacers, 4);

  .quotes-title {
    margin: 3rem 0 1.5rem 0;
    font-weight: $font-weight-bolder;
    font-size: $h1-font-size;
    line-height: 1;
  }

  .item-list {
    margin-top: map-get($spacers, 8);
    display: flex;
    justify-content: center;
    width: 100%;
    border-bottom: 1px solid $nav-tabs-border-color;
  }

  ul.list-group {
    flex-direction: row;
    width: 100%;
    justify-content: center;
    gap: 2rem;

    @include media-breakpoint-down(lg) {
      gap: 0.75rem;
    }

    @media (max-width: 950px) and (min-width: 769px) {
      gap: 0.25rem;
    }

    @include media-breakpoint-down(md) {
      flex-direction: column;
      gap: 0.5rem;
    }

    .list-group-item {
      border: none;
      position: relative;
      flex-shrink: 1;
      min-width: 0;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: -1rem;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 20px;
        background-color: #dee2e6;

        @include media-breakpoint-down(lg) {
          right: -0.375rem;
        }

        @media (max-width: 950px) and (min-width: 769px) {
          right: -0.125rem;
        }
      }

      @include media-breakpoint-down(md) {
        &::after {
          display: none;
        }
      }

      a {
        text-decoration: none;
        color: $gray-800;
        text-transform: uppercase;
        font-size: 14px;
        font-weight: $headings-font-weight;
        display: block;
        text-align: center;
        padding: 1rem 0;
        white-space: nowrap;

        @include media-breakpoint-down(xl) {
          font-size: 13px;
        }

        @include media-breakpoint-down(lg) {
          font-size: 12px;
          white-space: normal;
          line-height: 1.2;
          padding: 0.75rem 0.25rem;
        }

        @media (max-width: 950px) and (min-width: 769px) {
          font-size: 11px;
          padding: 0.5rem 0.125rem;

          .menu-icon, .menu-text {
            display: block;
            text-align: center;
          }

          .menu-icon img {
            margin-right: 0;
            margin-bottom: 2px;
          }
        }

        @include media-breakpoint-down(md) {
          padding: 0.5rem 0;
          font-size: 14px;

          .menu-icon, .menu-text {
            display: inline-block;
          }

          .menu-icon img {
            margin-right: 8px;
            margin-bottom: 0;
          }
        }

        .menu-icon {
          position: relative;
          bottom: 2px;

          img {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
            width: 16px;
            height: 16px;
          }
        }

        .menu-text {
          display: inline-block;
        }

        &:hover,
        &.active {
          color: $color-primary;

          .menu-icon img {
            filter: brightness(0) saturate(100%) invert(31%) sepia(92%) saturate(2108%) hue-rotate(205deg) brightness(89%) contrast(94%);
          }
        }
      }
    }
  }

  .quotes-info-text {
    margin: 1.5rem 0 2rem 0;
    font-weight: $font-weight-light;
    font-size: $font-size-base;
    line-height: 1.5;
    letter-spacing: 0;
    color: $gray-800;

    a {
      color: $gray-800;
      text-decoration: underline;
    }
  }
}
