.quotes-banner {
  @include full-width;
  height: 200px;
  display: flex;
  align-items: center;
  margin-top: map-get($spacers, 8);
  margin-bottom: map-get($spacers, 8);

  &.banner-blue {
    background-image: url('/themes/custom/bootstrap4grow/images/banners/blue-pattern.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  &.banner-yellow {
    background-image: url('/themes/custom/bootstrap4grow/images/banners/yellow-pattern.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  .banner-container {
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 10rem;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    @include media-breakpoint-up(xxl) {
      max-width: $max-width-container;
    }

    @include media-breakpoint-down(md) {
      flex-direction: column;
      text-align: center;
      gap: 2rem;
      padding: 0;
    }
  }

  .banner-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    @include media-breakpoint-down(md) {
      justify-content: center;
      text-align: center;
    }

    .banner-text-and-button {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 1.5rem;

      @include media-breakpoint-down(md) {
        align-items: center;
      }

      .banner-title {
        font-size: $h2-font-size;
        font-weight: $font-weight-bolder;
        color: $black;
        line-height: 1.1;
        margin: 0;
      }

      .banner-button-with-logo {
        display: flex;
        align-items: center;
        gap: 1rem;

        .btn-banner {
          @include npx-button-big;
          background-color: #79B5FF;
          color: #183881;
          margin: 0;
        }

        .banner-logo-img {
          height: 3rem;
          width: auto;
        }
      }
    }
  }

  &.banner-yellow .banner-content {
    .btn-banner {
      @include npx-button-big;
      background-color: $color-orange-button !important;
      color: $white !important;
      border: 1px solid $color-orange-button !important;
    }
  }
}
