#cookies-overlay.active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: black;
  opacity: 0.5;
  z-index: 55557;
}
.cookiesjsr--app .cookiesjsr-banner {
  z-index: 55558;
}
.cookiesjsr-banner--text,
.cookiesjsr-links.links--row li a {
  @include t3;
  color: #000;
}

.cookiesjsr-banner--text a {
  color: #2A7DE3 !important;
  text-decoration: none;
}
.cookiesjsr-links.links--row li a {
  font-weight: 600;
}
.cookiesjsr--app .cookiesjsr-banner {
  background: $white;
  box-shadow: -0px -4px 36px -5px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  .cookiesjsr-links.links--row li::before {
    content: "";
    background-color: #000;
    border-radius: 100%;
  }
  .cookiesjsr-links.links--row li:first-child::before {
    content: "";
  }
  @include media-breakpoint-down(md) {
    .cookiesjsr-links.links--row li::before,
    .cookiesjsr-links.links--row li:first-child::before {
      content: "";
      background-color: #000;
      border-radius: 100%;
      height: .3em;
      left: 0;
      position: absolute;
      top: 45%;
      width: .3em;
    }
  }
  button {
    @include t2b;
    background: var(--secondary);
    border: 0;
    border-radius: 5px;
    text-transform: none;
    &:hover {
      background-color: #034b7d;
    }
  }
  &.active {
    align-items: flex-start;
  }
}
.cookiesjsr--app .cookiesjsr-btn:hover {
  transform: scale(1);
}
.cookiesjsr-banner {

  .cookiesjsr-btn.important.denyAll,
  .cookiesjsr-btn.important.allowAll,
  .cookiesjsr-btn.cookiesjsr-settings {
    color: black;
  }

  .cookiesjsr-btn.important.allowAll {
    background-color: rgb(254, 204, 9);
    &:hover {
      background-color: #ffab1a;
    }
  }
  .cookiesjsr-btn.important.denyAll {
    background-color: transparent;
    border: 2px solid #CED4DA;
  }

  .cookiesjsr-btn.cookiesjsr-settings,
  .cookiesjsr-btn.cookiesjsr-settings:hover {
    background-color: transparent;
    border: none;
    text-decoration: underline;
  }
}
.cookiesjsr-banner .cookiesjsr-banner--info {
  margin-bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  @include media-breakpoint-up(md) {
    flex-direction: row;
  }

  .cookiesjsr-banner--links.links--row {
    flex-direction: column;
    align-items: flex-start;
  }

}
.cookiesjsr-links, .cookiesjsr-banner--links.links--row {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 0;
}
.cookiesjsr-banner--text {
  flex: 1;
}
.cookiesjsr--app .cookiesjsr-banner .cookiesjsr-banner--action {
  flex-direction: column-reverse !important;
  @include media-breakpoint-up(md) {
    flex-direction: row-reverse !important;
    min-width: 60%;
    width: auto;
  }
}
.cookiesjsr-layer {
  border-radius: 5px;
}
.cookiesjsr-layer--body {
  margin: 0 1rem;
  .cookiesjsr-service-group.active .cookiesjsr-service-group--tab, .cookiesjsr-service-group.active .cookiesjsr-service-group--content {
    background: #0056B3;
    @include t3;
  }
  .cookiesjsr-service-group.active {
    @include media-breakpoint-down(md) {
      .cookiesjsr-service-group--tab {
        display: none;
      }
      .cookiesjsr-service-group--content {
        border-radius: 4px;
      }
    }
  }
}
.cookiesjsr-layer {
  .cookiesjsr-layer--header, .cookiesjsr-layer--footer, .cookiesjsr-layer--body {
    background: $white;
    color: black;
    border: none;

    .cookiesjsr-btn {
      text-transform: none;
      @include t2b;
      width: 100%;
      color: black;
    }

    .cookiesjsr-btn.allowAll {
      display: none;
    }
  }
  .cookiesjsr-layer--footer {
    border: none !important;
    width: 100%;
    @include media-breakpoint-up(md) {
      padding-right: 0;
    }
    .cookiesjsr-layer--label-all {
      display: none;
    }
  .cookiesjsr-layer--actions {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .cookiesjsr-btn {
      margin: 0;
      width: 100%;
      order: 1;
      
      &.save {
        order: 1;
      }
      
      &.denyAll {
        order: 2;
      }
    }
  }
  }
}
.cookiesjsr-layer--actions {
  .cookiesjsr-btn:hover {
    transform: scale(1);
  }
  .cookiesjsr-btn.invert {
    background-color: rgb(0, 102, 204);
    border: none;
    color: #fff;
    &:hover {
      background-color: #034b7d;
    }
  }
  .cookiesjsr-btn.important.invert {
    background: #fecc09;
    border: none;
    color: black;
    &:hover {
      background-color: #ffab1a;
    }
  }
}
.cookiesjsr-btn {
  border-radius: 5px;
}
.cookiesjsr-service-group:not(.active) {
  .cookiesjsr-service-group--tab {
    background: $white;
    @include t3;
    color: black;
  }
}
.cookiesjsr-service-group {
  button {
    border: none !important;
  }
  &--intro,
  &--service {
    @include t3;
  }

  &--content {
    display: flex !important;
    flex-direction: column !important;

    .cookiesjsr-service-group--intro {
      order: 2 !important;
      padding-top: 0;
    }

    li.cookiesjsr-service:hover {
      background-color: #0056B3;
    }
  }
}

.cookiesjsr-service--always-on {
  span {
    display: none;
  }
  &::before {
    content: "";
    display: inline-block;
    width: 32px;
    height: 16px;
    background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.cookiesjsr-layer .cookiesjsr-switch {
  background-color: transparent !important;
  box-shadow: none !important;
  margin-top: 5px !important;
  margin-left: 15px !important;
  &::after {
    display: none !important;
  }
  &::before {
    content: "";
    display: inline-block;
    width: 32px;
    height: 16px;
    background-image: url("/themes/custom/bootstrap4grow/images/toggle-off.svg");
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.cookiesjsr-layer .cookiesjsr-switch.active {
  &::before {
    background-image: url("/themes/custom/bootstrap4grow/images/toggle-on.svg");
  }
}

.cookiesjsr-service-group {
  @include media-breakpoint-up(md) {
    &--tab,
    &--content {
      width: 50% !important;
    }
  }
}
