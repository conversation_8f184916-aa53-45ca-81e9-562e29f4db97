.n-breadcrumb {
  padding-left: 0.75rem;
  color: #000;
  a, a:hover, a:active, a:focus {
    color: #000;
  }
  li::before {
    content: ' \BB ';
    font-size: 19.2px;
    padding: spacer(0 1 0 4px);
  }
  li:first-child::before {
    content: none;
  }
  &.inner {
    max-width: $max-width-container;
    padding-left: spacer(6);
    padding-right: spacer(6);
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1rem;
  }
}
.node--type-landing-page .n-breadcrumb,
.node--type-npxtraining .n-breadcrumb {
  @include t3b;
  color: #183881;

  a,
  a:hover,
  a:active,
  a:focus {
    color: inherit;
    text-decoration: none;
  }

  li::before {
    content: '> ';
    font-size: 1.1em;
    width: 8px;
    padding: spacer(0 1 0 1);
    color: inherit;
  }

  li:first-child::before {
    content: none;
  }
}
#main-wrapper {
  border-top: 1px solid #ebedec;
}
