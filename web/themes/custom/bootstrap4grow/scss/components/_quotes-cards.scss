.view-npx-quotes .views-field-rendered-entity,
.view-quotes-per-categories .view-content.row {
  margin-left: 0;

  .views-row {
    margin: 0.75rem 0;
    line-height: 1.4;
    padding: 1rem 0.5rem;
    font-size: $font-size-base;
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-rows: auto auto;
    gap: 0 0.5rem;
    background-color: $grey-100-new;
    border-left: 4px solid $color-border-yellow;
    border-top-left-radius: $border-radius;
    border-bottom-left-radius: $border-radius;
  }

  .views-field {
    margin: 0;
  }

  .views-field-body {
    grid-column: 2;
    grid-row: 1;

    .field-content {
      font-size: $font-size-md-new;
      font-weight: $font-weight-bolder;
      line-height: 1.5;
    }
  }

  .views-field-field-quote-author {
    grid-column: 2;
    grid-row: 2;
    margin-top: 5px;
    text-align: left;

    .field-content {
      font-size: $font-size-md-new;
      line-height: 1.5;
      font-weight: $font-weight-light;
    }
  }
}

.quotes-category-title {
  font-size: $h2-font-size;
  font-weight: $font-weight-bolder;
  line-height: 1.1;
}

.quotes-category-section {
  .view-content {
    position: relative;
  }

  &:has(.more-quotes-button-wrapper) .view-content .views-row:last-child,
  &.has-more-button .view-content .views-row:last-child {
    position: relative;

    .views-field {
      filter: blur(0.3px);
      opacity: 0.8;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 30%,
        rgba(255, 255, 255, 0.6) 70%,
        rgba(255, 255, 255, 0.8) 100%
      );
      pointer-events: none;
      z-index: 1;
    }
  }

  &.loading-ajax {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      z-index: 10;
      pointer-events: none;
    }
  }
}

.view-npx-quotes, .view-quotes-per-categories {

  .btn-trainings.btn-quotes {
    @include npx-button-big-primary;
    margin-right: map-get($spacers, 4);
    margin-bottom: map-get($spacers, 4);
    position: relative;
    padding-right: map-get($spacers, 8);

    &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background-image: url('/themes/custom/bootstrap4grow/images/icons/arrow-right.svg');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
    }

    &.disabled,
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .btn-download.btn-quotes,
  .btn-more.btn-quotes {
    @include npx-button-big-secondary;
    margin-bottom: map-get($spacers, 4);

    &.disabled,
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .download-all-quotes-button {
    margin-top: map-get($spacers, 4);
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;

    @include media-breakpoint-down(sm) {
      flex-direction: column;

      .btn-quotes {
        margin-right: 0;
      }
    }
  }

  .vocabulary-quotes-categories > h2,
  .vocabulary-quotes-categories .field--name-name {
    display: none;
  }
}

.more-quotes-button-wrapper {
  text-align: center;
  margin-top: map-get($spacers, 4);

  .btn.btn-quotes.load-more-quotes {
    @include npx-button-big-tertiary;
    border-width: 2px;
    margin: 0;
    position: relative;
    padding-right: 3rem;

    &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background-image: url('/themes/custom/bootstrap4grow/images/icons/arrow-down.svg');
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      transition: all 0.3s ease;
    }

    &:hover::after {
      background-image: url('/themes/custom/bootstrap4grow/images/icons/arrow-down-hover.svg');
    }

    &.disabled,
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;

      &::after {
        animation: spin 1s linear infinite;
      }
    }
  }
}

@keyframes spin {
  from {
    transform: translateY(-50%) rotate(0deg);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

.webform-submission-quotes-download-form-form .js-form-type-checkbox label {
  .npx-popover-trigger {
    text-decoration: underline;
    cursor: pointer;
  }

  &:after {
    content: '';
    vertical-align: super;
    display: inline-block;
    background-image: url('/themes/custom/bootstrap4grow/images/required.svg');
    background-repeat: no-repeat;
    background-size: 7px 7px;
    width: 7px;
    height: 7px;
  }
}
