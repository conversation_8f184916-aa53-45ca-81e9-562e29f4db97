@mixin npx-button {
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  text-align: center;
  text-transform: uppercase;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  color: #191919;
  line-height: 24px;
  padding: 12px 30px;
  width: auto;
  border: none;
  margin: 10px auto 10px 0;
  -moz-transition: all .5s;
  -o-transition: all .5s;
  -webkit-transition: all .5s;
  transition: all .5s;
  background: $color-tertiary;
  display: inline-block;
  max-width: 100%;
  &:hover {
    text-decoration: none;
    background-color: #ffab1a;
    color: #191919;
  }
}
@mixin npx-button-dark {
  display: inline-block;
  padding: 12px 30px;
  margin: 48px auto 16px;
  margin-top: 48px;
  font-weight: bold;
  font-size: 14px;
  height: 50px;
  line-height: 24px;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -moz-box-shadow: 0 0 0 transparent;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -moz-transition: all 500ms;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  white-space: normal;
  &:hover {
    background-color: #034b7d;
    text-decoration: none;
    color: #fff;
  }
}

@mixin page-bg {
  background: #0056B3;
}
@mixin page-bg-new {
  background: #154DB2;
}
@mixin page-bg-cover {
  background: #fff;
}
@mixin limiter {
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  padding: 0 20px;
  @include media-breakpoint-up(sm) {
    max-width: 450px;
  }
  @include media-breakpoint-up(md) {
    max-width: 720px;
  }
  @include media-breakpoint-up(lg) {
    max-width: 960px;
  }
  @include media-breakpoint-up(xl) {
    max-width: 1200px;
  }
  @include media-breakpoint-up(ld) {
    max-width: 1350px;
  }
  @include media-breakpoint-up(xxl) {
    max-width: $max-width-container;
  }
}
@mixin h1other {
  border-bottom: 1px solid #aeaeb0;
  font-weight: 800;
  padding: 20px 0;
  margin: 5px 0 16px;
  text-align: left;
  color: #000;
}
@mixin h2other {
  line-height: 100%;
  padding: 0 0 20px;
  margin: 0 0 12.8px;
  border-bottom: 1px solid #aeaeb0;
  text-align: center;
  @include media-breakpoint-up(md) {
    text-align: left;
  }
}
@mixin full-width {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

@mixin npx-button-big {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: $headings-font-weight;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  padding: 8px 16px;
  height: 3rem;
  border-radius: 4px;
  border: none;
  text-decoration: none;
  text-transform: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-sizing: border-box;
  cursor: pointer;
}

@mixin npx-button-big-primary {
  @include npx-button-big;
  background-color: $color-primary;
  color: $white;

  &:hover {
    background-color: $color-blue-darker;
    color: $white;
    text-decoration: none;
  }
}

@mixin npx-button-big-secondary {
  @include npx-button-big;
  background-color: #E3F0FF;
  color: $color-blue-darker;

  &:hover {
    background-color: #CEE5FF;
    color: $color-blue-darker;
    text-decoration: none;
  }
}

@mixin npx-button-big-tertiary {
  @include npx-button-big;
  background-color: transparent;
  color: $color-primary;
  border: 2px solid $color-primary;

  &:hover {
    background-color: transparent;
    color: $color-blue-darker;
    border-color: $color-blue-darker;
    text-decoration: none;
  }
}

@mixin npx-button-big-orange {
  @include npx-button-big;
  background-color: $color-orange-button;
  color: $white;
  border: 1px solid $color-orange-button;

  &:hover {
    background-color: $color-orange-button-hover;
    color: $white;
    border-color: $color-orange-button-hover;
    text-decoration: none;
  }
}

@mixin t3b {
  font-size: 14px;
  color: #183881;
  line-height: 1.2;
  font-weight: 700;
}

@mixin t3 {
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

@mixin t2b {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
}
