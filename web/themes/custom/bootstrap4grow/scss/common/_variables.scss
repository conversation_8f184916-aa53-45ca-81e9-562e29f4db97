$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  lgm: 1050px,
  lga: 1101px,
  xl: 1200px,
  xla: 1300px,
  ld: 1400px,
  xxl: 1600px
) !default;

$container-max-widths: (
  sm: 575px,
  md: 767px,
  lg: 991px,
  lgm: 1049px,
  lga: 1100px,
  xl: 1199px,
  xla: 1299px,
  ld: 1399px,
  xxl: 1599px
) !default;

$spacer: 1rem;

$spacers: (
  0: 0,
  1: ($spacer * .5), //8px
  2: ($spacer * .625), //10px
  3: ($spacer * 0.9375), //15px
  4: $spacer, //16px
  5: ($spacer * 1.25),  //20px
  6: ($spacer * 1.875), //30px
  7: ($spacer * 2.5), //40px
  8: ($spacer * 3)
);

/* VARIABLES */

// colors
$primary-shade: rgb(255, 78, 46);
$primary-light: lighten($primary-shade, 37%);
$primary-dark: darken($primary-shade, 12%);
$accent-shade: #0079C0;
$accent-light: lighten($accent-shade, 37%);
$accent-dark: darken($accent-shade, 12%);
// Reassign color vars to semantic color scheme
$red: #dc3545;
$yellow: #ffc107;
$green: #28a745;
$cyan: #17a2b8;
$white: #fff;
$gray-100: #f8f9fa;
$gray-800: #343a40;
$black: #000000;
$theme-colors: ( primary: $white, secondary: #0066cc, success: $green, info: $cyan, warning: $yellow, danger: $red, light: $gray-100, dark: $gray-800);

$brand-primary: $accent-shade;
//$brand-success: $green;
//$brand-info: $teal;
//$brand-warning: $orange;
//$brand-danger: $red;
$brand-inverse: $primary-shade;
// Body
//
// Settings for the `<body>` element.
$body-bg: $white;
$body-color: $gray-800;
$inverse-bg: $primary-shade;
$inverse-color: $white;
// Links
//
// Style anchor elements.
$link-decoration: none;
$link-hover-color: $accent-dark;
$link-hover-decoration: underline;
// Comments
$comment-spacer-x: 1.25em;
$comment-spacer-y: 1.25em;
// Responsive font sizes
$enable-responsive-font-sizes: true;
$enable-shadows: false;
$enable-gradients: false;
// Breadcrumbs (Works only on Bootstrap 5+)
// $breadcrumb-divider: quote(">");

$font-family: "Muli", sans-serif;
$roboto: $font-family;


$list-group-active-bg: transparent;
$list-group-bg: #fff;
$table-cell-padding: 8px;
$base-font-size: 16px;
$p-font-size: 1rem;
$font-weight-bolder: 700;

$border-separator: 1px solid #d0d8db;
$border-separator-light: 1px solid #e3e9e9;


$link-color: #0053B3;
$pagination-active-bg: #06c;
$pagination-color: #06c;
$color-tertiary: #fecc09; //buttons
$npxSpoilerColorGray: #034b7d;

$npxFreshmailSubmitBackground: #e454ff;
$npxFreshmailSubmitHoverBackground: #c434df;
$npxFreshmailSubmitColor: #fff;
$npxFreshmailMsgColor: #909090;

$color-primary: #2A7DE3;
$color-blue-darker: #0056B3;
$color-orange-button: #FF9900;
$color-orange-button-hover: #FFB546;
$color-border-yellow: #F2CA10;
$grey-100-new: #F5F5F5;


/* TYPOGRAPHY */

/* Google Fonts */

//@import url("https://fonts.googleapis.com/css?family=Lato:400,300,300italic,400italic,700,700italic,900,900italic");
//@import url("https://fonts.googleapis.com/css?family=Raleway:400,300,500,600,700,800,900");
// font families
$font-lato: 'Lato',
Arial,
Verdana,
sans-serif;
$font-raleway: 'Raleway',
Arial,
Verdana,
sans-serif;
// Fonts
//
// Font, line-height, and color for body text, headings, and more.
$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
$font-family-serif: Georgia,"Times New Roman",Times,serif;
$font-family-base: $font-family;
$font-size-base: 1rem; // Assumes the browser default, typically `16px`
$font-size-lg: $font-size-base * 1.25;
$font-size-sm: $font-size-base * .875;
$font-size-xs: $font-size-base * .75;
$font-weight-normal: normal;
$font-weight-bold: bold;
$font-weight-base: $font-weight-normal;
$line-height-base: 1.5;
$h1-font-size: $font-size-base * 2.5;
$h2-font-size: $font-size-base * 2;
$h3-font-size: $font-size-base * 1.5;
$h4-font-size: $font-size-base * 1.2;

$headings-font-weight: 600;
$font-weight-light: 400;
$font-size-md-new: $font-size-base * 1.125;



$max-width-container: 1415px;

:root {--secondary: #0066cc;}


// Necessary BS variables
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-700: #495057 !default;
$component-active-color: #fff !default;
$component-active-bg: #0d6efd !default;
$nav-link-padding-y: .5rem !default;
$nav-link-padding-x: 1rem !default;
$nav-link-font-size: null !default;
$nav-link-font-weight: null !default;
$nav-link-color: $link-color !default;
$nav-link-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;
$enable-transitions: true !default;
$enable-reduced-motion: true !default;
$nav-link-hover-color: $link-hover-color !default;
$nav-link-disabled-color: #6c757d !default;
$nav-tabs-border-width: 1px !default;
$nav-tabs-border-color: $gray-300 !default;
$border-radius: .25rem !default;
$nav-tabs-border-radius: $border-radius !default;
$enable-rounded: true !default;
$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;
$nav-tabs-link-active-color: $gray-700 !default;
$nav-tabs-link-active-bg: $body-bg !default;
$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;
$nav-pills-border-radius: $border-radius !default;
$nav-pills-link-active-color: $component-active-color !default;
$nav-pills-link-active-bg: $component-active-bg !default;

// Additional variables for tiles and components
$color-primary: #183881;
$color-border-light: #F4F9FF;
$color-accent: #f2ca10;
$color-accent-alt: #ffd817;
$color-link: #0056B3;
$color-text: #343A40;
$color-blue: #2A7DE3;

$font-size-title: 20px;
$font-size-content: 14px;
$font-size-small: 12px;

$font-weight-semibold: 600;

