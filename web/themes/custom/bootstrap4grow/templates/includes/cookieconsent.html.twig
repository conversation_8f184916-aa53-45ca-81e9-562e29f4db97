{# templates/includes/cookieconsent.html.twig #}
<!-- Google Tag Manager -->
<script>
  if (window.location.hostname === '4grow.pl') {


    // Define dataLayer and the gtag function.
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}

    // Set default consent to 'denied' as a placeholder
    // Determine actual values based on your own requirements
    gtag('consent', 'default', {
      'ad_storage': 'denied',
      'ad_user_data': 'denied',
      'ad_personalization': 'denied',
      'analytics_storage': 'denied',
      'functionality_storage': 'denied',
      'personalization_storage': 'denied',
      'security_storage': 'granted'
    });

    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-NG9HJ9H');
  }
</script>
<!-- End Google Tag Manager -->

<!-- Cookies Cancel Button -->
<script>
document.addEventListener("DOMContentLoaded", function() {
  function addCancelButton() {
    const actionsContainer = document.querySelector(".cookiesjsr-layer--actions");
    if (actionsContainer && !actionsContainer.querySelector(".cookiesjsr-btn.cancel")) {
      const cancelButton = document.createElement("button");
      cancelButton.type = "button";
      cancelButton.className = "cookiesjsr-btn cancel";
      cancelButton.textContent = "ANULUJ";
      cancelButton.addEventListener("click", function() {
        const closeButton = document.querySelector(".cookiesjsr-layer--close");
        if (closeButton) {
          closeButton.click();
        }
      });
      actionsContainer.appendChild(cancelButton);
    }
  }
  
  addCancelButton();
  setTimeout(addCancelButton, 500);
  setTimeout(addCancelButton, 1000);
  setTimeout(addCancelButton, 2000);
});
</script>
