{#
/**
 * @file
 * Default theme implementation for a fieldgroup html element.
 *
 * Available variables:
 * - title: Title of the group.
 * - title_element: Element to wrap the title.
 * - children: The children of the group.
 * - wrapper_element: The html element to use
 * - attributes: A list of HTML attributes for the group wrapper.
 *
 * @see template_preprocess_field_group_html_element()
 *
 * @ingroup themeable
 */
#}
{% if top_par_type == 'top-title-fullwidth-image' %}
  <{{ wrapper_element }} {{ attributes.removeClass('b-lazy', 'is-b-visible').addClass('full-width-image') }}>
    <div class="obraz position-relative">
      <img src="{{ image_url }}" alt="{{ image_alt }}" title="{{ image_title }}"/>
      <div class="inner inner-absolute position-absolute">{{ top_title_h1 }}</div>
    </div>
    <div class="top-full-width-img-bg">
      <div class="inner">
        {{ top_description }}
      </div>
    </div>
  </{{ wrapper_element }}>
{% elseif top_par_type == 'top-title-right-image' %}
  <{{ wrapper_element }} {{ attributes.addClass('half-width-image') }}>
    <div class="d-none d-xl-block">
    {% if title %}
      <{{ title_element }}{{ title_attributes }}>{{ title }}</{{ title_element }}>
    {% endif %}
    {% if collapsible %}
      <div class="field-group-wrapper">
    {% endif %}
    {{children}}
    {% if collapsible %}
      </div>
    {% endif %}
    </div>
    {% if top_paragraph_type == 'tytul_szkolenia_video' %}
      <div class="video-mobile d-xl-none">
        <div class="video-embed">{{ video_embed }}</div>
      </div>
    {% else %}
      {% set mobile_classes = ['obraz', 'd-xl-none', 'position-relative'] %}
      {% if top_paragraph_type == 'tytul_szkolenia_iii' %}
        {% set mobile_classes = mobile_classes|merge(['obraz--gradient-mobile']) %}
      {% endif %}
      <div class="{{ mobile_classes|join(' ') }}">
        <img src="{{ image_url }}" alt="{{ image_alt }}" title="{{ image_title }}"/>
        <div class="inner inner-absolute position-absolute"><span class="h1">{{ top_title }}</span></div>
      </div>
      <div class="top-full-width-img-bg d-xl-none">
        <div class="inner">
          {{ top_description }}
        </div>
      </div>
    {% endif %}
    {% if top_paragraph_type == 'tytul_szkolenia_video' %}
      <div class="top-full-width-img-bg d-xl-none">
        <div class="inner">
          <h1>{{ top_title }}</h1>
          {{ top_description }}
        </div>
      </div>
    {% endif %}
  </{{ wrapper_element }}>

{% else %}
  <{{ wrapper_element }} {{ attributes }}>
    {% if title %}
      <{{ title_element }}{{ title_attributes }}>{{ title }}</{{ title_element }}>
    {% endif %}
    {% if collapsible %}
      <div class="field-group-wrapper">
    {% endif %}
    {{children}}
    {% if collapsible %}
      </div>
    {% endif %}
  </{{ wrapper_element }}>
{% endif %}
