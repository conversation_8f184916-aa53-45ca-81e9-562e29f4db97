(function ($, Drupal, drupalSettings) {
  "use strict";

  var animateScroll = function (object, offset) {
    return new Promise((resolve, reject) => {
      $("html, body").animate(
        { scrollTop: $(object).offset().top - offset },
        {
          duration: 600,
          step: function (now, tween) {
            var newOffset = $(object).offset().top - offset;
            if (tween.end !== newOffset) {
              tween.end = newOffset;
            }
          },
          complete: function () {
            resolve();
          },
        }
      );
    });
  };

  Drupal.behaviors.npxMenuScrollTop = {
    attach: function (context) {
      // Nothing to do
    },
    lastOffset: 0,
    scrolled: 0,
    npxOnScrollResize: function () {
      var $body = $("body");
      var $header = $("#page .headerwrapper.grow3");
      var viewportHeight = $(window).height();
      var offset = $(window).scrollTop();
      var npxLastOffset = Drupal.behaviors.npxMenuScrollTop.lastOffset;
      var npxScrolled = Drupal.behaviors.npxMenuScrollTop.scrolled;

      if (offset < npxLastOffset) {
        npxScrolled += npxLastOffset - offset;
        if (npxScrolled > 1000 && offset > viewportHeight) {
          if (
            !$body.hasClass("main-menu-block-visible") &&
            $header.css("position") != "static"
          ) {
            $header.fadeOut(0).fadeIn(500);
            $body.addClass("main-menu-block-visible");
          }
        }
      } else if (offset > npxLastOffset) {
        $body.removeClass("main-menu-block-visible");
        Drupal.behaviors.npxMenuScrollTop.scrolled = 0;
      }

      if (offset < viewportHeight) {
        $body.removeClass("main-menu-block-visible");
      }

      Drupal.behaviors.npxMenuScrollTop.lastOffset = offset;
    },
  };

  Drupal.behaviors.npxFindAnchors = {
    attach: function (context) {
      const elements = once(
        "npx-autolink-scroll",
        'a[href^="#"]:not(a[href$="#"]):not(a[data-toggle="tab"])',
        context
      );
      elements.forEach(function (element) {
        var $anchor = $(element);
        if ($($anchor.attr("href")).length) {
          var text = $anchor.html();
          if (!$anchor.hasClass("npx-no-autolink")) {
            $anchor.addClass("npx-autolink");
            $anchor.click(function (e) {
              e.preventDefault();
              var href = $anchor.attr("href");
              animateScroll(href, 150).then(() => {
                animateScroll(href, 150);
              });
            });
          }
        }
      });
    },
  };

  Drupal.behaviors.npxSpoiler = {
    attach: function (context) {
      const elements = once("npx-spoiler", "div.npx-spoiler", context);
      elements.forEach(function (element) {
        var $spoiler = $(element);
        $spoiler
          .find(".npx-spoiler-toggle")
          .removeClass("hide-icon")
          .addClass("show-icon");
        $spoiler.find(".npx-spoiler-content").hide();
      });
    },
  };

  Drupal.behaviors.npxChatFix = {
    attach: function (context) {
      const elements = once("npx-chat-fix", "body", context);
      elements.forEach(function (element) {
        var $body = $(element);
        var interval = setInterval(function () {
          var $btn = $body.find(".mylivechat_buttonround_tooltip");
          if ($btn.length > 0) {
            var arrow =
              '<div class="mylivechat_buttonround_tooltip_arrow" style="resize: none; font-size: 13px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; position: absolute; left: 100%; top: 9px; width: 12px; height: 0px; border-color: transparent rgb(254, 204, 9); border-width: 10px 0px 10px 12px; border-style: solid; box-sizing: content-box;"></div>';
            var txt = $btn.text().replace("? ", "? </span><span>");
            $btn.html("<span>" + txt + "</span>" + arrow);
            clearInterval(interval);
          }
        }, 250);
      });
    },
  };
  Drupal.behaviors.npxSuperfishFix = {
    attach: function (context) {
      var interval = setInterval(function () {
        const elements = once(
          "npx-superfish-fix-attach",
          "#superfish-grow3menu",
          context
        );
        if (elements.length) {
          elements.forEach(function (element) {
            var $menu = $(element);
            Drupal.behaviors.npxSuperfishFix.attachSearchWidget($menu);
            Drupal.behaviors.npxSuperfishFix.positionElements($menu);
          });
          clearInterval(interval);
        }
      }, 250);
    },
    npxOnResize: function () {
      var $menu = $("#superfish-grow3menu");
      Drupal.behaviors.npxSuperfishFix.positionElements($menu);
    },
    attachSearchWidget: function (context) {
      const menus = once(
        "n-has-menu-search-widget",
        "li.sf-depth-1.menuparent:first-child > ul",
        document
      );
      menus.forEach(function (menuElement) {
        var $menu = $(menuElement);
        var $widget = $.parseHTML(
          "<li class='n-menu-search-wrapper-li'><div id='n-menu-search-wrapper'>" +
          "<input type='text' id='n-menu-search-input' placeholder='Znajdź szkolenie' />" +
          "" +
          "<div class='n-menu-search-results-wrapper'><ul class='n-menu-search-results'></ul></div>" +
          "</div></li>"
        );
        $menu.append($widget);

        var isSearchTyping = false;
        const inputs = once(
          "n-has-menu-search-widget-action",
          "#n-menu-search-input",
          document
        );
        inputs.forEach(function (inputElement) {
          var $input = $(inputElement);
          $input.keyup(function () {
            if (!isSearchTyping) {
              isSearchTyping = true;

              setTimeout(function () {
                Drupal.behaviors.npxSuperfishFix.searchAction($menu);
                isSearchTyping = false;
              }, 500);
            }
          });
        });
      });
    },
    searchAction: function ($menu) {
      var phrase = Drupal.checkPlain($menu.find("#n-menu-search-input").val());
      var $results = $menu.find(".n-menu-search-results");
      $results.html("");
      if (phrase == "" || phrase.length < 3) {
        return;
      }
      var $links = $menu.find(
        "li.sf-depth-2.menuparent li:icontains('" + phrase + "')"
      );

      $links.each(function () {
        $(this).clone().removeAttr("id").appendTo($results);
      });
    },
    positionElements: function ($menu) {
      if ($menu.length > 0) {
        $menu
          .find(".n-online-info")
          .closest("li")
          .addClass("n-online-info-wrapper")
          .addClass("menuparent");

        var position = $menu.find("li.sf-depth-2.menuparent").first().offset();
        var columnLen = 450;

        $menu.find("li.sf-depth-2 > ul").css("left", position.left + columnLen);
        $menu.find("#n-menu-search-input").css("left", position.left);
        $menu
          .find("div.n-menu-search-results-wrapper")
          .css("left", position.left + columnLen);
        if ($(window).innerWidth() > 1330) {
          $menu
            .find(".n-menu-rcol")
            .closest("li")
            .css("left", position.left + 1000)
            .addClass("n-menu-rcol");
          $menu.find(".kalendarz").closest("li").css({ top: 0, "z-index": 999 });
          $menu.find(".moneybox").closest("li").css({ top: 100, "z-index": 999 });
          $menu.find(".menu-link-promocje").closest("li").css({ top: 187, "z-index": 999 });
        } else {
          $menu
            .find(".n-menu-rcol")
            .closest("li")
            .css("left", position.left)
            .removeClass("n-menu-rcol");
          var $sli = $menu.find(".n-menu-search-wrapper-li");
          var $cli = $menu.find(".kalendarz").closest("li");
          var $mli = $menu.find(".moneybox").closest("li");
          var $ul = $sli.closest("ul");
          $mli.detach();
          $cli.detach();
          $ul.append($mli);
          $ul.append($cli);
        }
      }
    },
  };
  Drupal.behaviors.npxGlobalSuperfishPositionWhenStickyinfo = {
    attach: function (context) {
      // Check if the sticky info block is present
      if ($('.region-admin-tabs .npx-sticky-info-block').length) {
        $("ul#superfish-grow3menu > li > ul").css(
          "top",
          $(".block-superfishgrow3menu").height() + $(".npx-sticky-info-block").height() + "px"
        );
      }
    },
  };
})(jQuery, Drupal, drupalSettings);
