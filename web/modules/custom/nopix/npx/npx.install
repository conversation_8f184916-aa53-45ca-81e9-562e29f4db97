<?php

use <PERSON><PERSON>al\field\Entity\FieldConfig;
use <PERSON><PERSON>al\field\Entity\FieldStorageConfig;
use Drupal\Core\Config\FileStorage;
use Dr<PERSON>al\node\Entity\Node;
use Drupal\taxonomy\Entity\Term;
use Dr<PERSON>al\taxonomy\Entity\Vocabulary;
use Drupal\menu_link_content\Entity\MenuLinkContent;
use Drupal\Core\Language\LanguageInterface;

/**
 * Implements hook_update_N() on Module npx Update # 8001.
 * Install field_npxtraining_date_form
 */
function npx_update_8001(&$sandbox)
{
  $config_path = \Drupal::service('extension.path.resolver')->getPath('module', 'npx') . '/config/install';
  $storage = new FileStorage($config_path);

  $data = $storage->read('field.storage.node.field_npxtraining_date_form');
  if (!FieldStorageConfig::loadByName($data['entity_type'], $data['field_name'])) {
    FieldStorageConfig::create($data)->save();
  }

  $data = $storage->read('field.field.node.npxtraining_date.field_npxtraining_date_form');
  if (!FieldConfig::loadByName($data['entity_type'], $data['bundle'], $data['field_name'])) {
    FieldConfig::create($data)->save();
  }
}

/**
 * Adds predefined category terms and their associated links if the 'quotes_categories' vocabulary exists.
 */
function npx_update_10101(&$sandbox)
{
  $messenger = \Drupal::messenger();
  $term_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');

  $vocabulary_machine_name = 'quotes_categories';
  $default_langcode = \Drupal::languageManager()->isMultilingual() ? \Drupal::languageManager()->getDefaultLanguage()->getId() : 'pl';

  $categories_to_add = [
    'Miłość, zakochanie, związki' => 'https://anioly-rozwoju.pl/',
    'Praca i kariera' => 'https://4grow.pl/szkolenia/zarzadzanie-stresem-szkolenie-zarzadzanie-emocjami-szkolenie',
    'Zarządzanie i szef' => 'https://4grow.pl/szkolenia/akademia-menedzera-2',
    'Praca zespołowa' => 'https://4grow.pl/szkolenia-z-kompetencji-miekkich',
    'Rozwój osobisty' => 'https://4grow.pl/szkolenia/komunikacja-szkolenie-z-komunikacji-interpersonalnej',
    'Sukces' => 'https://4grow.pl/szkolenia/wywieranie-wplywu-szkolenie-perswazja',
  ];

  $vocabulary = Vocabulary::load($vocabulary_machine_name);

  if (!$vocabulary) {
    $warning_message = t('Słownik taksonomii "@vocab" nie istnieje. Terminy nie zostały dodane.', [
      '@vocab' => $vocabulary_machine_name,
    ]);
    $messenger->addWarning($warning_message);
    return $warning_message->render();
  }

  $added_terms_count = 0;
  $updated_terms_count = 0;
  $existing_terms_count = 0;

  foreach ($categories_to_add as $term_name => $term_link) {
    $existing_terms = $term_storage->loadByProperties([
      'name' => $term_name,
      'vid' => $vocabulary_machine_name,
    ]);

    $term = NULL;

    if (empty($existing_terms)) {
      try {
        $term = Term::create([
          'name' => $term_name,
          'vid' => $vocabulary_machine_name,
          'langcode' => $default_langcode,
          'field_link' => ['uri' => $term_link],
        ]);
        $term->save();
        $added_terms_count++;
        $messenger->addStatus(t('Dodano termin "@term_name" z linkiem do słownika "@vocab".', [
          '@term_name' => $term_name,
          '@vocab' => $vocabulary_machine_name,
        ]));
      } catch (\Exception $e) {
        $messenger->addError(t('Błąd podczas dodawania terminu "@term_name": @error', [
          '@term_name' => $term_name,
          '@error' => $e->getMessage(),
        ]));
      }
    } else {
      $term = reset($existing_terms);
      $current_link = $term->get('field_link')->uri;

      if ($current_link !== $term_link) {
        try {
          $term->set('field_link', ['uri' => $term_link]);
          $term->save();
          $updated_terms_count++;
          $messenger->addStatus(t('Zaktualizowano link dla terminu "@term_name" w słowniku "@vocab".', [
            '@term_name' => $term_name,
            '@vocab' => $vocabulary_machine_name,
          ]));
        } catch (\Exception $e) {
          $messenger->addError(t('Błąd podczas aktualizacji linku dla terminu "@term_name": @error', [
            '@term_name' => $term_name,
            '@error' => $e->getMessage(),
          ]));
        }
      } else {
        $existing_terms_count++;
      }
    }
  }

  $message_parts = [];
  $message_parts[] = t('Operacja dodawania/aktualizacji terminów kategorii zakończona dla słownika quotes category.')->render();

  if ($added_terms_count > 0) {
    $message_parts[] = t('Dodano @count nowych terminów.', ['@count' => $added_terms_count])->render();
  }
  if ($updated_terms_count > 0) {
    $message_parts[] = t('Zaktualizowano @count terminów z nowymi linkami.', ['@count' => $updated_terms_count])->render();
  }
  if ($existing_terms_count > 0) {
    $message_parts[] = t('@count terminów już istniało i miało poprawny link.', ['@count' => $existing_terms_count])->render();
  }

  $target_categories_count = count($categories_to_add);
  if ($added_terms_count == 0 && $updated_terms_count == 0 && $existing_terms_count == $target_categories_count && $target_categories_count > 0) {
    $message_parts[] = t('Wszystkie zdefiniowane terminy już istniały i miały poprawne linki.')->render();
  } elseif ($added_terms_count == 0 && $updated_terms_count == 0 && $existing_terms_count == 0 && $target_categories_count > 0) {
    $message_parts[] = t('Nie dodano ani nie zaktualizowano żadnych terminów (sprawdź logi błędów).')->render();
  } elseif ($target_categories_count == 0) {
    $message_parts[] = t('Lista kategorii do dodania była pusta.')->render();
  }

  $final_message_string = implode(' ', $message_parts);

  $messenger->addStatus($final_message_string);
  return $final_message_string;
}

/**
 * Imports npx_quote nodes from csv.
 */
function npx_update_10102(&$sandbox)
{
  mb_internal_encoding('UTF-8');
  $messenger = \Drupal::messenger();
  $node_storage = \Drupal::entityTypeManager()->getStorage('node');
  $term_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');

  $module_name = 'npx_quotes';
  $module_path = \Drupal::service('extension.list.module')->getPath($module_name);
  $csv_file_path = $module_path . '/data/npx_quote_import_with_langcode.csv';

  $created_count = 0;
  $skipped_existing_count = 0;
  $skipped_category_issue_count = 0;
  $processed_rows_count = 0;

  $content_type_machine_name = 'npx_quote';
  $author_field_machine_name = 'field_quote_author';
  $category_field_machine_name = 'field_quote_category';
  $category_vocabulary_machine_name = 'quotes_categories';
  $default_text_format = 'basic_html';

  if (!file_exists($csv_file_path)) {
    $messenger->addError(t('Plik CSV z cytatami nie został znaleziony: @path', ['@path' => $csv_file_path]));
    return t('Błąd: Nie znaleziono pliku CSV.');
  }

  if (($handle = fopen($csv_file_path, 'r')) !== FALSE) {
    $header = fgetcsv($handle);
    if ($header === FALSE || count($header) < 5) {
      $messenger->addError(t('Nie można odczytać poprawnych nagłówków z pliku CSV lub plik jest pusty: @path. Oczekiwano co najmniej 5 kolumn.', ['@path' => $csv_file_path]));
      fclose($handle);
      return t('Błąd: Problem z plikiem CSV (nagłówki lub pusty plik).');
    }

    $expected_headers = ['title', 'body', 'field_quote_author', 'field_quote_category', 'langcode'];
    if ($header !== $expected_headers) {
      $messenger->addWarning(t('Nagłówki w pliku CSV (@csv_headers) różnią się od oczekiwanych (@expected_headers). Kontynuuję, zakładając poprawną kolejność kolumn.', [
        '@csv_headers' => implode(', ', $header),
        '@expected_headers' => implode(', ', $expected_headers),
      ]));
    }

    while (($row = fgetcsv($handle, 0, ',')) !== FALSE) {
      $processed_rows_count++;
      $row = array_map(function ($cell) {
        return mb_convert_encoding(trim($cell), 'UTF-8', 'UTF-8');
      }, $row);

      if (count($row) < 5) {
        $row = array_pad($row, 5, '');
        $messenger->addWarning(t('Wiersz #@row_num ma mniej niż 5 kolumn. Brakujące kolumny zostały potraktowane jako puste. Dane: @data', [
          '@row_num' => $processed_rows_count,
          '@data' => implode(', ', $row),
        ]));
      }

      $csv_title = $row[0];
      $csv_body = $row[1];
      $csv_author = $row[2];
      $csv_categories_str = $row[3];
      $csv_langcode = $row[4];

      if (strtolower($csv_author) === 'null') {
        $csv_author = '';
      }

      if (empty($csv_body)) {
        $messenger->addWarning(t('Pominięto wiersz #@row_num z powodu braku treści cytatu. Dane: @data', [
          '@row_num' => $processed_rows_count,
          '@data' => implode(', ', $row),
        ]));
        continue;
      }
      if (empty($csv_langcode)) {
        $csv_langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();
        $messenger->addStatus(t('Ustawiono domyślny język strony (@lang) dla wiersza #@row_num, ponieważ nie podano kodu języka.', ['@lang' => $csv_langcode, '@row_num' => $processed_rows_count]));
      }

      $query = $node_storage->getQuery()
        ->condition('type', $content_type_machine_name)
        ->condition('body.value', mb_substr($csv_body, 0, 250) . '%', 'LIKE') // Użyj mb_substr
        ->condition('langcode', $csv_langcode)
        ->range(0, 1)
        ->accessCheck(FALSE);

      if (!empty($csv_author)) {
        $query->condition($author_field_machine_name . '.value', $csv_author);
      }
      $existing_nodes = $query->execute();

      if (!empty($existing_nodes)) {
        $skipped_existing_count++;
        continue;
      }

      $category_target_ids = [];
      if (!empty($csv_categories_str)) {
        $category_names = explode('|', $csv_categories_str);
        foreach ($category_names as $category_name) {
          $category_name = trim($category_name);
          if (empty($category_name)) continue;

          $terms = $term_storage->loadByProperties([
            'name' => $category_name,
            'vid' => $category_vocabulary_machine_name,
          ]);

          if (!empty($terms)) {
            $term = reset($terms);
            $category_target_ids[] = ['target_id' => $term->id()];
          } else {
            $messenger->addWarning(t('Nie znaleziono kategorii "%cat_name" (słownik: %vocab) dla cytatu w wierszu #@row_num. Kategoria "%cat_name" zostanie pominięta.', [
              '%cat_name' => $category_name,
              '%vocab' => $category_vocabulary_machine_name,
              '@row_num' => $processed_rows_count
            ]));
          }
        }
      }

      if (empty($category_target_ids) && !empty($csv_categories_str) && count(array_filter(explode('|', $csv_categories_str), 'trim')) > 0) {
        $skipped_category_issue_count++;
        $messenger->addWarning(t('Pominięto cytat z wiersza #@row_num, ponieważ nie udało się zmapować żadnej z podanych kategorii: "@categories". Upewnij się, że kategorie istnieją w słowniku "%vocab".', [
          '@row_num' => $processed_rows_count,
          '@categories' => $csv_categories_str,
          '%vocab' => $category_vocabulary_machine_name,
        ]));
        continue;
      }

      $node_title = $csv_title;
      if (empty($node_title) || preg_match('/^cytat_\d+$/i', $node_title)) {
        $title_author_part = !empty($csv_author) ? (' - ' . $csv_author) : '';
        $node_title_body_part = strip_tags($csv_body);
        $node_title = mb_substr($node_title_body_part, 0, 60) . (mb_strlen($node_title_body_part) > 60 ? '...' : '') . $title_author_part;
      }
      if (mb_strlen($node_title) > 255) {
        $node_title = mb_substr($node_title, 0, 252) . '...';
      }
      $node_title = mb_convert_encoding($node_title, 'UTF-8', 'UTF-8');


      try {
        $node_values = [
          'type' => $content_type_machine_name,
          'title' => $node_title,
          'langcode' => $csv_langcode,
          'uid' => 1,
          'status' => 1,
          'body' => [
            'value' => $csv_body,
            'format' => $default_text_format,
          ],
        ];
        if (!empty($csv_author)) {
          $node_values[$author_field_machine_name] = ['value' => $csv_author];
        }
        if (!empty($category_target_ids)) {
          $node_values[$category_field_machine_name] = $category_target_ids;
        }

        $node = Node::create($node_values);
        $node->save();
        $created_count++;

      } catch (\Exception $e) {
        $messenger->addError(t('Krytyczny błąd podczas tworzenia węzła dla wiersza #@row_num: @error. Dane: @data. Tytuł: @title', [
          '@row_num' => $processed_rows_count,
          '@error' => $e->getMessage(),
          '@data' => implode(', ', $row),
          '@title' => $node_title,
        ]));
      }
    }
    fclose($handle);

    $message_parts = [t('Import cytatów z CSV zakończony.')];
    $message_parts[] = t('Przetworzono @processed wierszy danych (nie licząc nagłówka).', ['@processed' => $processed_rows_count]);
    if ($created_count > 0) {
      $message_parts[] = t('Dodano @count nowych cytatów.', ['@count' => $created_count]);
    }
    if ($skipped_existing_count > 0) {
      $message_parts[] = t('Pominięto @count istniejących cytatów.', ['@count' => $skipped_existing_count]);
    }
    if ($skipped_category_issue_count > 0) {
      $message_parts[] = t('Pominięto @count cytatów z powodu problemów z mapowaniem wszystkich podanych kategorii (sprawdź, czy kategorie istnieją).', ['@count' => $skipped_category_issue_count]);
    }
    if ($created_count == 0 && $skipped_existing_count == 0 && $skipped_category_issue_count == 0 && $processed_rows_count > 0) {
      $message_parts[] = t('Nie dodano żadnych nowych cytatów. Sprawdź logi i komunikaty ostrzegawcze.');
    } elseif ($processed_rows_count == 0 && file_exists($csv_file_path) && filesize($csv_file_path) > (isset($header) ? strlen(implode(',', $header)) : 0)) {
      $message_parts[] = t('Nie przetworzono żadnych wierszy danych z pliku CSV (poza nagłówkiem). Sprawdź format pliku i zawartość.');
    }


    $final_message = implode(' ', $message_parts);
    $messenger->addStatus($final_message);
    return $final_message;

  } else {
    $messenger->addError(t('Nie można otworzyć pliku CSV do odczytu: @path', ['@path' => $csv_file_path]));
    return t('Błąd: Nie można otworzyć pliku CSV.');
  }
}

/**
 * Change old quotes simple page path and menu link url.
 */
function npx_update_10103()
{
  $messages = [];

  $path_alias_storage = \Drupal::entityTypeManager()->getStorage('path_alias');
  $node_path = '/node/2380';

  $aliases = $path_alias_storage->loadByProperties([
    'path' => $node_path,
  ]);

  $old_alias_path = '/zlote-mysli-cytaty-aforyzmy-sentencje-rozwoju-osobistym-zawodowym';

  foreach ($aliases as $alias) {
    if ($alias->getAlias() === $old_alias_path) {
      $alias->delete();
      $messages[] = 'Usunięto stary alias: ' . $old_alias_path;
      break;
    }
  }

  $alias_repository = \Drupal::service('path_alias.repository');
  $alias_storage = \Drupal::entityTypeManager()->getStorage('path_alias');

  $new_alias = $alias_storage->create([
    'path' => $node_path,
    'alias' => '/zlote-mysli-old',
    'langcode' => LanguageInterface::LANGCODE_NOT_SPECIFIED,
  ]);
  $new_alias->save();

  $messages[] = 'Utworzono nowy alias: /zlote-mysli-old dla ' . $node_path;


  $menu_link = MenuLinkContent::load(205);

  if ($menu_link) {
    $menu_link->set('link', [
      'uri' => 'internal:' . $old_alias_path
    ]);
    $menu_link->save();
    $messages[] = 'Zaktualizowano link w menu grow3menu (ID: 205)';

    \Drupal::service('cache.menu')->invalidateAll();
  } else {
    $messages[] = 'UWAGA: Nie znaleziono menu link o ID 205';
  }

  return implode(' | ', $messages);
}

/**
 * Update menu link from "Letnia Kampania Szkoleniowa" to "Voucher na szkolenia"
 */
function npx_update_10104(&$sandbox) {
  $menu_link = MenuLinkContent::load(327);

  if ($menu_link) {
    $link_options = $menu_link->link->first()->options;

    $menu_link->set('title', 'Voucher na szkolenia');
    $menu_link->set('link', [
      'uri' => 'https://4grow.pl/voucher-szkoleniowy',
      'options' => $link_options // Keep existing options including class
    ]);

    $menu_link->save();

    \Drupal::logger('npx')->info('Updated menu link ID 327 to Voucher na szkolenia');
  }
}
