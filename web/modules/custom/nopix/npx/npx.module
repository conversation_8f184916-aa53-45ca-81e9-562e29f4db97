<?php

/**
 * @file
 * Contains npx.module..
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Entity\Display\EntityViewDisplayInterface;
use <PERSON><PERSON><PERSON>\node\Entity\NodeType;
use <PERSON><PERSON>al\Core\Link;
use Drupal\Core\Url;
use Drupal\npx\NpxLinkDiscount;
use <PERSON><PERSON><PERSON>\user\UserInterface;
use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\Core\Form\FormStateInterface;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\Core\Entity\ContentEntityInterface;
use Dr<PERSON>al\ckeditor5\Plugin\CKEditor5PluginDefinition;

/**
 * Implements hook_help().
 */
function npx_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the npx module.
    case 'help.page.npx':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('NoPix') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_preprocess_HOOK().
 */
function npx_preprocess_page_title(&$variables) {
  $variables['npx']['page_subtitle'] = \Drupal::config('npx.npxsettings')->get('npx_page_subtitle');
}

/**
 * Implements hook_preprocess_HOOK().
 */
function npx_page_attachments(array &$attachments) {
  $attachments['#attached']['library'][] = 'npx/npx.main';
  $attachments['#attached']['library'][] = 'npx/npx.npxtopmenu';
  $attachments['#attached']['library'][] = 'npx/npx.npxglobalcss';
  $attachments['#attached']['library'][] = 'npx/npx.npxglobal';
  if(\Drupal::currentUser()->isAuthenticated()) {
    $attachments['#attached']['library'][] = 'npx/npx.npxadmin';
  }
}

/**
 * Implements hook_page_attachments_alter()
 */
function npx_page_attachments_alter(array &$attachments): void {
  //unset <meta name="Generator" content="Drupal 10 (https://www.drupal.org)" />
  foreach ($attachments['#attached']['html_head'] as $key => $attachment) {
    if($attachment[1] == 'system_meta_generator') {
      unset($attachments['#attached']['html_head'][$key]);
    }
  }
}

/**
 * Implements hook_entity_extra_field_info().
 */
function npx_entity_extra_field_info() {
  $extra = [];

  foreach (NodeType::loadMultiple() as $bundle) {
    if($bundle->Id() == 'opinia') {
      $extra['node'][$bundle->Id()]['display']['npx_training_title_extra'] = [
        'label' => t('Training title extra field'),
        'description' => t('Training title pseudo-field'),
        'weight' => 100,
        'visible' => true,
      ];
    }
  }

  return $extra;
}

/*
 * Implements hook_ENTITY_TYPE_view_alter.
 */
function npx_node_view_alter(array &$build, EntityInterface $entity, EntityViewDisplayInterface $display) {
  if($entity instanceof Node && $entity->getType() == 'opinia' && $build['#view_mode'] == 'full') {
    $current_user = \Drupal::currentUser();
    $roles = $current_user->getRoles();
    if(!(in_array('administrator', $roles) || in_array('redaktor', $roles))) {
      $build['node_link']['#access'] = false;
    }
    if ($display->getComponent('npx_training_title_extra')) {
      $markup = '';
      if (empty($entity->field_kategoria->getValue())) {
        return;
      }

      $cat_id = $entity->field_kategoria->entity->id();

      if ($cat_id) {
        $query = \Drupal::entityQuery('node')
        ->condition('type','npxtraining')
        ->condition('field_kategoria', $cat_id)
        ->accessCheck(FALSE)
        ->range(0, 1);

        $query_res = $query->execute();

        $id = reset($query_res);

        //kint($id);

        if($id) {
          $manager = \Drupal::getContainer()->get('entity_type.manager')->getStorage('node');
          $node = $manager->load($id);
          $markup = Link::fromTextAndUrl($node->title->value, Url::fromUri('internal:/node/'.$node->id(), ['absolute' => true]))->toString();
          $build['field_kategoria']['#access'] = false;
          $build['npx_training_title_extra'] = [
            '#type' => 'markup',
            '#markup' => $markup,
            '#prefix' => '<h3>',
            '#suffix' => '</h3>'
          ];
        }
      }

    }
  }
}

/**
 * Implements hook_ds_pre_render_alter().
 */
function npx_ds_pre_render_alter(array &$layout_render_array, array $context, array &$vars) {
  if($context['entity_type'] == 'node' && $context['bundle'] == 'npxtraining' && $context['view_mode'] == 'full') {

    foreach( $layout_render_array['ds_content'] as $i => $val) {
      if(isset($val['#attributes']['id'])) {
        $id = $val['#attributes']['id']->value();
        if($id == 'szkolenie-grupa-7') {
            $layout_render_array['ds_content'][$i]['#access'] = false;
        }
        elseif ($id == 'szkolenie-grupa-12') {
          //hide empty fields
          $arg_par = $layout_render_array['ds_content'][$i]['group_narrow2']['field_arguments_par'][0]['#paragraph'];
          if(!$arg_par->field_arguments_right->value) {
            $layout_render_array['ds_content'][$i]['group_narrow2']['field_arguments_par']['#access'] = false;
          }
        }
      }
    }
  }
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function npx_npxlinkdiscount_presave(EntityInterface $entity) {
  if ($entity->isNew()) {
    $hash = NpxLinkDiscount::generateHash();

    $entity->set(NpxLinkDiscount::FIELD_HASH, $hash);
    $entity->set('title', $entity->title->value . ' ?nld=' . $hash);
  }
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function npx_user_presave(UserInterface $user) {
  if($user->isNew()) {
    $hash = $user->get('field_npxhash')->value;

    if( $hash == '689e17686684863e8624062ca37b2fdd') {
      $user->addRole('webinar200');
    }
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function npx_form_node_npxtraining_edit_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  $form['#attached']['library'][] = 'npx/npx.npxeditform';
  $form['#validate'][] = 'npx_custom_training_type_validate';
}

function npx_custom_training_type_validate($form, \Drupal\Core\Form\FormStateInterface $form_state) {
  $stationary = $form_state->getValue('field_npxtraining_stationary');
  $online = $form_state->getValue('field_npxtraining_online');
  $online_live = $form_state->getValue('field_online_live');

  $stationary = is_array($stationary) ? ($stationary['value'] ?? 0) : $stationary;
  $online = is_array($online) ? ($online['value'] ?? 0) : $online;
  $online_live = is_array($online_live) ? ($online_live['value'] ?? 0) : $online_live;

  if (empty($stationary) && empty($online) && empty($online_live)) {
    $form_state->setErrorByName('field_npxtraining_stationary', t('Musisz wybrać przynajmniej jedną opcję: Stacjonarne, Online lub Online Live.'));
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function npx_form_user_register_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  $hash = \Drupal::request()->query->get('webreg');

   if(isset($form['field_npxhash']['widget'][0]['value'])) {
     $form['field_npxhash']['widget'][0]['value']['#type'] = 'hidden';
     $form['field_npxhash']['widget'][0]['value']['#value'] = $hash;
   }
}

/**
 * Alter the fields used to represent an entity in the IEF table.
 *
 * @param array $fields
 *   The fields, keyed by field name.
 * @param array $context
 *   An array with the following keys:
 *   - parent_entity_type: The type of the parent entity.
 *   - parent_bundle: The bundle of the parent entity.
 *   - field_name: The name of the reference field on which IEF is operating.
 *   - entity_type: The type of the referenced entities.
 *   - allowed_bundles: Bundles allowed on the reference field.
 *
 * @see \Drupal\inline_entity_form\InlineFormInterface::getTableFields()
 */
function npx_inline_entity_form_table_fields_alter(&$fields, $context) {
  if ($context['entity_type'] == 'node' && in_array('npxtraining_date', $context['allowed_bundles'])) {
    $fields['field_npxtraining_date_start'] = [
      'type' => 'field',
      'label' => t('Start'),
      'settings' => [
      ],
      'weight' => 101,
    ];
  }
}

/**
 * Set dynamic allowed values for field_npxtraining_date_form.
 */
function npx_field_npxtraining_date_form_values(FieldStorageConfig $definition = null, ContentEntityInterface $entity = null, $cacheable = false) {
  return [
    'stationary' => 'Stacjonarne',
    'online' => 'Online',
    'online_live' => 'Online Live'
  ];
}

/**
 * Drupal 10.3+: Enable upload of SVGs and WebP images in the CKEditor.
 */

function npx_ckeditor5_plugin_info_alter(array &$plugin_definitions): void {
  $image_upload_plugin_definition = $plugin_definitions['ckeditor5_imageUpload']->toArray();
  $image_upload_plugin_definition['ckeditor5']['config']['image']['upload']['types'][] = 'webp';
  $image_upload_plugin_definition['ckeditor5']['config']['image']['upload']['types'][] = 'svg+xml';
  $plugin_definitions['ckeditor5_imageUpload'] = new CKEditor5PluginDefinition($image_upload_plugin_definition);
}

/**
 * Implements hook_cron().
 */
function npx_cron() {
  $last_check = \Drupal::state()->get('npx.last_discount_check', 0);
  $request_time = \Drupal::time()->getRequestTime();

  if($request_time > $last_check + 60) {//TODO increase time interval after testing
    /** @var \Drupal\npx\NpxDiscountManagerService $manager */
    $manager = \Drupal::service('npx.discount_manager');
    $manager->renewDiscounts();
    \Drupal::state()->set('npx.last_discount_check', $request_time);
  }
}

use Drupal\image\Entity\ImageStyle;
use Drupal\file\Entity\File;

/**
 * Implements hook_preprocess_field().
 * for video embed fields in Wideo nodes - wideo opinie - to replace default yt thumbnail with chosen thumbnail if exists
 * for video embed fields in tytul_szkolenia_video paragraphs - to replace default yt thumbnail with field_ts_obraz if exists
 */
function npx_preprocess_field(array &$variables) {
  $element = $variables['element'];
  
  // Original logic for Wideo nodes
  if (
    isset($element['#field_name']) &&
    $element['#field_name'] === 'field_video_embed' &&
    isset($element['#bundle']) &&
    $element['#bundle'] === 'wideo'
  ) {
    $node = $element['#object'];
    if ($node->hasField('field_zaslepka') && !$node->get('field_zaslepka')->isEmpty()) {
      $image_file = $node->get('field_zaslepka')->first()->entity;
      if ($image_file instanceof File) {

        $uri = $image_file->getFileUri();
        $styled_url = ImageStyle::load('video_thumbnails')->buildUrl($uri);

        // Loop through the rendered items and replace the image source
        foreach ($variables['items'] as &$item) {
          if (isset($item['content']['children']['#markup'])) {
            $item['content']['children']['#markup'] = preg_replace(
              '#<img[^>]+src="[^"]+"#',
              '<img src="' . $styled_url . '"',
              $item['content']['children']['#markup']
            );
          }
        }
      }
    }
  }
  
  // New logic for tytul_szkolenia_video paragraphs
  if (
    isset($element['#field_name']) &&
    $element['#field_name'] === 'field_ts_video_embed' &&
    isset($element['#object']) &&
    $element['#object']->getEntityTypeId() === 'paragraph' &&
    $element['#object']->bundle() === 'tytul_szkolenia_video'
  ) {
    $paragraph = $element['#object'];
    if ($paragraph->hasField('field_ts_obraz') && !$paragraph->get('field_ts_obraz')->isEmpty()) {
      $image_file = $paragraph->get('field_ts_obraz')->first()->entity;
      if ($image_file instanceof File) {

        $uri = $image_file->getFileUri();
        
        // Use the same image style as the video field formatter
        $view_mode = $element['#view_mode'] ?? 'default';
        $display_options = \Drupal::service('entity_display.repository')
          ->getViewDisplay('paragraph', 'tytul_szkolenia_video', $view_mode)
          ->getComponent('field_ts_video_embed');
        
        // Use video_thumbnails style (1280x720)
        $image_style = ImageStyle::load('video_thumbnails');
        $styled_url = $image_style->buildUrl($uri);

        
        // Loop through the rendered items and replace the image source
        foreach ($variables['items'] as &$item) {
          // Try different possible locations for the markup
          if (isset($item['content']['children']['#markup'])) {
            $item['content']['children']['#markup'] = preg_replace(
              '/src="[^"]*"/',
              'src="' . $styled_url . '"',
              $item['content']['children']['#markup']
            );
          } elseif (isset($item['content']['#markup'])) {
            $item['content']['#markup'] = preg_replace(
              '/src="[^"]*"/',
              'src="' . $styled_url . '"',
              $item['content']['#markup']
            );
          } elseif (isset($item['#markup'])) {
            $item['#markup'] = preg_replace(
              '/src="[^"]*"/',
              'src="' . $styled_url . '"',
              $item['#markup']
            );
          } else {
            // Try to find markup in any nested structure
            array_walk_recursive($item, function(&$value, $key) use ($styled_url) {
              if ($key === '#markup' && is_string($value) && strpos($value, '<img') !== FALSE) {
                $value = preg_replace(
                  '/src="[^"]*"/',
                  'src="' . $styled_url . '"',
                  $value
                );
              }
            });
          }
        }
      }
    }
  }
}

