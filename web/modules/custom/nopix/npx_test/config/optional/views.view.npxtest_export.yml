langcode: pl
status: true
dependencies:
  config:
    - user.role.administrator
    - user.role.manager
    - user.role.redaktor
  module:
    - csv_serialization
    - npx_test
    - rest
    - serialization
    - user
    - views_data_export
id: npxtest_export
label: 'Npxtest export'
module: views
description: ''
tag: ''
base_table: npx_test_result
base_field: tid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        email:
          id: email
          table: npx_test_result
          field: email
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: standard
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
      pager:
        type: none
        options:
          offset: 0
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
            manager: manager
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        email:
          id: email
          table: npx_test_result
          field: email
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments:
        tid:
          id: tid
          table: npx_test_result
          field: tid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: node
          default_argument_options: {  }
          default_argument_skip_url: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - user.roles
      tags: {  }
  data_export_1:
    id: data_export_1
    display_title: 'Eksport danych'
    display_plugin: data_export
    position: 1
    display_options:
      style:
        type: data_export
        options:
          formats:
            csv: csv
          csv_settings:
            delimiter: ;
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '1'
            use_serializer_encode_only: false
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/structure/npxtest-export
      auth:
        - cookie
      displays:
        embed_1: embed_1
        default: '0'
      filename: ''
      automatic_download: true
      store_in_public_file_directory: false
      custom_redirect_path: false
      redirect_to_display: none
      include_query_params: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - request_format
        - url
        - user.roles
      tags: {  }
  embed_1:
    id: embed_1
    display_title: Osadzony
    display_plugin: embed
    position: 2
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - user.roles
      tags: {  }
