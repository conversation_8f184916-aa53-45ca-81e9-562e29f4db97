<?php

/**
 * @file
 * Contains npx_test.module.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function npx_test_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the npx_test module.
    case 'help.page.npx_test':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Npx Competency Test') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_mail().
 */
function npx_test_mail($key, &$message, $params) {
  switch ($key) {
    case 'npx_test_send_result':
      $message['from'] = $params['from'];
      $message['subject'] = $params['subject'];
      $message['body'][] = $params['message'];
      $message['headers']['From'] = $params['from'];
      $message['headers']['Importance'] = 'high';
      $message['headers']['X-Priority'] = 1;
      $message['headers']['Disposition-Notification-To'] = $params['from'];
      break;
  }
}

/**
 * Implements hook_views_data().
 * @see https://api.drupal.org/api/drupal/core%21modules%21views%21views.api.php/function/hook_views_data/10
 */
function npx_test_views_data() {
  $data = [];
  $data['npx_test_result'] = [];
  $data['npx_test_result']['table'] = [];
  $data['npx_test_result']['table']['group'] = t('Npx test');
  $data['npx_test_result']['table']['provider'] = 'npx_test';
  $data['npx_test_result']['table']['base'] = [
    'field' => 'tid',
    'title' => t('Npx test results'),
    'help' => t('Npx test results.'),
    'weight' => -10,
  ];
  $data['npx_test_result']['table']['join'] = [
    'npxtest' => [
      'left_field' => 'id',
      'field' => 'tid',
    ],
  ];
  $data['npx_test_result']['tid'] = [
    'title' => t('Npxtest ID'),
    'help' => t('Related test.'),
    'relationship' => [
      'base' => 'npxtest',
      'base field' => 'id',
      'id' => 'standard',
      'label' => t('Npx related test.'),
    ],
    'field' => [
      'id' => 'standard',
    ],
    'filter' => [
      'id' => 'numeric',
    ],
    'argument' => [
      'id' => 'numeric',
    ],  
  ];
  $data['npx_test_result']['email'] = [
    'title' => t('Npx user email'),
    'help' => t('Npx user email.'),
    'field' => [
      'id' => 'standard',
    ],
    'sort' => [
      'id' => 'standard',
    ],
    'filter' => [
      'id' => 'string',
    ],
    'argument' => [
      'id' => 'string',
    ],
  ];
  $data['npx_test_result']['timestamp'] = [
    'title' => t('Npx timestamp'),
    'help' => t('Created on.'),
    'field' => [
      'id' => 'date',
    ],
    'sort' => [
      'id' => 'date',
    ],
    'filter' => [
      'id' => 'date',
    ],
  ];
  $data['npx_test_result']['result'] = [
    'title' => t('Npx test result'),
    'help' => t('Npx test result field.'),
    'field' => [
      'id' => 'npx_test_result_data',
    ],
  ];
  return $data;
}
/**
 * Implements hook_preprocess_HOOK() for html templates.
 */
function npx_test_preprocess_html__npxtest(array &$variables) {
  $entity_id = \Drupal::routeMatch()->getParameter('entity_id');
  if ($entity_id) {
    $entity = \Drupal::entityTypeManager()->getStorage('npxtest')->load($entity_id);

    if ($entity instanceof \Drupal\npx_test\Entity\NpxTestEntity) {
      // Retrieve and unserialize the field_metatags value.
      $metatags_value = $entity->get('field_metatags')->value;
      $metatags = unserialize($metatags_value);

      // Generate the <meta> tags dynamically.
      $meta_tags_markup = '';
      if (is_array($metatags)) {
        foreach ($metatags as $name => $content) {
          $meta_tags_markup .= '<meta name="' . htmlspecialchars($name, ENT_QUOTES, 'UTF-8') . '" content="' . htmlspecialchars($content, ENT_QUOTES, 'UTF-8') . '">';
        }
      }
      $variables['meta_tags_markup'] = $meta_tags_markup;
    }
  }
}

function npx_test_preprocess_html__admin__structure__npxtest(array &$variables) {
  // Get the current path.
  $current_path = \Drupal::service('path.current')->getPath();

  // Resolve the alias to the internal path.
  $path_alias_manager = \Drupal::service('path_alias.manager');
  $internal_path = $path_alias_manager->getPathByAlias($current_path);

  // Extract the entity ID from the internal path.
  if (preg_match('/\/admin\/structure\/npxtest\/(\d+)/', $internal_path, $matches)) {
    $entity_id = $matches[1];

    if ($entity_id) {
      // Load the entity using the entity ID.
      $entity = \Drupal::entityTypeManager()->getStorage('npxtest')->load($entity_id);

      if ($entity instanceof \Drupal\npx_test\Entity\NpxTestEntity) {
        // Retrieve and unserialize the field_metatags value.
        $metatags_value = $entity->get('field_metatags')->value;
        $metatags = unserialize($metatags_value);

        // Generate the <meta> tags dynamically.
        $meta_tags_markup = '';
        if (is_array($metatags)) {
          foreach ($metatags as $name => $content) {
            $meta_tags_markup .= '<meta name="' . htmlspecialchars($name, ENT_QUOTES, 'UTF-8') . '" content="' . htmlspecialchars($content, ENT_QUOTES, 'UTF-8') . '">';
          }
        }
        $variables['meta_tags_markup'] = $meta_tags_markup;
      }
    }
  }
}