(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.npxQuotesScroll = {
    attach: function (context, settings) {
      once('npx-quotes-scroll', '.quotes-category-link', context).forEach(function (element) {
        $(element).on('click', function (e) {
          e.preventDefault();
          
          var categoryId = $(this).data('category-id');
          var targetElement = $('#category_' + categoryId);
          
          if (targetElement.length) {
            // Native smooth scroll - much more consistent
            window.scrollTo({
              top: targetElement.offset().top - 100,
              behavior: 'smooth'
            });
          }
        });
      });
    }
  };

})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, once); 