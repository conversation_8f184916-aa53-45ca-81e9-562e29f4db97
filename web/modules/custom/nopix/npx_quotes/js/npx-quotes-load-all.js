(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.npxQuotesLoadAll = {
    attach: function (context, settings) {
      $('.quotes-category-section', context).each(function() {
        if ($(this).find('.more-quotes-button-wrapper').length > 0) {
          $(this).addClass('has-more-button');
        }
      });

      once('npx-quotes-load-all', '.load-more-quotes', context).forEach(function (element) {
        $(element).on('click', function (e) {
          e.preventDefault();

          var $button = $(this);
          var $categorySection = $button.closest('.quotes-category-section');
          var $viewContent = $categorySection.find('.view-content');
          var categoryId = $categorySection.attr('data-category-id');

          if (!categoryId || $viewContent.length === 0) {
            return;
          }

          if ($categorySection.hasClass('loading-ajax')) {
            return;
          }

          var currentCount = $viewContent.find('.views-row').length;

          $categorySection.addClass('loading-ajax');
          $categorySection.find('.btn-quotes').prop('disabled', true).addClass('disabled');
          $button.text('Ładowanie...');

          $.ajax({
            url: '/npx-quotes/ajax/load-all/' + categoryId,
            type: 'GET',
            data: { skip: currentCount },
            success: function (response) {
              var addedCount = 0;

              if (response && response.html && response.html.trim() !== '') {
                $viewContent.append(response.html);

                var newCount = $viewContent.find('.views-row').length;
                addedCount = newCount - currentCount;

                var $newRows = $viewContent.find('.views-row').slice(currentCount);
                $newRows.each(function() {
                  Drupal.attachBehaviors(this);
                });
              }

              if (addedCount > 0) {
                $button.closest('.more-quotes-button-wrapper').remove();
                $categorySection.removeClass('has-more-button');
              }
            },
            error: function (xhr, status, error) {
              $button.text('Błąd ładowania').prop('disabled', true);
            },
            complete: function () {
              $categorySection.removeClass('loading-ajax');
              $categorySection.find('.btn-quotes:not(.load-more-quotes)').prop('disabled', false).removeClass('disabled');
            }
          });
        });
      });
    }
  };

})(jQuery, Drupal, once);
