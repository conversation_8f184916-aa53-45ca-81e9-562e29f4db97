(function($, Drupal, drupalSettings) {
  function attachAgreementPopover(context) {
    var popoverText = drupalSettings?.npx_freshmail?.accept_text || 'Treść twojego popovera';
    $('.npx-popover-trigger', context).each(function() {
      var $trigger = $(this);
      if (!$trigger.attr('data-bs-toggle')) {
        $trigger.attr({
          'data-bs-toggle': 'popover',
          'data-bs-trigger': 'focus hover',
          'data-bs-html': 'true',
          'data-bs-placement': 'top',
          'data-bs-content': popoverText,
          'tabindex': 0
        });
      }
      if (typeof $trigger.popover === "function") {
        $trigger.popover();
      }
    });
  }

  Drupal.behaviors.npxQuotesNpxQuotesPopover = {
    attach: function(context, settings) {
      attachAgreementPopover(context);

      $(document).ajaxComplete(function() {
        attachAgreementPopover(document);
      });

      $(document).on('dialogopen', '.ui-dialog', function(e) {
        attachAgreementPopover(e.target);
        $(this).find('.npx-popover-trigger[data-bs-toggle="popover"]').popover();
      });
    }
  };
})(jQuery, Drupal, drupalSettings);
