<?php

namespace Drupal\npx_quotes\Plugin\Block;

use <PERSON><PERSON><PERSON>\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON><PERSON>\Core\Routing\UrlGeneratorInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Drupal\Core\Entity\EntityTypeManagerInterface;

/**
 * Provides a 'Quotes Menu' block.
 *
 * @Block(
 * id = "quotes_menu_block",
 * admin_label = @Translation("Quotes Menu")
 * )
 */
class QuotesCategoryMenuBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * The URL generator service.
   *
   * @var \Drupal\Core\Routing\UrlGeneratorInterface
   */
  protected UrlGeneratorInterface $urlGenerator;

  /**
   * The request stack.
   *
   * @var \Symfony\Component\HttpFoundation\RequestStack
   */
  protected RequestStack $requestStack;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * Constructs a new QuotesCategoryMenuBlock instance.
   *
   * @param array $configuration
   * A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   * The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   * The plugin implementation definition.
   * @param \Drupal\Core\Routing\UrlGeneratorInterface $urlGenerator
   * The URL generator service.
   * @param \Symfony\Component\HttpFoundation\RequestStack $requestStack
   * The request stack.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entityTypeManager
   * The entity type manager service.
   */
  public function __construct(
    array $configuration,
          $plugin_id,
          $plugin_definition,
    UrlGeneratorInterface $urlGenerator,
    RequestStack $requestStack,
    EntityTypeManagerInterface $entityTypeManager
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->urlGenerator = $urlGenerator;
    $this->requestStack = $requestStack;
    $this->entityTypeManager = $entityTypeManager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('url_generator'),
      $container->get('request_stack'),
      $container->get('entity_type.manager') // Get the entity type manager
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {

    // Mapowanie nazw w menu do rzeczywistych nazw kategorii w bazie  
    $categories_data = [
      'Miłość, zakochanie' => ['taxonomy_name' => 'Miłość, zakochanie, związki', 'icon' => 'milosc-i-zakochanie.svg'],
      'Praca i kariera' => ['taxonomy_name' => 'Praca i kariera', 'icon' => 'praca-i-kariera.svg'], 
      'Zarządzanie, szef' => ['taxonomy_name' => 'Zarządzanie i szef', 'icon' => 'zarzadzanie-szef.svg'],
      'Praca zespołowa' => ['taxonomy_name' => 'Praca zespołowa', 'icon' => 'praca-zespolowa.svg'],
      'Rozwój osobisty' => ['taxonomy_name' => 'Rozwój osobisty', 'icon' => 'rozwoj-osobisty.svg'],
      'Sukces' => ['taxonomy_name' => 'Sukces', 'icon' => 'sukces.svg'],
    ];

    $vocabulary_machine_name = 'quotes_categories';
    $term_storage = $this->entityTypeManager->getStorage('taxonomy_term');
    $items = [];

    $current_path = $this->requestStack->getCurrentRequest()->getPathInfo();

    foreach ($categories_data as $display_name => $category_info) {
      $taxonomy_name = $category_info['taxonomy_name'];
      $icon_file = $category_info['icon'];
      
      $terms = $term_storage->loadByProperties([
        'name' => $taxonomy_name,
        'vid' => $vocabulary_machine_name,
      ]);

      if (!empty($terms)) {
        $term = reset($terms);
        $term_id = $term->id();

        // Get the SVG icon path
        $module_path = \Drupal::service('extension.list.module')->getPath('npx_quotes');
        $icon_url = '/' . $module_path . '/icons/' . $icon_file;
        $full_icon_path = DRUPAL_ROOT . '/' . $module_path . '/icons/' . $icon_file;
        
        if (file_exists($full_icon_path)) {
          // Use img tag with SVG URL instead of inline
          $icon_span = '<span class="menu-icon"><img src="' . $icon_url . '" alt="" width="16" height="16" /></span>';
        } else {
          // Fallback to original emoji
          $fallback_icons = [
            'Miłość, zakochanie' => '❤️',
            'Praca i kariera' => '💼',
            'Zarządzanie, szef' => '👨‍💼',
            'Praca zespołowa' => '👥',
            'Rozwój osobisty' => '🧠',
            'Sukces' => '🌟',
          ];
          $icon_span = '<span class="menu-icon">' . $fallback_icons[$display_name] . '</span>';
        }
        
        $text_span = '<span class="menu-text">' . $display_name . '</span>';
        $link_text_markup = $icon_span . $text_span;

        // Use anchor links to scroll to sections instead of separate pages
        $url = Url::fromUri('internal:#category_' . $term_id);

        $link_options = [
          'attributes' => [
            'class' => ['quotes-category-link'],
            'data-category-id' => $term_id,
          ],
        ];

        $items[] = [
          '#type' => 'link',
          '#title' => ['#markup' => $link_text_markup],
          '#url' => $url,
          '#options' => $link_options,
        ];
      } else {
        \Drupal::logger('npx_quotes')->warning('Taxonomy term "@taxonomy_name" not found in vocabulary "@vocab_name" for QuotesCategoryMenuBlock.', [
          '@taxonomy_name' => $taxonomy_name,
          '@vocab_name' => $vocabulary_machine_name,
        ]);
      }
    }

    $build = [
      'title' => [
        '#type' => 'html_tag',
        '#tag' => 'h3',
        '#value' => $this->t('Złote myśli, cytaty, aforyzmy, sentencje o rozwoju osobistym i zawodowym'),
        '#attributes' => ['class' => ['quotes-title']],
      ],
      'list' => [
        '#theme' => 'item_list',
        '#items' => $items,
        '#attributes' => ['class' => ['quotes-category-menu']],
        '#attached' => [
          'library' => ['bootstrap4grow/npxquotes', 'npx_quotes/quotes-scroll'],
        ],
      ],
      'info_text' => [
        '#type' => 'html_tag',
        '#tag' => 'p',
        '#value' => $this->t('A jeśli szukasz inspiracji i chcesz rozwijać swoje kompetencje - sprawdź nasze <a href="@training_link" target="_blank">szkolenia miękkie</a> lub sprawdź swoje kompetencje - <a href="@tests_link" target="_blank">testy kompetencji miękkich</a>!', [
          '@training_link' => 'https://4grow.pl/szkolenia-miekkie',
          '@tests_link' => 'https://4grow.pl/testy-kompetencji-miekkich-online',
        ]),
        '#attributes' => ['class' => ['quotes-info-text']],
      ],
    ];

    $build['#cache']['contexts'][] = 'url.path';

    $build['#cache']['tags'][] = 'taxonomy_term_list:' . $vocabulary_machine_name;


    return $build;
  }

}
