<?php

namespace Drupal\npx_quotes\Plugin\WebformHandler;

use Dr<PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\webform\Plugin\WebformHandlerBase;
use <PERSON><PERSON><PERSON>\webform\WebformSubmissionInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\npx_pdf\PDFHelper;
use Dr<PERSON>al\taxonomy\Entity\Term;
use Drupal\Core\Url;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Messenger\MessengerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Drupal\Core\StringTranslation\TranslationInterface;
use Drupal\file\FileRepositoryInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Dr<PERSON>al\Core\Session\AccountProxyInterface;
use <PERSON><PERSON>al\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Component\Transliteration\TransliterationInterface;

/**
 * Webform submission handler for generating and emailing quotes PDF.
 *
 * @WebformHandler(
 *   id = "npx_quotes_pdf_handler",
 *   label = @Translation("Quotes PDF Generator"),
 *   category = @Translation("Custom"),
 *   description = @Translation("Generates a PDF of quotes based on category ID from URL and emails it."),
 *   cardinality = \Drupal\webform\Plugin\WebformHandlerInterface::CARDINALITY_SINGLE,
 *   results = \Drupal\webform\Plugin\WebformHandlerInterface::RESULTS_PROCESSED,
 * )
 */
class QuotesPdfWebformHandler extends WebformHandlerBase implements ContainerFactoryPluginInterface {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The request stack.
   *
   * @var \Symfony\Component\HttpFoundation\RequestStack
   */
  protected $requestStack;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The file repository service.
   *
   * @var \Drupal\file\FileRepositoryInterface
   */
  protected $fileRepository;

  /**
   * The mail manager service.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  protected $mailManager;

  /**
   * The current user service.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * The logger factory service.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The transliteration service.
   *
   * @var \Drupal\Component\Transliteration\TransliterationInterface
   */
  protected $transliteration;

  /**
   * Constructs a new QuotesPdfWebformHandler object.
   */
  public function __construct(
    array $configuration,
          $plugin_id,
          $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    MessengerInterface $messenger,
    RequestStack $request_stack,
    TranslationInterface $string_translation,
    FileSystemInterface $file_system,
    FileRepositoryInterface $file_repository,
    MailManagerInterface $mail_manager,
    AccountProxyInterface $current_user,
    LoggerChannelFactoryInterface $logger_factory,
    TransliterationInterface $transliteration
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
    $this->messenger = $messenger;
    $this->requestStack = $request_stack;
    $this->stringTranslation = $string_translation;
    $this->fileSystem = $file_system;
    $this->fileRepository = $file_repository;
    $this->mailManager = $mail_manager;
    $this->currentUser = $current_user;
    $this->loggerFactory = $logger_factory;
    $this->transliteration = $transliteration;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('messenger'),
      $container->get('request_stack'),
      $container->get('string_translation'),
      $container->get('file_system'),
      $container->get('file.repository'),
      $container->get('plugin.manager.mail'),
      $container->get('current_user'),
      $container->get('logger.factory'),
      $container->get('transliteration')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state, WebformSubmissionInterface $webform_submission) {
    mb_internal_encoding('UTF-8');
    $values = $webform_submission->getData();

    $request = $this->requestStack->getCurrentRequest();
    $destination_url = $request->headers->get('referer') ?: Url::fromRoute('<front>')->toString();
    $redirect_url_object = Url::fromUri($destination_url);
    $form_state->setRedirectUrl($redirect_url_object);

    $imie = isset($values['name']) ? trim($values['name']) : 'Użytkownik';
    $email = isset($values['email']) ? trim($values['email']) : NULL;

    $category_filter_value = $this->getCategoryFilterFromRequest();

    $existing_pdf_path = $this->getExistingPdfPath($category_filter_value);

    if ($existing_pdf_path) {
      $result = $this->sendExistingPdfEmail($existing_pdf_path, $category_filter_value, $email, $imie);
    } else {
      $quotes_data = $this->getFilteredQuotesData($category_filter_value);

      if (!empty($quotes_data)) {
        $html_content_for_pdf = $this->generatePdfContent($quotes_data);
        $result = $this->sendQuotesPdfEmail($html_content_for_pdf, $category_filter_value, $email, $imie);
      } else {
        $this->messenger->addWarning($this->t('Nie znaleziono żadnych cytatów do wygenerowania PDF dla podanej kategorii.'));
        return;
      }
    }

    if ($result === TRUE) {
      $this->messenger->addStatus($this->t('E-mail z cytatami został wysłany pomyślnie na adres %email. Sprawdź swoją skrzynkę odbiorczą!', ['%email' => $email]));
    } else {
      $this->messenger->addError($this->t('Wystąpił problem z wysyłaniem maila. Prosimy spróbować ponownie.'));
    }
  }

  /**
   * Checks if existing PDF file exists for given category.
   *
   * @param string|int $category_filter_value
   *   The category filter value.
   *
   * @return string|null
   *   The path to existing PDF file or NULL if not found.
   */
  private function getExistingPdfPath($category_filter_value) {
    // Określ ścieżkę do katalogu z gotowymi PDF-ami w module
    $module_path = \Drupal::service('extension.list.module')->getPath('npx_quotes');
    $pdf_directory = $module_path . '/pdf_templates/';

    $filename = '4grow-cytaty-wszystkie.pdf';
    if ($category_filter_value !== 'all' && is_numeric($category_filter_value)) {
      $term = Term::load($category_filter_value);
      if ($term) {
        $term_name = $term->getName();
        $transliterated_name = $this->transliteration->transliterate($term_name, 'en', '_');
        $transliterated_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $transliterated_name);
        $transliterated_name = preg_replace('/_+/', '_', $transliterated_name); // Usuń wielokrotne podkreślenia
        $transliterated_name = trim($transliterated_name, '_'); // Usuń podkreślenia na początku i końcu
        $transliterated_name = strtolower($transliterated_name); // Zamień na małe litery

        $filename = '4grow-cytaty-' . $transliterated_name . '.pdf';

      }
    }

    $pdf_path = $pdf_directory . $filename;

    if (file_exists($pdf_path)) {
      return $pdf_path;
    }

    return NULL;
  }

  /**
   * Sends existing PDF email.
   *
   * @param string $pdf_path
   *   The path to existing PDF file.
   * @param string|int $category_filter_value
   *   The category filter value.
   * @param string $email
   *   The recipient email.
   * @param string $imie
   *   The recipient name.
   *
   * @return bool
   *   TRUE if email was sent successfully, FALSE otherwise.
   */
  private function sendExistingPdfEmail($pdf_path, $category_filter_value, $email, $imie) {
    try {
      // Pobierz nazwę pliku z ścieżki
      $filename = basename($pdf_path);

      $params = [
        'subject' => $this->t('Twoje cytaty z 4grow.pl'),
        'body' => [
          '#markup' => $this->t('Cześć @imie! <br><br> Dziękujemy za Twoje zainteresowanie zbiorem cytatów z 4grow.pl. <br><br>W załączniku znajdziesz plik PDF, przygotowany specjalnie dla Ciebie.<br><br>Z pozdrowieniami,<br>Zespół 4grow.pl', ['@imie' => $imie]),
        ],
        'attachments' => [[
          'filepath' => $pdf_path,
          'filename' => $filename,
          'filemime' => 'application/pdf',
        ]],
      ];

      $result = $this->mailManager->mail(
        'npx_quotes',
        'quotes_pdf',
        $email,
        $this->currentUser->getPreferredLangcode(),
        $params,
        NULL,
        TRUE
      );

      return !empty($result['result']);
    }
    catch (\Exception $e) {
      $this->messenger->addError($this->t('Błąd podczas wysyłania PDF: @error', ['@error' => $e->getMessage()]));
      $this->loggerFactory->get('npx_pdf')->error('Błąd wysyłania PDF: @error', ['@error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
      return FALSE;
    }
  }

  /**
   * Gets category filter from request.
   *
   * @return string|int
   *   The category filter value.
   */
  private function getCategoryFilterFromRequest() {
    $request = $this->requestStack->getCurrentRequest();
    $query_params = $request->query;
    $category_filter_value = 'all';

    if ($query_params->has('category_id')) {
      $value = trim($query_params->get('category_id'));
      if (is_numeric($value) && Term::load((int) $value)) {
        $category_filter_value = (int) $value;
      }
    }

    return $category_filter_value;
  }

  /**
   * Gets filtered quotes data.
   *
   * @param string|int $category_filter_value
   *   The category filter value.
   *
   * @return array
   *   The filtered quotes data.
   */
  private function getFilteredQuotesData($category_filter_value) {
    $content_type = 'npx_quote';
    $author_field = 'field_quote_author';
    $category_field = 'field_quote_category';

    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', $content_type)
      ->condition('status', 1)
      ->accessCheck(FALSE)
      ->sort('created', 'DESC');

    if ($category_filter_value !== 'all') {
      $query->condition($category_field, $category_filter_value);
    }

    $nids = $query->execute();
    $quotes = [];

    if (!empty($nids)) {
      $nodes = Node::loadMultiple($nids);

      foreach ($nodes as $node) {
        $category_name = 'Bez kategorii';
        $display_terms = [];

        if (!$node->get($category_field)->isEmpty()) {
          $first_term = $node->get($category_field)->entity;
          if ($first_term) {
            $category_name = $first_term->getName();
          }
          foreach ($node->get($category_field)->referencedEntities() as $term) {
            $display_terms[] = $term->getName();
          }
        }

        $author = !$node->get($author_field)->isEmpty() ? $node->get($author_field)->value : '';

        $quotes[$category_name][] = [
          'body' => $node->get('body')->value,
          'author' => $author,
          'categories_display' => implode(', ', $display_terms),
        ];
      }
    }

    ksort($quotes);
    return $quotes;
  }

  /**
   * Generates PDF content.
   *
   * @param array $quotes_data
   *   The quotes data.
   *
   * @return string
   *   The HTML content for PDF.
   */
  private function generatePdfContent(array $quotes_data) {
    $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
    $html .= '<title>Zbiór cytatów i złotych myśli - 4grow.pl</title>';
    $html .= '<style>
      body {line-height: 1.5; font-size: 0.875rem; font-family: "DejaVu Sans", Arial, sans-serif;}
      .category-header { font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem; border-bottom: 1px solid #7f8c8d; padding-bottom: 5px; }
      .quote-item { padding: 0.5rem; }
      .quote-body { font-style: italic;}
      .quote-author { text-align: left; font-weight: bold; color: #555; }
      .quote-category-display { font-size: 1.2rem; color: #95a5a6; margin-top: 6px; }
      h1 { text-align: center; border-bottom: 2px solid #bdc3c7; padding-bottom: 0.5rem; margin-bottom: 1.5rem; }
    </style>';
    $html .= '</head><body>';
    $html .= '<h1>Zbiór cytatów i złotych myśli - 4grow.pl</h1>';

    foreach ($quotes_data as $category => $quotes) {
      $html .= '<div class="category-section">';
      $html .= '<h2 class="category-header">' . htmlspecialchars($category) . '</h2>';

      foreach ($quotes as $quote) {
        $html .= '<div class="quote-item">';
        $html .= '<div class="quote-body">"' . nl2br(htmlspecialchars(strip_tags($quote['body']))) . '"</div>';
        if (!empty($quote['author'])) {
          $html .= '<div class="quote-author">- ' . htmlspecialchars(strip_tags($quote['author'])) . '</div>';
        }
        $html .= '</div>';
      }

      $html .= '</div>';
    }

    $html .= '</body></html>';
    return $html;
  }

  /**
   * Sends quotes PDF email.
   *
   * @param string $html_content
   *   The HTML content for PDF.
   * @param string|int $category_filter_value
   *   The category filter value.
   * @param string $email
   *   The recipient email.
   * @param string $imie
   *   The recipient name.
   *
   * @return bool
   *   TRUE if email was sent successfully, FALSE otherwise.
   */
  private function sendQuotesPdfEmail($html_content, $category_filter_value, $email, $imie) {
    try {
      $pdf_data = PDFHelper::getPdfData($html_content);
      if (!$pdf_data) {
        return FALSE;
      }

      $filename = '4grow-cytaty-wszystkie.pdf';
      if ($category_filter_value !== 'all' && is_numeric($category_filter_value)) {
        $term = Term::load($category_filter_value);
        if ($term) {
          $term_name = $term->getName();
          $transliterated_name = $this->transliteration->transliterate($term_name, 'en', '_');
          // Dodatkowo oczyszczamy nazwę z niedozwolonych znaków
          $transliterated_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $transliterated_name);
          $transliterated_name = preg_replace('/_+/', '_', $transliterated_name); // Usuń wielokrotne podkreślenia
          $transliterated_name = trim($transliterated_name, '_'); // Usuń podkreślenia na początku i końcu
          $transliterated_name = strtolower($transliterated_name); // Zamień na małe litery

          $filename = '4grow-cytaty-' . $transliterated_name . '.pdf';
        }
      }

      $uri = 'temporary://' . $filename;
      $file = $this->fileRepository->writeData($pdf_data, $uri, FileSystemInterface::EXISTS_REPLACE);

      if (!$file) {
        return FALSE;
      }

      $absolute_file_path = $this->fileSystem->realpath($file->getFileUri());

      $params = [
        'subject' => $this->t('Twoje cytaty z 4grow.pl'),
        'body' => [
          '#markup' => $this->t('Cześć @imie! <br><br> Dziękujemy za Twoje zainteresowanie zbiorem cytatów z 4grow.pl. <br><br>W załączniku znajdziesz plik PDF, przygotowany specjalnie dla Ciebie.<br><br>Z pozdrowieniami,<br>Zespół 4grow.pl', ['@imie' => $imie]),
        ],
        'attachments' => [[
          'filepath' => $absolute_file_path,
          'filename' => $filename,
          'filemime' => 'application/pdf',
        ]],
      ];

      $result = $this->mailManager->mail(
        'npx_quotes',
        'quotes_pdf',
        $email,
        $this->currentUser->getPreferredLangcode(),
        $params,
        NULL,
        TRUE
      );

      return !empty($result['result']);
    }
    catch (\Exception $e) {
      $this->messenger->addError($this->t('Błąd podczas wysyłania PDF: @error', ['@error' => $e->getMessage()]));
      $this->loggerFactory->get('npx_pdf')->error('Błąd wysyłania PDF: @error', ['@error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
      return FALSE;
    }
  }

}
