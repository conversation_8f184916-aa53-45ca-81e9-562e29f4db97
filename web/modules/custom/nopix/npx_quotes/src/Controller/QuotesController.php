<?php

declare(strict_types=1);

namespace Drupal\npx_quotes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON>upal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Block\BlockManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Drupal\views\Views;

/**
 * Controller for displaying quotes page with separate blocks per category.
 */
class QuotesController extends ControllerBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The block manager.
   *
   * @var \Drupal\Core\Block\BlockManagerInterface
   */
  protected $blockManager;

  /**
   * Constructs a QuotesController object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Block\BlockManagerInterface $block_manager
   *   The block manager.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, BlockManagerInterface $block_manager) {
    $this->entityTypeManager = $entity_type_manager;
    $this->blockManager = $block_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('plugin.manager.block')
    );
  }

  /**
   * Returns a render-able array for the quotes page.
   */
  public function viewPage(Request $request): array {
    $categories = $this->getQuoteCategories();

    $build = [];
    $build['#attached']['library'][] = 'npx_quotes/quotes-load-all';

    foreach ($categories as $category) {
      $category_id = $category->id();

      $category_section = $this->buildCategorySection($category);
      if ($category_section) {
        $build['category_' . $category_id] = $category_section;

        $banner_section = $this->buildBannerSection($category);
        if ($banner_section) {
          $build['banner_' . $category_id] = $banner_section;
        }
      }
    }

    return $build;
  }

  /**
   * AJAX endpoint to load all remaining quotes from a category.
   */
  public function loadAllAjax(Request $request, string $category_id): JsonResponse {
    $skip = (int) $request->get('skip', 0);

    $quotes = $this->loadQuotesForCategory($category_id, $skip);

    if (empty($quotes)) {
      return new JsonResponse(['html' => '', 'count' => 0]);
    }

    $html = $this->generateQuotesHtml($quotes);

    return new JsonResponse(['html' => $html, 'count' => count($quotes)]);
  }

  /**
   * Get all quote categories.
   *
   * @return \Drupal\taxonomy\Entity\Term[]
   *   Array of taxonomy terms.
   */
  private function getQuoteCategories(): array {
    $terms = $this->entityTypeManager
      ->getStorage('taxonomy_term')
      ->loadByProperties(['vid' => 'quotes_categories']);

    return $terms;
  }

  /**
   * Build a complete category section with view and optional banner.
   */
  private function buildCategorySection($category): ?array {
    $category_id = $category->id();
    $category_name = $category->getName();

    $view = Views::getView('quotes_per_categories');
    if (!$view) {
      \Drupal::logger('npx_quotes')->error('Could not load quotes_per_categories view');
      return null;
    }

    $view->setDisplay('block_1');
    $view->setArguments([$category_id]);
    $view->execute();

    $rendered_view = $view->buildRenderable('block_1', [$category_id]);
    if (!$rendered_view) {
      \Drupal::logger('npx_quotes')->warning('View for category @id is empty', ['@id' => $category_id]);
      return null;
    }

    $total_quotes = $this->getQuotesCountForCategory($category_id);

    $build = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['quotes-category-section'],
        'data-category-id' => $category_id,
        'data-total-quotes' => $total_quotes,
        'id' => 'category_' . $category_id,
      ],
      'title' => [
        '#type' => 'html_tag',
        '#tag' => 'h3',
        '#value' => $category_name . ' - złote myśli, cytaty, aforyzmy, sentencje',
        '#attributes' => [
          'class' => ['quotes-category-title'],
        ],
      ],
      'view' => $rendered_view,
    ];

    if ($total_quotes > 5) {
      $build['more_button'] = $this->buildMoreButton();
    }

    return $build;
  }

  /**
   * Build banner section for a category if configured.
   */
  private function buildBannerSection($category): ?array {
    $category_name = $category->getName();
    $category_id = $category->id();

    $banners = $this->getBannerConfigs();

    if (!isset($banners[$category_name])) {
      return null;
    }

    $config = $banners[$category_name];

    return [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['quotes-banner', $config['type']],
      ],
      'content' => [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['banner-container'],
        ],
        'text_content' => [
          '#type' => 'container',
          '#attributes' => [
            'class' => ['banner-content'],
          ],
          'wrapper' => [
            '#type' => 'container',
            '#attributes' => [
              'class' => ['banner-text-and-button'],
            ],
            'title' => [
              '#type' => 'html_tag',
              '#tag' => 'h3',
              '#value' => $this->t($config['title']),
              '#attributes' => [
                'class' => ['banner-title'],
              ],
            ],
            'button_with_logo' => [
              '#type' => 'container',
              '#attributes' => [
                'class' => ['banner-button-with-logo'],
              ],
              'btn' => [
                '#type' => 'link',
                '#title' => $this->t($config['button_text']),
                '#url' => \Drupal\Core\Url::fromUri($config['url']),
                '#attributes' => [
                  'class' => ['btn', 'btn-banner', 'btn-banner-secondary'],
                  'target' => '_blank',
                ],
              ],
              'logo' => [
                '#type' => 'html_tag',
                '#tag' => 'img',
                '#attributes' => [
                  'src' => $config['logo_src'],
                  'alt' => $config['logo_alt'],
                  'class' => ['banner-logo-img'],
                ],
              ],
            ],
          ],
        ],
      ],
    ];
  }

  /**
   * Get banner configurations.
   */
  private function getBannerConfigs(): array {
    return [
      'Miłość, zakochanie, związki' => [
        'type' => 'banner-blue',
        'title' => 'Dowiedz się więcej o Aniołach Rozwoju',
        'button_text' => 'Dowiedz się',
        'url' => 'https://anioly-rozwoju.pl/',
        'logo_src' => '/themes/custom/bootstrap4grow/images/banners/anioly.png',
        'logo_alt' => 'Anioły Rozwoju',
      ],
      'Praca i kariera' => [
        'type' => 'banner-yellow',
        'title' => 'Dowiedz się więcej o naszych szkoleniach',
        'button_text' => 'Dowiedz się',
        'url' => 'https://4grow.pl/',
        'logo_src' => '/themes/custom/bootstrap4grow/images/banners/4grow.png',
        'logo_alt' => '4grow Logo',
      ],
    ];
  }

  /**
   * Build the "More" button for loading additional quotes.
   */
  private function buildMoreButton(): array {
    return [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['more-quotes-button-wrapper'],
      ],
      'button' => [
        '#type' => 'html_tag',
        '#tag' => 'button',
        '#attributes' => [
          'type' => 'button',
          'class' => ['btn', 'btn-quotes', 'load-more-quotes'],
        ],
        '#value' => 'Więcej',
      ],
    ];
  }

  /**
   * Get total count of quotes for a category.
   */
  private function getQuotesCountForCategory(string $category_id): int {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', 'npx_quote')
      ->condition('status', 1)
      ->condition('field_quote_category', $category_id)
      ->accessCheck(TRUE)
      ->count();

    return (int) $query->execute();
  }

  /**
   * Load quotes for a category with pagination.
   */
  private function loadQuotesForCategory(string $category_id, int $skip = 0, int $limit = 9999): array {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', 'npx_quote')
      ->condition('status', 1)
      ->condition('field_quote_category', $category_id)
      ->sort('created', 'DESC')
      ->range($skip, $limit)
      ->accessCheck(TRUE);

    $quote_ids = $query->execute();

    if (empty($quote_ids)) {
      return [];
    }

    return $this->entityTypeManager->getStorage('node')->loadMultiple($quote_ids);
  }

  /**
   * Generate HTML for quotes.
   */
  private function generateQuotesHtml(array $quotes): string {
    $html = '';

    foreach ($quotes as $quote) {
      $body = $quote->get('body')->value;
      $author = $quote->get('field_quote_author')->value;

      $html .= '<div class="views-row">';
      $html .= '<div class="views-field views-field-body"><div class="field-content">' . htmlspecialchars($body, ENT_QUOTES, 'UTF-8') . '</div></div>';

      if ($author) {
        $html .= '<div class="views-field views-field-field-quote-author"><div class="field-content">' . htmlspecialchars($author, ENT_QUOTES, 'UTF-8') . '</div></div>';
      }

      $html .= '</div>';
    }

    return $html;
  }

}
