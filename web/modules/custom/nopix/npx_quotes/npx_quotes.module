<?php

/**
 * @file
 * Contains npx_quotes module..
 */

use Drupal\Core\Render\Markup;

/**
 * Implements hook_mail().
 */
function npx_quotes_mail($key, &$message, $params) {
  switch ($key) {
    case 'quotes_pdf':
      $message['subject'] = $params['subject'];
      $message['body'] = $params['body'];

      if (!empty($params['attachments'])) {
        $attachment = [
          'filecontent' => $params['attachments']['filecontent'],
          'filename' => $params['attachments']['filename'],
          'filemime' => $params['attachments']['filemime'],
        ];
        $message['attachments'][] = $attachment;
      }
      break;
  }
}

/**
 * Implements hook_route_alter().
 */
function npx_quotes_route_alter(array &$routes) {
  foreach ($routes as $route_name => $route) {
    if ($route->getPath() === '/zlote-mysli-cytaty-aforyzmy-sentencje-rozwoju-osobistym-zawodowym') {
      if ($route_name === 'view.npx_quotes.page_1') {
        $route->setOption('weight', -10);
      }

      if (strpos($route_name, 'page_manager.') === 0) {
        $route->setOption('weight', 10);
      }
    }
  }
}

/**
 * Implements hook_form_alter().
 */
function npx_quotes_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  if ($form_id === "webform_submission_quotes_download_form_add_form") {
    $config = \Drupal::config('npx_freshmail.npxfreshmailsettings');
    $accept_text = $config->get('accept_text.value');

    $form['#attached']['drupalSettings']['npx_freshmail']['accept_text'] = $accept_text;
    $form['#attached']['library'][] = 'npx_quotes/npx_quotes_popover';
  }
}
