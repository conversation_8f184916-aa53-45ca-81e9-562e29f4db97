<?php

namespace Dr<PERSON>al\npx_contact\Entity;

use <PERSON><PERSON><PERSON>\Core\Entity\ContentEntityInterface;
use <PERSON><PERSON>al\Core\Entity\EntityChangedInterface;
use <PERSON><PERSON><PERSON>\user\EntityOwnerInterface;

/**
 * Provides an interface for defining Npx contact entity entities.
 *
 * @ingroup npx_contact
 */
interface NpxContactEntityInterface extends ContentEntityInterface, EntityChangedInterface, EntityOwnerInterface {

  // Add get/set methods for your configuration properties here.

  /**
   * Gets the Npx contact entity name.
   *
   * @return string
   *   Name of the Npx contact entity.
   */
  public function getName();

  /**
   * Sets the Npx contact entity name.
   *
   * @param string $name
   *   The Npx contact entity name.
   *
   * @return \Drupal\npx_contact\Entity\NpxContactEntityInterface
   *   The called Npx contact entity entity.
   */
  public function setName($name);

  /**
   * Gets the Npx contact entity creation timestamp.
   *
   * @return int
   *   Creation timestamp of the Npx contact entity.
   */
  public function getCreatedTime();

  /**
   * Sets the Npx contact entity creation timestamp.
   *
   * @param int $timestamp
   *   The Npx contact entity creation timestamp.
   *
   * @return \Drupal\npx_contact\Entity\NpxContactEntityInterface
   *   The called Npx contact entity entity.
   */
  public function setCreatedTime($timestamp);

  /**
   * Returns the Npx contact entity published status indicator.
   *
   * Unpublished Npx contact entity are only visible to restricted users.
   *
   * @return bool
   *   TRUE if the Npx contact entity is published.
   */
  public function isPublished();

  /**
   * Sets the published status of a Npx contact entity.
   *
   * @param bool $published
   *   TRUE to set this Npx contact entity to published, FALSE to set it to unpublished.
   *
   * @return \Drupal\npx_contact\Entity\NpxContactEntityInterface
   *   The called Npx contact entity entity.
   */
  public function setPublished($published);

}
