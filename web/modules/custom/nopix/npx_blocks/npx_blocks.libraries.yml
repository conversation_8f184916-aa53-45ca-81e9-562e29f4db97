npx_blocks.trainingblock:
  css:
    component:
      js/lib/monthly/monthly.css: {}
  js:
    js/ua-parser.min.js: {}
    js/jquery-scrolltofixed-min.js: { minified: true }
    js/jquery.npx.slidedates.js: {}
    js/lib/monthly/monthly.js: {}
    js/trainingblock.js: {}
  dependencies:
    - core/jquery

npx_blocks.floatingbeltblock:
  js:
    js/floatingbeltblock.js: {}
  dependencies:
    - core/jquery

npx_blocks.trainingplaceblock:
  css:
    component:
      js/lib/monthly/monthly.css: {}
  js:
    js/lib/monthly/monthly.js: {}
    js/trainingplaceblock.js: {}
  dependencies:
    - core/jquery
    - core/once

npx_blocks.trainingprogramblock:
  js:
    js/trainingprogramblock.js: {}
    js/traininglandingprogramblock.js: {}
  dependencies:
    - core/jquery
    - bootstrap4grow/bootstrap.tab

npx_blocks.landingprogramblock:
  js:
    js/traininglandingprogramblock.js: {}
    js/tile-sections.js: {}
    js/training-tile-expand.js: {}
  dependencies:
    - core/jquery
    - core/drupal
    - core/once

npx_blocks.coachingblock:
  js:
    js/ua-parser.min.js: { minified: true }
    js/jquery-scrolltofixed-min.js: { minified: true }
    js/coachingblock.js: {}
  dependencies:
    - core/jquery
    - core/once

npx_blocks.calendarblock:
  js:
    /libraries/fullcalendar/fullcalendar.min.js: { minified: true }
    /libraries/fullcalendar/locale/pl.js: {}
    js/calendarblock.js: {}
  css:
    component:
      /libraries/fullcalendar/fullcalendar.min.css: { minified: true }
  dependencies:
    - core/jquery
    - jquery_ui_tooltip/tooltip
    - fullcalendar_api/fullcalendar

npx_blocks.traininglistfrontblock:
  js:
    js/jquery.equalheights.min.js: { minified: true }
    js/traininglistfrontblock.js: {}
  dependencies:
    - core/jquery
    - core/drupalSettings
    - core/once

#npx_blocks.followupinfoblock:

#npx_blocks.breadcrumb_block:

npx_blocks.trainingtermtableblock:
  js:
    js/term-table-block.js: {}
    js/term-table-block-optimize.js: {}
  dependencies:
    - core/jquery
    - core/once

npx_blocks.innermenublock:
  js:
    js/inner-menu-block.js: {}
  dependencies:
    - core/jquery
    - core/once

    - core/jquery
    - core/once
