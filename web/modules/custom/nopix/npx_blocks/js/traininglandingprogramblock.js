/**
 * @file
 * Attaches the behaviors for the TrainingLandingProgramBlock.
 */
(function ($, Drupal, drupalSettings) {

  'use strict';

  Drupal.behaviors.npxProgramBlockLazyTabs = {
    attach: function (context) {
      // Nothing to do
    },
    npxOnScrollResize: function() {
      var $tabs = $('.npx-program-tabs-wrapper .n-tab-header-lazy');
      if($tabs && $tabs.length) {
        $tabs.each(function() {
          if ($(this).isInViewport()) {
            $(this).removeClass('n-tab-header-lazy');
            var image = $(this).attr('data-bg');
            if (image != '') {
              $(this).css('background-image', "url('" + image + "')");
              $(this).removeAttr('data-bg');
            }
          }
        });
      }
    }
  };
})(jQuery, Drupal, drupalSettings);
