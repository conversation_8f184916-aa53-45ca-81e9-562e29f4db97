(function ($, Drupal, drupalSettings, once) {
  'use strict';

  Drupal.behaviors.tileSections = {
    attach: function (context, settings) {
      once('tile-sections', '.show-more-btn', context).forEach(function (button) {
        var $button = $(button);
        var sectionIndex = $button.data('section-index');
        var $section = $('.section-' + sectionIndex);

        $button.on('click', function (e) {
          e.preventDefault();
          e.stopPropagation();

          var $hiddenTiles = $section.find('.hidden-tiles');
          var $showMoreContainer = $button.closest('.show-more-container');
          var $visibleTiles = $section.find('.visible-tiles');

          $section.find('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper').each(function() {
            var $tile = $(this);
            $tile.removeClass('expanded');
            $tile.css('height', '320px');
            $tile.css('transform', 'translateY(0px)');
          });

          // Reset section padding
          $section.css('padding-bottom', '60px');

          $visibleTiles.find('.hidden-tile-in-row').slideDown(300, function() {
            if (Drupal.behaviors.trainingTileExpand && Drupal.behaviors.trainingTileExpand.processTiles) {
              setTimeout(function() {
                Drupal.behaviors.trainingTileExpand.processTiles($visibleTiles[0]);
              }, 100);
            }
          });
          $visibleTiles.removeClass('has-hidden-in-row');

          if ($hiddenTiles.length > 0) {
            $hiddenTiles.slideDown(300, function () {
              $showMoreContainer.fadeOut(200);

              if (Drupal.behaviors.trainingTileExpand && Drupal.behaviors.trainingTileExpand.processTiles) {
                setTimeout(function() {
                  Drupal.behaviors.trainingTileExpand.processTiles($section[0]);
                }, 350);
              }
            });
          } else {
            $showMoreContainer.fadeOut(200);

            if (Drupal.behaviors.trainingTileExpand && Drupal.behaviors.trainingTileExpand.processTiles) {
              setTimeout(function() {
                Drupal.behaviors.trainingTileExpand.processTiles($section[0]);
              }, 350);
            }
          }
        });
      });
    }
  };

  function updateShowMoreButtonPositionForSection(section) {
    if (typeof window.calculateAndSetSectionHeight === 'function') {
      window.calculateAndSetSectionHeight(section);
    } else if (Drupal.behaviors.trainingTileExpand &&
               typeof Drupal.behaviors.trainingTileExpand.calculateAndSetSectionHeight === 'function') {
      Drupal.behaviors.trainingTileExpand.calculateAndSetSectionHeight(section);
    }
  }

})(jQuery, Drupal, drupalSettings, once);
