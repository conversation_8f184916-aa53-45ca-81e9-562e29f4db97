(function (Drupal, drupalSettings) {
  'use strict';

  Drupal.behaviors.trainingTileExpand = {
    attach: function (context) {
      this.processTiles(context);

      if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                if (node.classList && (node.classList.contains('npx-blocks-program-tab-wrapper') ||
                    node.classList.contains('npx-tile-textbox-wrapper') ||
                    node.classList.contains('npx-tile-opinion-wrapper'))) {
                  setTimeout(() => this.processTiles(node), 50);
                } else {
                  const tiles = node.querySelectorAll && node.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');
                  if (tiles && tiles.length > 0) {
                    setTimeout(() => this.processTiles(node), 50);
                  }
                }
              }
            });
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }

      let resizeTimeout;
      let previousWidth = window.innerWidth;

      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          const currentWidth = window.innerWidth;
          const wasMobile = previousWidth < 768;
          const isMobile = currentWidth < 768;

          if (wasMobile !== isMobile) {
            const sections = document.querySelectorAll('.npx-tile-section');
            sections.forEach(section => {
              const tiles = section.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');

              tiles.forEach(tile => {
                tile.classList.remove('expanded');
                tile.style.setProperty('height', '300px', 'important');
                tile.style.removeProperty('transform');
                tile.style.removeProperty('z-index');
              });

              section.style.setProperty('padding-bottom', '60px', 'important');
            });
          } else {
            const sections = document.querySelectorAll('.npx-tile-section');
            sections.forEach(section => {
              const tiles = section.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');
              tiles.forEach(tile => {
                if (window.innerWidth < 768) {
                  tile.style.removeProperty('transform');
                }
              });

              calculateAndSetSectionHeight(section);
            });
          }

          previousWidth = currentWidth;
        }, 250);
      });
    },

    processTiles: function(context) {
      const tiles = context.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');

      tiles.forEach((tile) => {
        tile.removeEventListener('mouseenter', tile._hoverEnter);
        tile.removeEventListener('mouseleave', tile._hoverLeave);

        tile._hoverEnter = function() {
          const currentTransform = this.style.transform || 'translateY(0px) scale(1)';
          const currentY = currentTransform.includes('translateY(') ? currentTransform.match(/translateY\(([^)]+)\)/)[1] : '0px';
          this.style.transform = `translateY(${currentY}) scale(1.02)`;
          this.style.zIndex = '15';
        };
        tile._hoverLeave = function() {
          const currentTransform = this.style.transform || 'translateY(0px) scale(1)';
          const currentY = currentTransform.includes('translateY(') ? currentTransform.match(/translateY\(([^)]+)\)/)[1] : '0px';
          this.style.transform = `translateY(${currentY}) scale(1)`;
          this.style.zIndex = '1';
        };

        tile.addEventListener('mouseenter', tile._hoverEnter);
        tile.addEventListener('mouseleave', tile._hoverLeave);
      });

      const unprocessedTiles = Array.from(tiles).filter(tile => !tile.hasAttribute('data-processed'));

      unprocessedTiles.forEach((tile) => {
        tile.setAttribute('data-processed', 'true');
        setTimeout(function() {
          let content;
          if (tile.classList.contains('npx-tile-textbox-wrapper')) {
            content = tile.querySelector('.npx-tile-textbox-content');
          } else if (tile.classList.contains('npx-tile-opinion-wrapper')) {
            content = tile.querySelector('.tile-opinion-content');
          } else {
            content = tile.querySelector('.npx-training-form-tab-content');
          }

          if (!content) {
            return;
          }

          const originalHeight = tile.style.height;
          const originalContentHeight = content.style.height;

          let realContentHeight;

          if (tile.classList.contains('npx-blocks-program-tab-wrapper') && !tile.classList.contains('npx-tile-textbox-wrapper') && !tile.classList.contains('npx-tile-opinion-wrapper')) {
            const tempDiv = document.createElement('div');
            tempDiv.style.position = 'absolute';
            tempDiv.style.visibility = 'hidden';
            tempDiv.style.width = content.offsetWidth + 'px';
            tempDiv.style.fontSize = window.getComputedStyle(content).fontSize;
            tempDiv.style.lineHeight = window.getComputedStyle(content).lineHeight;
            tempDiv.style.fontFamily = window.getComputedStyle(content).fontFamily;
            tempDiv.innerHTML = content.innerHTML;
            document.body.appendChild(tempDiv);
            realContentHeight = tempDiv.offsetHeight;
            document.body.removeChild(tempDiv);
          } else {
            tile.style.setProperty('height', 'auto', 'important');
            content.style.setProperty('height', 'auto', 'important');
            realContentHeight = content.scrollHeight;
            tile.style.setProperty('height', originalHeight || '300px', 'important');
            content.style.setProperty('height', originalContentHeight || '', 'important');
          }

          const hasText = content.textContent.trim().length > 0;
          const availableHeight = 300;
          const tolerance = 5;
          const shouldAddLink = hasText && realContentHeight > (availableHeight + tolerance);

          const existingLinks = tile.querySelectorAll('.show-more-link');
          existingLinks.forEach(link => link.remove());

          if (!shouldAddLink) {
            tile.classList.remove('has-overflow');
            content.classList.remove('has-overflow');
          }

          if (shouldAddLink) {
            if (tile.classList.contains('npx-tile-textbox-wrapper') || tile.classList.contains('npx-tile-opinion-wrapper')) {
              tile.classList.add('has-overflow');
            } else {
              content.classList.add('has-overflow');
            }

            const showMoreLink = document.createElement('a');
            showMoreLink.href = '#';
            showMoreLink.className = 'show-more-link';
            showMoreLink.textContent = 'Pokaż więcej';

            if (tile.classList.contains('npx-tile-textbox-wrapper') || tile.classList.contains('npx-tile-opinion-wrapper')) {
              tile.appendChild(showMoreLink);
            } else {
              content.appendChild(showMoreLink);
            }

            showMoreLink.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();

              if (tile.classList.contains('expanded')) {
                tile.classList.remove('expanded');
                tile.style.setProperty('height', '300px', 'important');
                resetAllTiles(tile);
                resetSection(tile);
              } else {
                tile.classList.add('expanded');
                tile.style.setProperty('height', 'auto', 'important');
                setTimeout(() => {
                  moveTilesBelow(tile);
                  expandSection(tile);
                }, 50);
              }
            });
          }

          if (tile.classList.contains('npx-blocks-program-tab-wrapper')) {
            const parentLink = tile.closest('a[data-npx-nid]');
            if (parentLink) {
              content.style.cursor = 'pointer';
              content.addEventListener('click', function(e) {
                const isShowMoreLink = e.target.closest('.show-more-link');
                const isCTALink = e.target.closest('.tile-training-cta a');

                if (!isShowMoreLink && !isCTALink) {
                  parentLink.click();
                }
              });
            }


          }
        }, 50);
      });
    }
  };

  function moveTilesBelow(expandedTile) {
    if (window.innerWidth < 768) {
      return;
    }

    const section = expandedTile.closest('.npx-tile-section');
    const sectionTiles = section.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');
    const expandedTileHeight = expandedTile.offsetHeight;
    const expandedTileRect = expandedTile.getBoundingClientRect();
    const expandedTileTop = expandedTileRect.top + window.scrollY;
    const expandedTileLeft = expandedTileRect.left;

    sectionTiles.forEach(tile => {
      if (tile === expandedTile) return;

      const tileRect = tile.getBoundingClientRect();
      const tileTop = tileRect.top + window.scrollY;
      const tileLeft = tileRect.left;
      const leftDifference = Math.abs(expandedTileLeft - tileLeft);

      if (tileTop > expandedTileTop && leftDifference < 100) {
        const currentTransform = tile.style.transform || 'translateY(0px) scale(1)';
        const currentY = parseInt(currentTransform.match(/translateY\(([^)]+)\)/) ? currentTransform.match(/translateY\(([^)]+)\)/)[1] : 0);
        const currentScale = currentTransform.includes('scale(') ? currentTransform.match(/scale\(([^)]+)\)/)[1] : '1';
        const newY = currentY + (expandedTileHeight - 300);

        tile.style.setProperty('transform', `translateY(${newY}px) scale(${currentScale})`, 'important');
        tile.style.setProperty('transition', 'transform 0.3s ease', 'important');
      }
    });
  }

  function resetAllTiles(expandedTile) {
    if (window.innerWidth < 768) {
      return;
    }

    const section = expandedTile.closest('.npx-tile-section');
    const sectionTiles = section.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');

    sectionTiles.forEach(tile => {
      const currentTransform = tile.style.transform;
      if (currentTransform && currentTransform.includes('translateY') && !currentTransform.includes('translateY(0px)')) {
        const currentScale = currentTransform.includes('scale(') ? currentTransform.match(/scale\(([^)]+)\)/)[1] : '1';
        tile.style.setProperty('transform', `translateY(0px) scale(${currentScale})`, 'important');
        tile.style.setProperty('transition', 'transform 0.3s ease', 'important');
      }
    });
  }

  function expandSection(expandedTile) {
    const section = expandedTile.closest('.npx-tile-section');

    if (window.innerWidth < 768) {
      section.style.setProperty('padding-bottom', '60px', 'important');
      return;
    }

    setTimeout(() => {
      calculateAndSetSectionHeight(section);
    }, 50);
  }

  function resetSection(expandedTile) {
    const section = expandedTile.closest('.npx-tile-section');

    if (window.innerWidth < 768) {
      section.style.setProperty('padding-bottom', '60px', 'important');
      return;
    }

    setTimeout(() => {
      calculateAndSetSectionHeight(section);
    }, 50);
  }

  function calculateAndSetSectionHeight(section) {
    // Na mobilnych nie obliczamy nic - kafelki układają się normalnie jeden pod drugim
    if (window.innerWidth < 768) {
      section.style.setProperty('padding-bottom', '60px', 'important');
      return;
    }

    const tiles = section.querySelectorAll('.npx-blocks-program-tab-wrapper, .npx-tile-textbox-wrapper, .npx-tile-opinion-wrapper');

    if (tiles.length === 0) {
      section.style.setProperty('padding-bottom', '60px', 'important');
      return;
    }

    const leftPositions = [];
    tiles.forEach(tile => {
      const rect = tile.getBoundingClientRect();
      leftPositions.push(rect.left);
    });

    leftPositions.sort((a, b) => a - b);
    const uniqueColumns = [];
    const tolerance = 100;

    leftPositions.forEach(left => {
      const existingColumn = uniqueColumns.find(col => Math.abs(col - left) < tolerance);
      if (!existingColumn) {
        uniqueColumns.push(left);
      }
    });

    const columns = {};
    uniqueColumns.forEach(colLeft => {
      columns[colLeft] = [];
    });

    tiles.forEach(tile => {
      const rect = tile.getBoundingClientRect();
      const tileLeft = rect.left;

      let closestColumn = uniqueColumns[0];
      let minDistance = Math.abs(tileLeft - closestColumn);

      uniqueColumns.forEach(colLeft => {
        const distance = Math.abs(tileLeft - colLeft);
        if (distance < minDistance) {
          minDistance = distance;
          closestColumn = colLeft;
        }
      });

      columns[closestColumn].push(tile);
    });

    let maxColumnHeight = 0;
    Object.entries(columns).forEach(([colLeft, columnTiles]) => {
      let columnHeight = 0;
      columnTiles.forEach(tile => {
        const tileHeight = tile.classList.contains('expanded') ? tile.offsetHeight : 300;
        columnHeight += tileHeight;
      });
      maxColumnHeight = Math.max(maxColumnHeight, columnHeight);
    });

    const actualColumnsCount = uniqueColumns.length;
    const rowsCount = Math.ceil(tiles.length / actualColumnsCount);
    const standardHeight = rowsCount * 300;

    const heightDifference = maxColumnHeight - standardHeight;
    const currentPadding = parseInt(section.style.paddingBottom) || 60;
    const basePadding = 60;
    const currentExpansion = currentPadding - basePadding;

    if (heightDifference > 0) {
      const newPadding = basePadding + heightDifference;
      section.style.setProperty('padding-bottom', newPadding + 'px', 'important');
      section.style.setProperty('transition', 'padding-bottom 0.3s ease', 'important');
    } else {
      section.style.setProperty('padding-bottom', basePadding + 'px', 'important');
      section.style.setProperty('transition', 'padding-bottom 0.3s ease', 'important');
    }

    // Aktualizujemy pozycję przycisku "pokaż więcej"
    updateShowMoreButtonPosition(section);
  }

  function updateShowMoreButtonPosition(section) {
    const showMoreContainer = section.querySelector('.show-more-container');
    if (!showMoreContainer) return;

    showMoreContainer.style.removeProperty('top');
    showMoreContainer.style.setProperty('bottom', '0', 'important');
  }

  window.calculateAndSetSectionHeight = calculateAndSetSectionHeight;
  window.updateShowMoreButtonPosition = updateShowMoreButtonPosition;
  Drupal.behaviors.trainingTileExpand.calculateAndSetSectionHeight = calculateAndSetSectionHeight;
  Drupal.behaviors.trainingTileExpand.updateShowMoreButtonPosition = updateShowMoreButtonPosition;
})(Drupal, drupalSettings);
