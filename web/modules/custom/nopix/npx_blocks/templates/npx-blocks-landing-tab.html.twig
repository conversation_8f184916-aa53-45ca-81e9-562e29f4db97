{#*
/**
 * Available variables:
 * - days
 * - title
 * - content
 * - image
 * - image_alt
 * - image_title
 * - webinar
 * - online
 * - stationary_only
 * - closed
 * - labels
 * - price
 * - dates
 * - city
 * - room
 * - date_start
 * - date_end
 * - has_links
 * - rating
 * - rating_count
 * - has_available_dates
 * - sample_dates

**/
#}

<div class="npx-blocks-program-tab-wrapper d-flex flex-column flex-md-row" {% if rating and rating_count and not closed and not has_links %} vocab="https://schema.org/" typeof="Product"{% endif %}>
  <div class="n-tab-header npx-training-form-tab-header">
    {% if image != '' %}
      <img {% if rating and rating_count and not closed and not has_links %} property="image"{% endif %} class="b-lazy" data-src="{{image}}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ image_alt }}" title="{{ image_title}}"/>
    {% endif %}
    {% if rating and rating_count and not closed and not has_links %}
      <div property="aggregateRating" typeof="AggregateRating" class="d-none">
        <span property="ratingValue">{{ rating }}</span>
        <span property="bestRating">6</span>
        <span property="ratingCount">{{ rating_count }}</span>
      </div>
    {% endif %}
    <div class="npx-training-form-tab-header-inner text-white">
      <div class="npx-training-form-tab-header-top">
{#        <div class="npx-training-form-tab-header-top-inner d-flex justify-content-between">#}
{#          <div class="npx-training-form-tab-header-hours fw-bold">{{ days }}</div>#}
{#        </div>#}
        <div class="npx-training-form-tab-header-title">
          <h3{% if rating and rating_count and not closed and not has_links %} property="name"{% endif %}>{{ title }}</h3>
        </div>
        <div class="npx-training-form-tab-header-type text-start fw-bold">
          {% set priority = ['stationary', 'online', 'online_live'] %}
          {% set active_flags = [] %}
          {% for key in priority %}
            {% if labels[key] is defined %}
              {% set active_flags = active_flags|merge([{ 'key': key, 'label': labels[key] }]) %}
            {% endif %}
          {% endfor %}

          {% for flag in active_flags|slice(0, 2) %}
            {% if flag.key == "online" %}
              <span class="online text-white text-uppercase">{{ flag.label }}</span>
            {% elseif flag.key == "online_live" %}
              <span class="online text-white text-uppercase">{{ flag.label }}</span>
            {% elseif flag.key == "stationary" %}
              <span class="stationary text-black text-uppercase">{{ flag.label }}</span>
            {% endif %}
          {% endfor %}
        </div>
      </div>
      <div class="npx-training-form-tab-header-info">
        {% if closed %}
          <div class="npx-training-form-tab-header-info-row d-flex justify-content-between align-items-start flex-nowrap long-text">
            <span class="t3">Szkolenie na zamówienie</span>
            <span class="t3b text-end">&nbsp;</span>
          </div>
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap">
            <span class="t3">Lokalizacja:</span>
            <i class="icon-location "></i>
            <span class="t3b">Dowolna</span>
          </div>
          <div class="text-start">
            <a href="mailto:<EMAIL>?subject=Zapytanie ofertowe – szkolenie {{ title|e }}&body=Dzień dobry, Prosimy o przesłanie oferty na tytułowe szkolenie zgodnie z poniższą specyfikacją:" class="training-inquiry-link t3b">Złóż zapytanie <i class="icon-envelope"></i></a>
          </div>
        {% elseif not has_available_dates %}
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap">
            <span class="t3">Lokalizacja:</span>
            <i class="icon-location "></i>
            <span class="t3b">Warszawa</span>
          </div>
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap mt-1 long-text">
            <span class="t3">Terminy od:</span>
            <i class="icon-calendar "></i>
            <span class="t3b">wkrótce podamy</span>
          </div>
          {% if price %}
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap mt-1">
            <span class="t3">Cena od:</span>
            <i class="icon-price "></i>
            <span class="t3b">{{ price }}zł netto</span>
          </div>
          {% endif %}
          <div class="npx-training-form-tab-header-info-note t3 mt-1">Szkolenia dedykowane / zamknięte:</div>
          <div class="text-start">
            <a href="mailto:<EMAIL>?subject=Zapytanie ofertowe – szkolenie {{ title|e }}&body=Dzień dobry, Prosimy o przesłanie oferty na tytułowe szkolenie zgodnie z poniższą specyfikacją:" class="training-inquiry-link t3b">Złóż zapytanie <i class="icon-envelope"></i></a>
          </div>
        {% else %}
          {% if city %}
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap">
            <span class="t3">Lokalizacja:</span>
            <i class="icon-location "></i>
            <span class="t3b">{{ city }}</span>
          </div>
          {% endif %}
          {% if dates %}
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap mt-1">
            <span class="t3">Terminy od:</span>
            <i class="icon-calendar "></i>
            <span class="t3b">{{ dates }}</span>
          </div>
          {% endif %}
          {% if price %}
          <div class="npx-training-form-tab-header-info-row d-flex align-items-center flex-nowrap mt-1">
            <span class="t3">Cena od:</span>
            <i class="icon-price "></i>
            <span class="t3b">{{ price }}zł netto</span>
          </div>
          {% endif %}
          <div class="npx-training-form-tab-header-info-note t3 mt-1">Szkolenia dedykowane / zamknięte:</div>
          <div class="text-start">
            <a href="mailto:<EMAIL>?subject=Zapytanie ofertowe – szkolenie {{ title|e }}&body=Dzień dobry, Prosimy o przesłanie oferty na tytułowe szkolenie zgodnie z poniższą specyfikacją:" class="training-inquiry-link t3b">Złóż zapytanie <i class="icon-envelope"></i></a>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  {# Content section - right side #}
  <div class="npx-training-form-tab-content p-3 fw-normal flex-grow-1">
    {{ content|striptags('<p><br><strong><em><ul><li><ol><div><span>')|raw }}
    {% if cta_url %}
      <div class="tile-training-cta mt-3">
        <a class="btn btn-link p-0" href="{{ cta_url }}">
          Poznaj szczegóły
        </a>
      </div>
    {% endif %}
  </div>
</div>
{% if city and not closed %}
  <div class="d-none" typeof="Event" vocab="https://schema.org/">
    <meta property="name" content="{{ title }}" />
    <meta property="description" content="{{ content|striptags }}" />
    <meta property="startDate" content="{{ date_start }}" />
    <meta property="endDate" content="{{ date_end }}" />
    <span property="location" typeof="Place">
      <span property="address" typeof="PostalAddress">
        <meta property="addressLocality" content="Warszawa" />
        <meta property="streetAddress" content="{{ room|striptags }}" />
      </span>
      <meta property="name" content="4GROW" />
    </span>
  </div>
{% endif %}
