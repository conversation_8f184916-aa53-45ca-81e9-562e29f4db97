<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Render\Markup;
use <PERSON><PERSON>al\Core\Routing\CurrentRouteMatch;
use <PERSON><PERSON>al\image\Entity\ImageStyle;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\npx_training_form\NpxCalculatorHelper;
use Drupal\npx_training_form\NpxDiscountHelper;
use Drupal\npx_blocks\TrainingDateHelper;
use Drupal\Core\Url;

/**
 * Provides a 'LandingTabBlock' block.
 *
 * @Block(
 *  id = "npx_landing_tab_block",
 *  admin_label = @Translation("Landing page tab block"),
 * )
 */
class LandingTabBlock extends BlockBase implements ContainerFactoryPluginInterface {

  const DATE_FORMAT = 'Y-m-d';

  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * Constructs a new BreadcrumbBlock object.
   */
  public function __construct(
    array $configuration,
          $plugin_id,
          $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    CurrentRouteMatch $current_route_match
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->currentRouteMatch = $current_route_match;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('current_route_match')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];

    if(!$node = $this->currentRouteMatch->getParameter('node')) {
      return $build;
    }

    if(!$node instanceof NodeInterface) {
      return $build;
    }

    $landing_node = $node;
    $section_title = 'Kafelki';

    if ($node->hasField('field_tiles_paragraphs') && !$node->get('field_tiles_paragraphs')->isEmpty()) {
      return $this->buildFromParagraphs($node);
    }

    return $this->buildLegacyTabs($node);
  }

  /**
   * Buduje kafelki na podstawie paragrafów
   */
  private function buildFromParagraphs($node) {
    $build = [];

    $build['sections_container'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['npx-tile-sections-container'],
      ],
    ];

    $sections = [];
    $current_section = null;
    $section_index = 0;

    foreach ($node->get('field_tiles_paragraphs') as $paragraph_item) {
      $paragraph = $paragraph_item->entity;

      if (!$paragraph) {
        continue;
      }

      if ($paragraph->bundle() == 'tile_header') {
        if ($current_section !== null) {
          $sections[] = $current_section;
        }

        $show_more_limit = 6;
        if ($paragraph->hasField('field_show_more') && !$paragraph->get('field_show_more')->isEmpty()) {
          $show_more_limit = (int) $paragraph->get('field_show_more')->value;
        }

        $current_section = [
          'header' => $this->buildHeaderTile($paragraph),
          'tiles' => [],
          'section_index' => $section_index++,
          'show_more_limit' => $show_more_limit
        ];
      } else {
        if ($current_section !== null) {
          $tile_build = $this->buildTileFromParagraph($paragraph);
          if (!empty($tile_build)) {
            if ($paragraph->bundle() == 'tile_training') {
              $training_node = $paragraph->get('field_training_reference')->entity;
              if ($training_node) {
                $alias = \Drupal::service('path_alias.manager')->getAliasByPath('/node/' . $training_node->id());
                $title = $training_node->hasField('field_top_tytul') &&
                !$training_node->get('field_top_tytul')->isEmpty() &&
                count($training_node->get('field_top_tytul')->referencedEntities()) > 0 &&
                $training_node->get('field_top_tytul')->referencedEntities()[0]->hasField('field_ts_tytul')
                  ? $training_node->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value
                  : $training_node->getTitle();

                $tab_rendered = \Drupal::service('renderer')->render($tile_build);
                $markup = Markup::create('<a class="npx-no-autolink" href="' . $alias . '" aria-label="' . $title . '" data-npx-nid="' . $training_node->id() . '">' . $tab_rendered . '</a>');

                $wrapped_tile = [
                  '#markup' => $markup,
                  '#wrapper_attributes' => [
                    'class' => ['nav-item'],
                  ],
                ];

                $current_section['tiles'][] = $wrapped_tile;
              }
            } else {
              $wrapped_tile = [
                '#markup' => \Drupal::service('renderer')->render($tile_build),
                '#wrapper_attributes' => [
                  'class' => ['nav-item'],
                ],
              ];
              $current_section['tiles'][] = $wrapped_tile;
            }
          }
        }
      }
    }

    if ($current_section !== null) {
      $sections[] = $current_section;
    }

    foreach ($sections as $section) {
      $section_build = [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['npx-tile-section', 'section-' . $section['section_index']],
        ],
      ];

      $section_build['header'] = $section['header'];

      $all_tiles = $section['tiles'];
      $show_more_limit = $section['show_more_limit'] ?? 6;
      $visible_tiles = array_slice($all_tiles, 0, $show_more_limit);
      $hidden_tiles = array_slice($all_tiles, $show_more_limit);
      $has_hidden_tiles_originally = !empty($hidden_tiles);

      $section_build['visible_tiles'] = [
        '#type' => 'container',
        '#attributes' => ['class' => ['visible-tiles']],
      ];

      $this->addTilesToContainer($section_build['visible_tiles'], $visible_tiles);

      if (!empty($hidden_tiles)) {
        $section_build['visible_tiles']['#attributes']['class'][] = 'has-hidden-in-row';

        $row_keys = array_keys($section_build['visible_tiles']);
        $last_row_key = end($row_keys);

        if (isset($section_build['visible_tiles'][$last_row_key])) {
          $tiles_in_last_row = 0;
          foreach ($section_build['visible_tiles'][$last_row_key] as $key => $tile) {
            if (strpos($key, 'tile_') === 0) {
              $tiles_in_last_row++;
            }
          }

          if ($tiles_in_last_row < 3) {
            $tiles_to_add = 3 - $tiles_in_last_row;
            $tiles_to_add = min($tiles_to_add, count($hidden_tiles));

            for ($i = 0; $i < $tiles_to_add; $i++) {
              $section_build['visible_tiles'][$last_row_key]['hidden_tile_' . $i] = [
                '#type' => 'container',
                '#attributes' => [
                  'class' => ['col-md-4', 'mb-3', 'hidden-tile-in-row'],
                  'style' => 'display: none;',
                ],
                'content' => $hidden_tiles[$i],
              ];
            }

            $hidden_tiles = array_slice($hidden_tiles, $tiles_to_add);
          }
        }
      }

      $remaining_hidden_tiles = $hidden_tiles;
      if (!empty($remaining_hidden_tiles)) {
        $section_build['hidden_tiles'] = [
          '#type' => 'container',
          '#attributes' => [
            'class' => ['hidden-tiles'],
            'style' => 'display: none;',
          ],
        ];
        $this->addTilesToContainer($section_build['hidden_tiles'], $remaining_hidden_tiles);
      }

      if ($has_hidden_tiles_originally) {
        $section_build['show_more'] = [
          '#type' => 'container',
          '#attributes' => ['class' => ['show-more-container', 'text-center', 'mt-4']],
          'button' => [
            '#type' => 'html_tag',
            '#tag' => 'button',
            '#value' => 'Pokaż więcej',
            '#attributes' => [
              'class' => ['btn', 'btn-outline-primary', 'show-more-btn', 'npx-program-button'],
              'data-section-index' => $section['section_index'],
            ],
          ],
        ];
      }

      $build['sections_container']['section_' . $section['section_index']] = $section_build;
    }

    $build['#attached'] = [
      'library' => [
        'npx_blocks/npx_blocks.landingprogramblock',
      ]
    ];

    $build['#cache']['max-age'] = 0;

    return $build;
  }

  private function addTilesToContainer(&$container, $tiles) {
    $row_index = 0;
    $current_row = [];

    foreach ($tiles as $tile) {
      $current_row[] = $tile;

      if (count($current_row) == 3 || $tile === end($tiles)) {
        $row_wrapper = [
          '#type' => 'container',
          '#attributes' => ['class' => ['tile-row', 'row', 'mb-4']],
        ];

        foreach ($current_row as $tile_in_row) {
          $row_wrapper['tile_' . $row_index] = [
            '#type' => 'container',
            '#attributes' => ['class' => ['col-md-4', 'mb-3']],
            'content' => $tile_in_row,
          ];
          $row_index++;
        }

        $container['row_' . $row_index] = $row_wrapper;
        $current_row = [];
      }
    }
  }

  /**
   * Adds a group of tiles with proper ul li wrapper
   */
  private function addTileGroup(&$build, $tiles, $group_index, &$weight) {
    $group_wrapper = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['npx-program-tabs-wrapper'],
      ],
    ];

    $group_wrapper['npx_tabs'] = [
      '#theme' => 'item_list',
      '#list_type' => 'ul',
      '#items' => $tiles,
      '#attributes' => [
        'class' => ['nav nav-tabs d-flex flex-column justify-content-center flex-md-row flex-wrap']
      ],
    ];

    $build['tiles_container']['group_' . $group_index] = $group_wrapper;
    $build['tiles_container']['group_' . $group_index]['#weight'] = $weight;
    $weight++;
  }

  /**
   * Buduje pojedynczy kafelek na podstawie paragrafu
   */
  private function buildTileFromParagraph($paragraph) {
    $build = [];

    switch ($paragraph->bundle()) {
      case 'tile_header':
        $build = $this->buildHeaderTile($paragraph);
        break;

      case 'tile_training':
        $training_node = $paragraph->get('field_training_reference')->entity;
        return $training_node ? $this->buildTrainingTileNode($training_node, $paragraph) : [];

      case 'tile_text_box':
        $build = $this->buildTextBoxTile($paragraph);
        break;

      case 'tile_opinion':
        $build = $this->buildOpinionTile($paragraph);
        break;
    }

    return $build;
  }

  /**
   * Buduje kafelek nagłówka
   */
  private function buildHeaderTile($paragraph) {
    $title = $paragraph->get('field_header_title')->value;
    $content = $paragraph->get('field_header_content')->value ?? '';

    return [
      '#theme' => 'npx_blocks_tile_header',
      '#title' => $title,
      '#content' => $content,
      '#attributes' => [
        'class' => ['npx-tile-header', 'mb-4'],
      ],
    ];
  }

  private function buildTrainingTileNode(NodeInterface $training_node, $paragraph = null) {
    $image_source = '';
    $image_alt = '';
    $image_title = '';

    // Obrazek
    if ($training_node->hasField('field_npxtraining_block_img') && !$training_node->get('field_npxtraining_block_img')->isEmpty()) {
      $image = $training_node->get('field_npxtraining_block_img')->entity;
      if ($image) {
        $image_uri = $image->getFileUri();
        $style = ImageStyle::load('landing_program_tab_image');
        if ($style) {
          $image_source = $style->buildUrl($image_uri);
        } else {
          $image_source = \Drupal::service('file_url_generator')->generateString($image_uri);
        }
        $image_alt = $training_node->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
        $image_title = $training_node->get('field_npxtraining_block_img')->first()->get('title')->getValue();
      }
    }

    $content = '';
    if ($training_node->hasField('field_npxtraining_program_lp') && !$training_node->get('field_npxtraining_program_lp')->isEmpty()) {
      $content = $training_node->get('field_npxtraining_program_lp')->value;
    } elseif ($training_node->hasField('field_program_szkolenia_wstep') && !$training_node->get('field_program_szkolenia_wstep')->isEmpty()) {
      $content = $training_node->get('field_program_szkolenia_wstep')->value;
    }

    $has_links = 0;
    if (!empty($content) && strpos($content, '<a') !== false) {
      $has_links = 1;
    }

    // Najbliższa data
    $closestDate = $this->getClosestFutureDate($training_node);

    // Check if training has available dates
    $has_available_dates = $closestDate !== null;

    // URL node
    $node_url = Url::fromRoute('entity.node.canonical', ['node' => $training_node->id()], ['absolute' => FALSE])->toString();

    // Tytuł (ew. przez pole top tytul)
    $title = '';
    if (
      $training_node->hasField('field_top_tytul') &&
      !$training_node->get('field_top_tytul')->isEmpty() &&
      count($training_node->get('field_top_tytul')->referencedEntities()) > 0 &&
      $training_node->get('field_top_tytul')->referencedEntities()[0]->hasField('field_ts_tytul')
    ) {
      $title = $training_node->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;
    } else {
      $title = $training_node->getTitle();
    }

    // CTA URL z paragrafu (jeśli istnieje)
    $cta_url = '';
    if ($paragraph && $paragraph->hasField('field_cta_link') && !$paragraph->get('field_cta_link')->isEmpty()) {
      $link_uri = $paragraph->get('field_cta_link')->uri;
      // Konwertuj internal:/dupa na /dupa
      $cta_url = Url::fromUri($link_uri)->toString();
    }

    // Use the landing tab template as intended
    return [
      '#theme' => 'npx_blocks_landing_tab',
      '#days' => $this->getDays($training_node, $closestDate),
      '#title' => $training_node->get('field_npxtraining_tytul_formalny')->value,
      '#content' => $content,
      '#image' => $image_source,
      '#image_alt' => $image_alt,
      '#image_title' => $image_title,
      '#webinar' => $training_node->hasField('field_npxtraining_webinar') ? $training_node->get('field_npxtraining_webinar')->value : NULL,
      '#online' => $training_node->hasField('field_npxtraining_online') ? $training_node->get('field_npxtraining_online')->value : NULL,
      '#online_live' => $training_node->hasField('field_online_live') ? $training_node->get('field_online_live')->value : NULL,
      '#closed' => $training_node->hasField('field_npxtraining_closed') ? $training_node->get('field_npxtraining_closed')->value : NULL,
      '#stationary_only' => $training_node->hasField('field_npxtraining_stationary') ? $training_node->get('field_npxtraining_stationary')->value : NULL,
      '#labels' => $this->getTrainingTypeLabels($training_node),
      '#price' => $has_available_dates ? $this->getPriceForTenPeople($training_node) : $this->getPrice($training_node),
      '#dates' => $this->getDatesDisplay($closestDate),
      '#city' => $this->getCity($closestDate),
      '#room' => $closestDate && $closestDate->hasField('field_npxtraining_date_room') && !$closestDate->get('field_npxtraining_date_room')->isEmpty()
        ? $closestDate->field_npxtraining_date_room->entity->field_room_page_desc->value : '',
      '#date_start' => $closestDate && !$closestDate->get('field_npxtraining_date_start')->isEmpty()
        ? $closestDate->field_npxtraining_date_start[0]->value : '',
      '#date_end' => $closestDate && !$closestDate->get('field_npxtraining_date_end')->isEmpty()
        ? $closestDate->field_npxtraining_date_end[0]->value : '',
      '#has_links' => $has_links,
      '#rating' => $training_node->hasField('field_aggregate_rating') ? $training_node->get('field_aggregate_rating')->value : NULL,
      '#rating_count' => $training_node->hasField('field_aggregate_rating_count') ? $training_node->get('field_aggregate_rating_count')->value : NULL,
      '#node_url' => $node_url,
      '#has_available_dates' => $has_available_dates,
      '#sample_dates' => $has_available_dates ? null : $this->getSampleDates(),
      '#training_id' => $training_node->id(),
      '#cta_url' => $cta_url,
    ];
  }

  /**
   * Buduje kafelek text box
   */
  private function buildTextBoxTile($paragraph) {
    $title = $paragraph->get('field_textbox_title')->value ?? '';
    $content = $paragraph->get('field_textbox_content')->value;

    return [
      '#theme' => 'npx_blocks_tile_textbox',
      '#title' => $title,
      '#content' => $content,
      '#attributes' => [
        'class' => ['npx-tile-textbox'],
      ],
    ];
  }

  /**
   * Buduje kafelek opinii
   */
  private function buildOpinionTile($paragraph) {
    $opinion_node = $paragraph->get('field_opinion_reference')->entity;

    if (!$opinion_node) {
      return [];
    }

    $opinion_node = \Drupal::service('entity.repository')->getTranslationFromContext($opinion_node);

    // Pobierz dane z node opinii
    $content_raw = $opinion_node->get('body')->value;
    $author_raw = $opinion_node->get('field_zajawka')->value;

    // Usuń tagi HTML i zachowaj łamania linii
    $content = nl2br(strip_tags($content_raw));
    // Zachowaj paragrafy w autorze - konwertuj </p><p> na <br> i usuń <p> oraz </p>
    $author = $author_raw;
    $author = str_replace('</p><p>', '<br>', $author);
    $author = str_replace('<p>', '', $author);
    $author = str_replace('</p>', '', $author);
    $author = strip_tags($author, '<br>'); // Pozwól tylko na <br> tagi
    // Usuń &nbsp; z końców linii żeby były równe odstępy
    $author = str_replace('&nbsp;', '', $author);
    $author = trim($author);

    $image_url = '';
    if ($opinion_node->hasField('field_image') && !$opinion_node->get('field_image')->isEmpty()) {
      $image_entity = $opinion_node->get('field_image')->entity;
      if ($image_entity) {
        // Jeśli to Media (typ obraz), spróbuj pobrać plik z pola field_media_image
        if ($image_entity->getEntityTypeId() === 'media') {
          $media = $image_entity;
          if ($media->hasField('field_media_image') && !$media->get('field_media_image')->isEmpty()) {
            $file = $media->get('field_media_image')->entity;
            if ($file) {
              $image_url = \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri());
            }
          }
        }
        elseif (method_exists($image_entity, 'getFileUri')) {
          $image_url = \Drupal::service('file_url_generator')->generateAbsoluteString($image_entity->getFileUri());
        }
      }
    }

    $linkedin_url = '';
    if ($opinion_node->hasField('field_linkedin_link') && !$opinion_node->get('field_linkedin_link')->isEmpty()) {
      $linkedin_url = $opinion_node->get('field_linkedin_link')->uri;
    }

    // Pobierz rating z field_ocena_wartosci_opinii (taxonomy term)
    $rating = 5; // domyślnie 5 gwiazdek
    if ($opinion_node->hasField('field_ocena_wartosci_opinii') && !$opinion_node->get('field_ocena_wartosci_opinii')->isEmpty()) {
      $rating_term = $opinion_node->get('field_ocena_wartosci_opinii')->entity;
      if ($rating_term) {
        // Mapuj nazwę termu na liczbę gwiazdek
        $term_name = $rating_term->getName();
        switch ($term_name) {
          case '1':
          case '1 gwiazdka':
            $rating = 1;
            break;
          case '2':
          case '2 gwiazdki':
            $rating = 2;
            break;
          case '3':
          case '3 gwiazdki':
            $rating = 3;
            break;
          case '4':
          case '4 gwiazdki':
            $rating = 4;
            break;
          case '5':
          case '5 gwiazdek':
          default:
            $rating = 5;
            break;
        }
      }
    }

    // Optional per-tile title and CTA link (if fields exist on paragraph)
    $tile_title = '';
    if ($paragraph->hasField('field_tile_title') && !$paragraph->get('field_tile_title')->isEmpty()) {
      $tile_title = $paragraph->get('field_tile_title')->value;
    }
    $cta_url = '';
    if ($paragraph->hasField('field_cta_link') && !$paragraph->get('field_cta_link')->isEmpty()) {
      $cta = $paragraph->get('field_cta_link')->first();
      if (!empty($cta->uri)) {
        $cta_url = Url::fromUri($cta->uri, ['absolute' => FALSE])->toString();
      }
    }

    return [
      '#theme' => 'npx_blocks_tile_opinion',
      '#author' => $author,
      '#content' => $content,
      '#company' => '', // Można dodać pole company do node opinia jeśli potrzebne
      '#rating' => $rating,
      '#image_url' => $image_url,
      '#linkedin_url' => $linkedin_url,
      '#tile_title' => $tile_title,
      '#cta_url' => $cta_url,
      '#attributes' => [
        'class' => ['npx-tile-opinion'],
      ],
    ];
  }

  /**
   * Stara logika - fallback
   */
  private function buildLegacyTabs($node) {
    $build = [];

    // Don't show any message when there are no tiles - just return empty build
    return $build;
  }

  public function getHours($closestDate) {
    $out = '';

    if($closestDate) {
      if($closestDate->get('field_npxtraining_date_hours')->value) {
        $out = $closestDate->get('field_npxtraining_date_hours')->value;
      } else {
        $count = count($closestDate->field_npxtraining_date_start);
        $days = 0;

        for($i = 0; $i < $count; $i++) {
          $days += self::calculateDays($closestDate->field_npxtraining_date_start[$i]->value, $closestDate->field_npxtraining_date_end[$i]->value);
        }
        $days_txt = $days > 1 ? ' dni | ' : ' dzień | ';
        $out =  $days . $days_txt . ($days * 8) . 'h';
      }
    }

    return $out;
  }

  public function getDays($training_node, $closestDate) {
    $out = '';

    if($training_node) {
      if($training_node->hasField('field_npxtraining_liczba_dni_lp') && !$training_node->get('field_npxtraining_liczba_dni_lp')->isEmpty()) {
        $days = $training_node->get('field_npxtraining_liczba_dni_lp')->value;
      } elseif($closestDate) {
        $count = count($closestDate->field_npxtraining_date_start);
        $days = 0;

        for($i = 0; $i < $count; $i++) {
          $days += self::calculateDays($closestDate->field_npxtraining_date_start[$i]->value, $closestDate->field_npxtraining_date_end[$i]->value);
        }
      } else {
        return $out;
      }

      $days_txt = $days > 1 ? ' dni' : ' dzień';
      $out = $days . $days_txt;
    }

    return $out;
  }

  public function getDatesDisplay($closestDate) {
    $out = null;
    if ($closestDate) {
      $count = count($closestDate->field_npxtraining_date_start);
      $out = TrainingDateHelper::prepareDateDisplay($closestDate->field_npxtraining_date_start[0]->value, $closestDate->field_npxtraining_date_end[$count-1]->value);
    }

    return $out;
  }

  public function getCity($closestDate) {
    $city = null;
    if ($closestDate && isset($closestDate->field_npxtraining_date_city->entity)) {
      $city = $closestDate->field_npxtraining_date_city->entity->name->value;
    }
    return $city;
  }

  public function getPrice($node) {
    $base_price = $node->field_npxtraining_price->value;

    $price = $base_price;
    $dcount = count($node->field_npxtraining_dates);
    if ($dcount > 0) {
      $date = $node->field_npxtraining_dates[$dcount - 1];
    } else {
      return $price;
    }

    if($date->entity && $date->entity->isPublished()) {
      $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
      $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
      if ($termTime > time()) {

        $fake_discount = 0;
        $fake_discount_upval = 0;
        if(NpxDiscountHelper::isParamFakeDiscountSet()) {
          $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
          $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
        }
        $amount = 1;

        $code_info = [];

        $disable_regular = false;
        $discount_info = NpxDiscountHelper::getDiscountInfo($fulldate->id());
        $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

        if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else if ($calc_info['info']['has_active_first']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
        }  else if ($calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else {
          $price = $calc_info['regular']['total_with_discount'];
        }
      }
    }
    return $price;
  }

  public function getDateLabels($closestDate): array {
    $labels = [];

    if ($closestDate && $this->checkDate($closestDate)) {
      foreach ($closestDate->get('field_npxtraining_date_form') as $val) {
        $label = npx_field_npxtraining_date_form_values()[$val->value];
        $labels[$val->value] = $label;
      }
    }

    return $labels;
  }

  /**
   * Get training type labels based on training node flags
   */
  public function getTrainingTypeLabels($training_node): array {
    $labels = [];

    // Check webinar flag
    if ($training_node->hasField('field_npxtraining_webinar') && $training_node->get('field_npxtraining_webinar')->value) {
      $labels['webinar'] = 'Webinar';
    }

    // Check online flag
    if ($training_node->hasField('field_npxtraining_online') && $training_node->get('field_npxtraining_online')->value) {
      $labels['online'] = 'Online';
    }

    // Check online live flag
    if ($training_node->hasField('field_online_live') && $training_node->get('field_online_live')->value) {
      $labels['online_live'] = 'Online Live';
    }

    // Check stationary flag - always show if not webinar-only
    if ($training_node->hasField('field_npxtraining_stationary') && $training_node->get('field_npxtraining_stationary')->value) {
      $labels['stationary'] = 'Stacjonarne';
    }

    return $labels;
  }

  public function checkDate(NodeInterface $date_node) {
    $now = new \DateTime(date(self::DATE_FORMAT));
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($date_node->field_npxtraining_date_start->value, 0, 10));
    $date->setTime(0,0,0);

    return $date > $now;
  }

  public function getClosestFutureDate($node) {
    $closestDate = null;
    $closestDiff = PHP_INT_MAX;

    foreach ($node->field_npxtraining_dates as $date) {
      if($date->entity && $date->entity->isPublished()) {
        $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
        $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
        if ($termTime > time()) {
          $diff = $termTime - time();
          if ($diff < $closestDiff) {
            $closestDiff = $diff;
            $closestDate = $fulldate;
          }
        }
      }
    }

    return $closestDate;
  }

  public function getLatestDate($node) {
    $latestDate = null;
    $latestTime = 0;

    foreach ($node->field_npxtraining_dates as $date) {
      if($date->entity && $date->entity->isPublished()) {
        $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
        $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
        if ($termTime > $latestTime) {
          $latestTime = $termTime;
          $latestDate = $fulldate;
        }
      }
    }

    return $latestDate;
  }

  static public function calculateDays($start, $end) {
    $date_start = new \DateTime($start);
    $date_end = new \DateTime($end);

    $date_diff = $date_start->diff($date_end);

    return $date_diff->format('%d') + 1;
  }

  /**
   * Calculate lowest possible price per person when buying for 10 people with maximum discount
   * Used when training has no available dates
   */
  public function getPriceForTenPeople($node) {
    $base_price = $node->field_npxtraining_price->value;
    $amount = 10;

    $discount_info = [];
    $first_available_date = null;
    foreach ($node->field_npxtraining_dates as $date) {
      if($date->entity && $date->entity->isPublished()) {
        $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
        $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
        if ($termTime > time()) {
          $available = $fulldate->get('field_npxtraining_date_available')->value;
          if ($available == 'tak' || $available == 'ostatnie') {
            $first_available_date = $fulldate;
            break;
          }
        }
      }
    }

    if ($first_available_date) {
      $discount_info = NpxDiscountHelper::getDiscountInfo($first_available_date->id());
    }

    $code_info = [];
    $disable_regular = false;

    $calc_info = \Drupal\npx_training_form\NpxCalculatorHelper::getCalculation(
      $base_price,
      $amount,
      $discount_info,
      $code_info,
      $disable_regular
    );

    $total_price = $base_price * $amount;
    if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
      $total_price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
    } else if ($calc_info['info']['has_active_first']) {
      $total_price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
    } else if ($calc_info['info']['has_active_last']) {
      $total_price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
    } else {
      $total_price = $calc_info['regular']['total_with_discount'];
    }

    $final_price = round($total_price / $amount);
    return $final_price;
  }

  /**
   * Generate sample dates for trainings without available dates
   */
  public function getSampleDates() {
    // Generate dates starting from next month
    $start_date = new \DateTime('first day of next month');
    $end_date = clone $start_date;
    $end_date->modify('+1 day');

    return $start_date->format('j') . '-' . $end_date->format('j.m');
  }
}
