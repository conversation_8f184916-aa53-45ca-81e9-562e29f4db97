cookies.texts:
  type: config_object
  label: 'Texts COOKiES widget'
  mapping:
    bannerText:
      type: text
      label: 'Banner text'
    privacyPolicy:
      type: label
      label: 'Privacy policy'
    privacyUri:
      type: uri
      label: 'Privacy URI'
    imprint:
      type: label
      label: 'Imprint'
    imprintUri:
      type: uri
      label: 'Imprint URI'
    cookieDocs:
      type: label
      label: 'Cookie documentation'
    cookieDocsUri:
      type: uri
      label: 'Cookie documentation URI'
    officialWebsite:
      type: label
      label: 'Official Website'
    denyAll:
      type: label
      label: 'Deny all'
    alwaysActive:
      type: label
      label: 'Always active'
    settings:
      type: label
      label: 'Settings'
    acceptAll:
      type: label
      label: 'Accept all'
    requiredCookies:
      type: label
      label: 'Required cookies'
    cookieSettings:
      type: label
      label: 'Cookie settings'
    close:
      type: label
      label: 'Close'
    readMore:
      type: label
      label: 'Read more'
    allowed:
      type: label
      label: 'Allowed'
    denied:
      type: label
      label: 'Denied'
    settingsAllServices:
      type: label
      label: 'Settings all services'
    saveSettings:
      type: label
      label: 'Save settings'
    cancelButton:
      type: label
      label: 'Cancel button'
    disclaimerText:
      type: text
      label: 'Disclaimer text'
    disclaimerTextPosition:
      type: string
      label: 'Disclaimer text position'
    processorDetailsLabel:
      type: label
      label: 'Processor company details'
    processorLabel:
      type: label
      label: 'Processor'
    processorWebsiteUrlLabel:
      type: label
      label: 'Processor website URL'
    processorPrivacyPolicyUrlLabel:
      type: label
      label: 'Processor privacy policy URL'
    processorCookiePolicyUrlLabel:
      type: label
      label: 'Processor cookie policy URL'
    processorContactLabel:
      type: label
      label: 'Processor contact'
    placeholderAcceptAllText:
      type: label
      label: 'Placeholder accept all text'
