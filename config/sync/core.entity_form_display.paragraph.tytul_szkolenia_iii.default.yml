uuid: a8d6b0c4-9488-4ac5-8eab-b1bb089fc139
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tytul_szkolenia_iii.field_ts_obraz
    - field.field.paragraph.tytul_szkolenia_iii.field_ts_opis
    - field.field.paragraph.tytul_szkolenia_iii.field_ts_podtytul
    - field.field.paragraph.tytul_szkolenia_iii.field_ts_tytul
    - image.style.thumbnail
    - paragraphs.paragraphs_type.tytul_szkolenia_iii
  module:
    - focal_point
    - text
id: paragraph.tytul_szkolenia_iii.default
targetEntityType: paragraph
bundle: tytul_szkolenia_iii
mode: default
content:
  field_ts_obraz:
    type: image_focal_point
    weight: 3
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
      preview_link: true
      offsets: '50,50'
    third_party_settings: {  }
  field_ts_opis:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ts_podtytul:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ts_tytul:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
