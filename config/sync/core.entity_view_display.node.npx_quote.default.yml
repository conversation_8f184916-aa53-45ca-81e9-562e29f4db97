uuid: df16b72b-12d8-4c42-97fc-066261c448cd
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.npx_quote.body
    - field.field.node.npx_quote.field_quote_author
    - field.field.node.npx_quote.field_quote_category
    - node.type.npx_quote
  module:
    - text
    - user
id: node.npx_quote.default
targetEntityType: node
bundle: npx_quote
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_quote_author:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  field_quote_category:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
