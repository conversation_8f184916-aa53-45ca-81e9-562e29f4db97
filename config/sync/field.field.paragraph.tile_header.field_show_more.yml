uuid: 6dd21aa6-bea8-4d01-b6a4-0310eb1dab15
langcode: pl
status: true
dependencies:
  config:
    - field.storage.paragraph.field_show_more
    - paragraphs.paragraphs_type.tile_header
id: paragraph.tile_header.field_show_more
field_name: field_show_more
entity_type: paragraph
bundle: tile_header
label: '<PERSON><PERSON><PERSON><PERSON> widocznych kafelk<PERSON>'
description: ''
required: false
translatable: false
default_value:
  -
    value: 6
default_value_callback: ''
settings:
  min: 1
  max: 50
  prefix: ''
  suffix: ''
field_type: integer
