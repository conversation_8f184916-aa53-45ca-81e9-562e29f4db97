uuid: d33fb0d0-2f78-4349-9279-f499c861fbc0
langcode: pl
status: true
dependencies:
  config:
    - field.field.taxonomy_term.quotes_categories.field_link
    - taxonomy.vocabulary.quotes_categories
  module:
    - link
    - path
    - text
id: taxonomy_term.quotes_categories.default
targetEntityType: taxonomy_term
bundle: quotes_categories
mode: default
content:
  description:
    type: text_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_link:
    type: link_default
    weight: 101
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 100
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden: {  }
