uuid: b5a920dc-17bd-452b-baf6-ddd9b0fa81b6
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.body
    - field.storage.node.field_quote_author
    - field.storage.taxonomy_term.field_link
    - taxonomy.vocabulary.quotes_categories
  module:
    - eva
    - link
    - node
    - taxonomy
    - text
    - user
id: quotes_per_categories
label: 'Cytaty na kategorie'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: '<PERSON><PERSON><PERSON>, cytaty, afo<PERSON>z<PERSON>, sentencje o rozwoju osobistym i zawodowym'
      fields:
        counter:
          id: counter
          table: views
          field: counter
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: counter
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          counter_start: 1
        body:
          id: body
          table: node__body
          field: body
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: true
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_quote_author:
          id: field_quote_author
          table: node__field_quote_author
          field: field_quote_author
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: true
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 5
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments:
        field_quote_category_target_id:
          id: field_quote_category_target_id
          table: node__field_quote_category
          field: field_quote_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_target_id
          default_action: 'not found'
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:taxonomy_term'
            fail: 'not found'
          validate_options:
            bundles:
              quotes_categories: quotes_categories
            access: false
            operation: view
            multiple: 0
          break_phrase: false
          not: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<h3>{{ arguments.field_quote_category_target_id }} - złote myśli, cytaty, aforyzmy, sentencje</h3>'
            format: full_html
          tokenize: true
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_quote_author'
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      title: 'Złote myśl'
      fields:
        body:
          id: body
          table: node__body
          field: body
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: true
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_quote_author:
          id: field_quote_author
          table: node__field_quote_author
          field: field_quote_author
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: true
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_link:
          id: field_link
          table: taxonomy_term__field_link
          field: field_link
          relationship: field_quote_category
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: uri
          type: link
          settings:
            trim_length: 80
            url_only: false
            url_plain: false
            rel: '0'
            target: '0'
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 5
      defaults:
        title: false
        pager: false
        relationships: false
        fields: false
        header: false
        footer: false
      relationships:
        field_quote_category:
          id: field_quote_category
          table: node__field_quote_category
          field: field_quote_category
          relationship: none
          group_type: group
          admin_label: 'field_quote_category: Taxonomy term'
          plugin_id: standard
          required: false
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: "<div class=\"download-all-quotes-button\" style=\"text-align: center; margin-bottom: 20px;\">\r\n{% if field_link is not empty %}\r\n        {% set training_link_uri = field_link|render|striptags|trim %}\r\n        <a href=\"{{ training_link_uri }}\" class=\"btn-trainings btn-quotes\">\r\n            Zobacz Szkolenia w tej kategorii\r\n        </a>\r\n    {% endif %}\r\n  <a \r\n    href=\"/form/quotes-download-form?category_id={{ raw_arguments.field_quote_category_target_id }}\" \r\n    class=\"use-ajax webform-dialog webform-dialog-normal button btn btn-quotes btn-download\" \r\n    data-dialog-type=\"modal\" \r\n    data-dialog-options='{\"width\":\"600px\"}'\r\n  >\r\n    Pobierz cytaty w PDF\r\n  </a>\r\n</div>"
            format: full_html
          tokenize: true
      footer: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_quote_author'
        - 'config:field.storage.taxonomy_term.field_link'
  entity_view_1:
    id: entity_view_1
    display_title: EVA
    display_plugin: entity_view
    position: 3
    display_options:
      defaults:
        header: false
      header: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      entity_type: taxonomy_term
      bundles:
        - quotes_categories
      argument_mode: token
      default_argument: '[term:tid]'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_quote_author'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 3
    display_options:
      fields:
        counter:
          id: counter
          table: views
          field: counter
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: counter
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          counter_start: 1
        body:
          id: body
          table: node__body
          field: body
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: true
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_quote_author:
          id: field_quote_author
          table: node__field_quote_author
          field: field_quote_author
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: true
          empty_zero: false
          hide_alter_empty: false
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_link:
          id: field_link
          table: taxonomy_term__field_link
          field: field_link
          relationship: field_quote_category
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: true
            text: '{{ field_link__uri }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: uri
          type: link
          settings:
            trim_length: null
            url_only: false
            url_plain: false
            rel: '0'
            target: '0'
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 0
      arguments:
        field_quote_category_target_id:
          id: field_quote_category_target_id
          table: node__field_quote_category
          field: field_quote_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_target_id
          default_action: 'not found'
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: true
          title: '{{ arguments.field_quote_category_target_id }}'
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:taxonomy_term'
            fail: 'not found'
          validate_options:
            bundles:
              quotes_categories: quotes_categories
            access: false
            operation: view
            multiple: 0
          break_phrase: false
          not: false
      defaults:
        pager: false
        relationships: false
        fields: false
        arguments: false
        footer: false
      relationships:
        field_quote_category:
          id: field_quote_category
          table: node__field_quote_category
          field: field_quote_category
          relationship: none
          group_type: group
          admin_label: 'field_quote_category: Taxonomy term'
          plugin_id: standard
          required: false
      footer:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: "<div class=\"download-all-quotes-button\" style=\"text-align: center; margin-bottom: 20px;\">\r\n  <a \r\n    href=\"/form/quotes-download-form?category_id={{ raw_arguments.field_quote_category_target_id }}\" \r\n    class=\"use-ajax webform-dialog webform-dialog-normal button btn btn-quotes btn-download\" \r\n    data-dialog-type=\"modal\" \r\n    data-dialog-options='{\"width\":\"600px\"}'\r\n  >\r\n    Pobieram cytaty\r\n  </a>\r\n{% if field_link is not empty %}\r\n        {% set training_link_uri = field_link|render|striptags|trim %}\r\n        <a href=\"{{ training_link_uri }}\" class=\"btn-trainings btn-quotes\">\r\n            Zobacz Szkolenia\r\n        </a>\r\n    {% endif %}\r\n</div>"
            format: full_html
          tokenize: true
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: zlote-mysli/%
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_quote_author'
        - 'config:field.storage.taxonomy_term.field_link'
