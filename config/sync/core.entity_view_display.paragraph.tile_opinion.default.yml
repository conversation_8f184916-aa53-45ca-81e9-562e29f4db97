uuid: 12345678-1234-1234-1234-123456789028
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tile_opinion.field_cta_link
    - field.field.paragraph.tile_opinion.field_opinion_reference
    - field.field.paragraph.tile_opinion.field_tile_title
    - paragraphs.paragraphs_type.tile_opinion
  module:
    - link
    - text
id: paragraph.tile_opinion.default
targetEntityType: paragraph
bundle: tile_opinion
mode: default
content:
  field_cta_link:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_opinion_reference:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
  field_tile_title:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
hidden: {  }
