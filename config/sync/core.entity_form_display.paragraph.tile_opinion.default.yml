uuid: 12345678-1234-1234-1234-123456789027
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tile_opinion.field_cta_link
    - field.field.paragraph.tile_opinion.field_opinion_reference
    - field.field.paragraph.tile_opinion.field_tile_title
    - paragraphs.paragraphs_type.tile_opinion
  module:
    - link
    - text
id: paragraph.tile_opinion.default
targetEntityType: paragraph
bundle: tile_opinion
mode: default
content:
  field_cta_link:
    type: link_default
    weight: 2
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_opinion_reference:
    type: entity_reference_autocomplete
    weight: 0
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_tile_title:
    type: text_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
