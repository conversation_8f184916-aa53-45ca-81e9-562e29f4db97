uuid: 3696da9f-85f7-4d86-8ad3-c98b26b1d546
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.npx_quote
  module:
    - text
id: node.npx_quote.body
field_name: body
entity_type: node
bundle: npx_quote
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  required_summary: false
  allowed_formats: {  }
field_type: text_with_summary
