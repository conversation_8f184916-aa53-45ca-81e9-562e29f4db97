uuid: 7de2043c-b4e6-4361-b66c-fddac7d18cee
langcode: pl
status: false
dependencies:
  config:
    - system.menu.mmenu
  module:
    - superfish
    - user
  theme:
    - bootstrap4grow
id: bootstrap4grow_mobilemenu
theme: bootstrap4grow
region: header
weight: -36
provider: null
plugin: 'superfish:mmenu'
settings:
  id: 'superfish:mmenu'
  label: 'SF Main navigation - Mobile menu'
  label_display: '0'
  provider: superfish
  level: 1
  depth: 0
  expand_all_items: false
  menu_type: horizontal
  style: none
  arrow: 0
  shadow: 0
  speed: fast
  delay: 800
  slide: vertical
  supposition: 1
  hoverintent: 1
  touch: 0
  touchbh: 2
  touchdh: 0
  touchbp: 768
  touchua: 0
  touchual: ''
  touchuam: 0
  small: 2
  smallbp: 992
  smallua: 0
  smallual: ''
  smalluam: 0
  smallact: 1
  smallset: null
  smallasa: 0
  smallcmc: 0
  smallecm: ''
  smallchc: 0
  smallech: null
  smallicm: ''
  smallich: ''
  smallamt: MENU
  smallabt: 0
  supersubs: 1
  minwidth: 12
  maxwidth: 27
  multicolumn: 0
  multicolumn_depth: 1
  multicolumn_levels: 1
  pathlevels: 1
  expanded: 0
  clone_parent: 0
  hide_linkdescription: 0
  add_linkdescription: 0
  link_depth_class: 1
  custom_list_class: ''
  custom_item_class: ''
  custom_link_class: ''
  link_text_prefix: ''
  link_text_suffix: ''
visibility:
  user_role:
    id: user_role
    negate: false
    context_mapping:
      user: '@user.current_user_context:current_user'
    roles:
      tester_grow3: tester_grow3
