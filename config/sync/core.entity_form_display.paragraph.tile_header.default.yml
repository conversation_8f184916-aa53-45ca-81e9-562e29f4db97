uuid: 6847cc23-87a3-4655-89bc-6f95aa3684ea
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tile_header.field_header_content
    - field.field.paragraph.tile_header.field_header_title
    - field.field.paragraph.tile_header.field_show_more
    - paragraphs.paragraphs_type.tile_header
  module:
    - text
id: paragraph.tile_header.default
targetEntityType: paragraph
bundle: tile_header
mode: default
content:
  field_header_content:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_header_title:
    type: text_textarea
    weight: 0
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_show_more:
    type: number
    weight: 2
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
