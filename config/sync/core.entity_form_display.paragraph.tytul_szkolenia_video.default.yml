uuid: 575284f8-8d8b-42ed-a6b2-ea6990a9dafe
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tytul_szkolenia_video.field_ts_obraz
    - field.field.paragraph.tytul_szkolenia_video.field_ts_opis
    - field.field.paragraph.tytul_szkolenia_video.field_ts_podtytul
    - field.field.paragraph.tytul_szkolenia_video.field_ts_tytul
    - field.field.paragraph.tytul_szkolenia_video.field_ts_video_embed
    - image.style.thumbnail
    - paragraphs.paragraphs_type.tytul_szkolenia_video
  module:
    - focal_point
    - imce
    - text
    - video_embed_field
id: paragraph.tytul_szkolenia_video.default
targetEntityType: paragraph
bundle: tytul_szkolenia_video
mode: default
content:
  field_ts_obraz:
    type: image_focal_point
    weight: 4
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
      preview_link: true
      offsets: '50,50'
    third_party_settings:
      imce:
        enabled: false
  field_ts_opis:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ts_podtytul:
    type: text_textarea
    weight: 1
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ts_tytul:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_ts_video_embed:
    type: video_embed_field_textfield
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  created: true
  status: true
