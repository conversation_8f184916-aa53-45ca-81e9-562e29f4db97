uuid: 5145e06e-ff86-4262-9ef5-051a83bbd3e2
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_quote_author
    - node.type.npx_quote
id: node.npx_quote.field_quote_author
field_name: field_quote_author
entity_type: node
bundle: npx_quote
label: Autor
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
