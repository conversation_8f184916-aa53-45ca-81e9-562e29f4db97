uuid: 5874dff2-0f61-4c0d-a53b-8cf819a83d8a
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.tile_training.field_cta_link
    - field.field.paragraph.tile_training.field_training_reference
    - paragraphs.paragraphs_type.tile_training
  module:
    - ds
    - link
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: all_classes
      settings:
        label: ''
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      ds_content:
        - field_training_reference
id: paragraph.tile_training.default
targetEntityType: paragraph
bundle: tile_training
mode: default
content:
  field_cta_link:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_training_reference:
    type: entity_reference_entity_view
    label: visually_hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 0
    region: ds_content
hidden: {  }
