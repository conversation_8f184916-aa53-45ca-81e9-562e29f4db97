{#
/**
 * @file
 * Theme override for guidelines for a text format.
 *
 * Available variables:
 * - format: Contains information about the current text format, including the
 *   following:
 *   - name: The name of the text format, potentially unsafe and needs to be
 *     escaped.
 *   - format: The machine name of the text format, e.g. 'basic_html'.
 * - attributes: HTML attributes for the containing element.
 * - tips: Descriptions and a CSS ID in the form of 'module-name/filter-id'
 *   (only used when 'long' is TRUE) for each filter in one or more text
 *   formats.
 *
 * @see template_preprocess_filter_tips()
 */
#}
{%
  set classes = [
    'filter-guidelines-item',
    'filter-guidelines-' ~ format.id,
  ]
%}
<div{{ attributes.addClass(classes) }}>
  <h4 class="label">{{ format.label }}</h4>
  {{ tips }}
</div>
