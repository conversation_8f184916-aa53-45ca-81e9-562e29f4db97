{#
/**
 * @file
 * Theme override for a list of new, unsaved media items being added in the
 * modal media library dialog.
 *
 * Available variables:
 * - items: A list of items. Each item contains:
 *   - attributes: HTML attributes to be applied to each list item.
 *   - value: The content of the list element.
 * - title: The title of the list.
 * - list_type: The tag for list element ("ul" or "ol").
 * - wrapper_attributes: HTML attributes to be applied to the list wrapper.
 * - attributes: HTML attributes to be applied to the list.
 * - empty: A message to display when there are no items. Allowed value is a
 *   string or render array.
 * - context: A list of contextual data associated with the list. May contain:
 *   - list_style: The custom list style.
 *
 * @see seven_preprocess_item_list__media_library_add_form_media_list()
 * @see template_preprocess_item_list()
 */
#}
{% if items -%}
  {%- if title is not empty -%}
    <h3>{{ title }}</h3>
  {%- endif -%}
  <{{ list_type }}{{ attributes.addClass('media-library-add-form__added-media') }}>
  {%- for item in items -%}
    <li{{ item.attributes.addClass('media-library-add-form__media') }}>{{ item.value }}</li>
  {%- endfor -%}
  </{{ list_type }}>
{%- endif %}
