!function(a,n){"use strict";var l="flybox",s=l,o="is-"+l,t=o,e="["+(o="data-"+l)+"-gallery]:not(."+t+")",f="["+o+"-trigger]";function i(o){o.preventDefault(),o.stopPropagation();o=o.target,o=o.href?o:a.closest(o,f);a.flybox.open(o)}function c(o){a.on(o,"click."+l,f,i),a.addClass(o,t)}a.flybox={open:function(o){a.isElm(o)&&n.blazyBox.open(o,{bodyClass:"is-flybox--open",bodyClosingClass:"is-flybox--closing",class:"b-flybox",fs:!1})}},n.behaviors.flyBox={attach:function(o){a.once(c,s,e,o)},detach:function(o,n,l){"unload"===l&&a.once.removeSafely(s,e,o)}}}(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>);
