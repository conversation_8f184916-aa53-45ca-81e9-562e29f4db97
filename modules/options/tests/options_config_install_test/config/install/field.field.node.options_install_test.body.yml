langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.options_install_test
third_party_settings: {  }
id: node.options_install_test.body
field_name: body
entity_type: node
bundle: options_install_test
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
  allowed_formats: {  }
field_type: text_with_summary
