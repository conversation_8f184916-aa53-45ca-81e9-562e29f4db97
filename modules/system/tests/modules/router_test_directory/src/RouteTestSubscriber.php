<?php

namespace Drupal\router_test;

use <PERSON><PERSON>al\Core\Routing\RouteSubscriberBase;
use <PERSON>ymfony\Component\Routing\RouteCollection;

/**
 * Listens to the dynamic route event and add a test route.
 */
class RouteTestSubscriber extends RouteSubscriberBase {

  /**
   * {@inheritdoc}
   */
  protected function alterRoutes(RouteCollection $collection) {
    $route = $collection->get('router_test.6');
    // Change controller method from test1 to test5.
    $route->setDefault('_controller', '\Drupal\router_test\TestControllers::test5');
  }

}
