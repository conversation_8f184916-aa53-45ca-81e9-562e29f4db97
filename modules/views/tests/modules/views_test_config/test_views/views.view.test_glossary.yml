langcode: en
status: true
dependencies:
  module:
    - node
    - user
id: test_glossary
label: Test glossary
module: node
description: 'All content, by letter.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      use_ajax: true
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: mini
        options:
          items_per_page: 36
          offset: 0
          id: 0
          total_pages: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: ‹‹
            next: ››
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          plugin_id: field
          relationship: none
          group_type: group
          admin_label: ''
          label: Title
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          entity_type: node
          entity_field: title
        name:
          id: name
          table: users_field_data
          field: name
          label: Author
          relationship: uid
          plugin_id: field
          type: user_name
          group_type: group
          admin_label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          entity_type: user
          entity_field: name
        changed:
          id: changed
          table: node_field_data
          field: changed
          label: 'Last update'
          type: timestamp
          settings:
            date_format: long
            custom_date_format: ''
            timezone: ''
          plugin_id: field
          relationship: none
          group_type: group
          admin_label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          entity_type: node
          entity_field: changed
      arguments:
        title:
          id: title
          table: node_field_data
          field: title
          default_action: default
          exception:
            title_enable: true
          default_argument_type: fixed
          default_argument_options:
            argument: a
          summary:
            format: default_summary
          specify_validation: true
          glossary: true
          limit: 1
          case: upper
          path_case: lower
          transform_dash: false
          plugin_id: string
          relationship: none
          group_type: group
          admin_label: ''
          title_enable: false
          title: ''
          summary_options: {  }
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          entity_type: node
          entity_field: title
      relationships:
        uid:
          id: uid
          table: node_field_data
          field: uid
          plugin_id: standard
          relationship: none
          group_type: group
          admin_label: author
          required: false
      style:
        type: table
        options:
          columns:
            title: title
            name: name
            changed: changed
          default: title
          info:
            title:
              sortable: true
              separator: ''
            name:
              sortable: true
              separator: ''
            changed:
              sortable: true
              separator: ''
          override: true
          sticky: false
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
          order: asc
          summary: ''
          empty_table: false
      row:
        type: fields
        options:
          inline: {  }
          separator: ''
          hide_empty: false
          default_field_elements: true
      header: {  }
      footer: {  }
      empty: {  }
      sorts: {  }
      filters:
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: language
          entity_type: node
          entity_field: langcode
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: 0
      tags: {  }
  attachment_1:
    id: attachment_1
    display_title: Attachment
    display_plugin: attachment
    position: 2
    display_options:
      query:
        type: views_query
        options: {  }
      pager:
        type: none
        options:
          offset: 0
          items_per_page: 0
      defaults:
        arguments: false
      arguments:
        title:
          id: title
          table: node_field_data
          field: title
          default_action: summary
          exception:
            title_enable: true
          default_argument_type: fixed
          default_argument_options:
            argument: a
          summary:
            format: unformatted_summary
          summary_options:
            items_per_page: 25
            inline: true
            separator: ' | '
          specify_validation: true
          glossary: true
          limit: 1
          case: upper
          path_case: lower
          transform_dash: false
          plugin_id: string
          relationship: none
          group_type: group
          admin_label: ''
          title_enable: false
          title: ''
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          entity_type: node
          entity_field: title
      displays:
        default: default
        page_1: page_1
      inherit_arguments: false
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: 0
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      query:
        type: views_query
        options: {  }
      path: test-glossary
      menu:
        type: normal
        title: Glossary
        weight: 0
        menu_name: main
        parent: ''
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: 0
      tags: {  }
