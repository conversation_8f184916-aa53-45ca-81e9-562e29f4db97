# cspell:ignore objectid
id: d7_field_instance_label_description_translation
label: Field label and description translation
migration_tags:
  - Drupal 7
  - Configuration
  - Multilingual
class: Drupal\migrate_drupal\Plugin\migrate\FieldMigration
field_plugin_method: alterFieldInstanceMigration
source:
  plugin: d7_field_instance_label_description_translation
process:
  langcode:
    plugin: skip_on_empty
    source: language
    method: row
  translation:
    plugin: skip_on_empty
    source: translation
    method: row
  entity_type_exists:
    plugin: skip_on_empty
    source: entity_type
    method: row
  objectid_exists:
    plugin: skip_on_empty
    source: objectid
    method: row
  type_exists:
    plugin: skip_on_empty
    source: type
    method: row
  exists:
    -
      plugin: migration_lookup
      migration: d7_field_instance
      source:
        - entity_type
        - objectid
        - type
    -
      plugin: skip_on_empty
      method: row
  bundle: objectid
  property:
    plugin: static_map
    source: property
    bypass: true
    map:
      label: label
      description: description
      title_value: label
  entity_type: entity_type
  field_name: type
destination:
  plugin: entity:field_config
  translations: true
migration_dependencies:
  required:
    - language
    - d7_field_instance
  optional:
    - d7_node_type
    - d7_comment_type
    - d7_taxonomy_vocabulary
