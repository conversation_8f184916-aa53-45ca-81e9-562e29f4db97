/**
 * @file
 * System admin module: modules page.
 */

:root {
  --module-table-cell-padding-vertical: var(--space-m);
  --module-table-cell-padding-horizontal: calc(var(--space-m) - (var(--input-border-size) * 2));
}

.modules-table-filter,
.permissions-table-filter {
  padding: 0.25rem var(--space-l);
  border: 1px solid var(--color-gray-200);
  border-radius: 2px 2px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Visually hide the module filter input description. */
.modules-table-filter .form-item__description,
.permissions-table-filter .form-item__description {
  position: absolute !important;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  width: 1px;
  height: 1px;
  word-wrap: normal;
}

.claro-details.claro-details--package-listing,
.claro-details.module-list__module-details {
  margin-top: 2rem;
  margin-bottom: 0;
  border: none;
  box-shadow: none;
}

.claro-details__wrapper.claro-details__wrapper--package-listing {
  margin: 0;
}

.claro-details__summary.claro-details__summary--package-listing {
  color: var(--color-text);
  border-radius: 4px;
  background: var(--color-gray-050);
  font-size: 1.125rem; /* 18px */
  line-height: 1.424rem; /* 23px */
}

.module-list {
  margin-top: 0;
}

.module-list__module {
  color: var(--color-text);
  border-bottom: 1px solid var(--color-gray-200);
  background: none;
}

.module-list__module:hover {
  background: none;
}

.module-list__module td {
  height: auto;
  padding: var(--module-table-cell-padding-vertical) var(--module-table-cell-padding-horizontal);
  vertical-align: top;
}

/* Set width only on wider view where description is visible by default. */
@media screen and (min-width: 60em) {
  td.module-list__module {
    width: 25%;
  }
}

.module-list__module-name {
  font-weight: bold;
}

.module-list__checkbox {
  padding-left: 0.6875rem; /* LTR */
  text-align: left; /* LTR */
}
[dir="rtl"] .module-list__checkbox {
  padding-right: 0.6875rem;
  padding-left: var(--module-table-cell-padding-horizontal);
  text-align: right;
}

.module-list__checkbox .form-type--checkbox {
  margin: 0;
  line-height: var(--details-line-height);
}

.module-list__checkbox .form-checkbox:not([disabled]) {
  cursor: pointer;
}

td.module-list__checkbox {
  width: 4%;
}

td.module-list__description {
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
}

.claro-details.module-list__module-details {
  margin: 0;
}

.claro-details__summary.module-list__module-summary {
  padding-top: var(--module-table-cell-padding-vertical);
  padding-bottom: var(--module-table-cell-padding-vertical);
  font-weight: normal;
  line-height: var(--details-line-height);
}

.claro-details__summary.module-list__module-summary::before {
  top: calc(var(--space-m) + var(--space-s));
}

.module-details__description {
  font-size: var(--space-s);
  line-height: 0.9375rem;
}

.claro-details__wrapper.module-details__wrapper {
  margin-top: 0;
  margin-bottom: 0;
}

.module-details__requirements {
  margin-bottom: var(--space-m);
}

.module-details__links {
  position: relative;
  /* Negative margin matches the value of action link's top padding. */
  margin-top: calc((var(--space-s) - ((var(--space-l) - var(--space-s)) / 2)) * -1);
  margin-bottom: var(--space-m);
}

.module-details__links .action-link + .action-link {
  margin-left: 0; /* LTR */
}
[dir="rtl"] .module-details__links .action-link + .action-link {
  margin-right: 0;
}

.claro-details .tableresponsive-toggle {
  padding: var(--space-m) var(--space-m) var(--space-m) 0;
}
.claro-details .tableresponsive-toggle::before {
  /* This adjustment is necessary for better alignment with the adjacent button
   text. */
  position: relative;
  top: -1px;
  display: inline-block;
  width: calc(var(--space-m) * 2);
  height: 1.25rem;
  content: "";
  cursor: pointer;
  vertical-align: text-top;
  background: url(../../images/icons/545560/plus.svg) no-repeat center;
  background-size: contain;
}

.claro-details .tableresponsive-toggle:hover {
  color: var(--color-absolutezero-hover);
  background-color: var(--color-bgblue-hover);
}
.claro-details .tableresponsive-toggle:hover::before {
  background-image: url(../../images/icons/0036b1/plus.svg);
}

.claro-details .tableresponsive-toggle-columns button {
  margin-top: var(--space-xs);
  text-decoration: none;
  color: var(--color-gray-800);
  font-weight: bold;
}

.claro-details .admin-missing {
  color: var(--color-maximumred);
}
