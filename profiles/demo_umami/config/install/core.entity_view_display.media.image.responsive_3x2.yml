langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.responsive_3x2
    - field.field.media.image.field_media_image
    - media.type.image
    - responsive_image.styles.3_2_image
  module:
    - layout_builder
    - responsive_image
third_party_settings:
  layout_builder:
    enabled: false
    allow_custom: false
id: media.image.responsive_3x2
targetEntityType: media
bundle: image
mode: responsive_3x2
content:
  field_media_image:
    type: responsive_image
    label: hidden
    settings:
      responsive_image_style: 3_2_image
      image_link: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  created: true
  langcode: true
  name: true
  thumbnail: true
  uid: true
