!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{Bookmark:"Bookmark","Bookmark must not be empty.":"Bookmark must not be empty.","Bookmark name":"Bookmark name","Bookmark name already exists.":"Bookmark name already exists.","Bookmark name cannot contain space characters.":"Bookmark name cannot contain space characters.","Bookmark toolbar":"Bookmark toolbar","bookmark widget":"bookmark widget",Bookmarks:"Bookmarks","Edit bookmark":"Edit bookmark","Enter the bookmark name without spaces.":"Enter the bookmark name without spaces.","No bookmarks available.":"No bookmarks available.","Remove bookmark":"Remove bookmark","Scroll to bookmark":"Scroll to bookmark"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(o){e.setAttribute(o,t[o])}))}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},128:e=>{"use strict";var t={};e.exports=function(e,o){var r=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(o)}},156:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var r=o(758),i=o.n(r),n=o(935),s=o.n(n)()(i());s.push([e.id,".ck.ck-bookmark-balloon .ck.ck-toolbar>.ck-toolbar__items{flex-wrap:nowrap}.ck.ck-bookmark-toolbar__preview{cursor:default;font-weight:400;max-width:var(--ck-input-width);min-width:3em;overflow:hidden;padding:0 var(--ck-spacing-medium);text-align:center;text-overflow:ellipsis;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}",""]);const a=s},237:e=>{"use strict";e.exports=CKEditor5.dll},311:(e,t,o)=>{e.exports=o(237)("./src/ui.js")},355:(e,t,o)=>{e.exports=o(237)("./src/icons.js")},493:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var r=o(758),i=o.n(r),n=o(935),s=o.n(n)()(i());s.push([e.id,":root{--ck-bookmark-icon-hover-fill-color:var(--ck-color-widget-hover-border);--ck-bookmark-icon-selected-fill-color:var(--ck-color-focus-border);--ck-bookmark-icon-animation-duration:var(--ck-widget-handler-animation-duration);--ck-bookmark-icon-animation-curve:var(--ck-widget-handler-animation-curve)}.ck .ck-bookmark.ck-widget{display:inline-block;outline:none}.ck .ck-bookmark.ck-widget .ck-bookmark__icon .ck-icon__fill{transition:fill var(--ck-bookmark-icon-animation-duration) var(--ck-bookmark-icon-animation-curve)}.ck .ck-bookmark.ck-widget:hover .ck-bookmark__icon .ck-icon__fill{fill:var(--ck-bookmark-icon-hover-fill-color)}.ck .ck-bookmark.ck-widget.ck-widget_selected .ck-bookmark__icon .ck-icon__fill{fill:var(--ck-bookmark-icon-selected-fill-color)}.ck .ck-bookmark.ck-widget.ck-widget_selected,.ck .ck-bookmark.ck-widget.ck-widget_selected:hover{outline:none}.ck .ck-bookmark.ck-widget .ck-bookmark__icon{display:block;position:relative;top:-.1em}.ck .ck-bookmark.ck-widget .ck-bookmark__icon .ck-icon{height:1.2em;vertical-align:middle;width:auto}.ck .ck-fake-bookmark-selection{background:var(--ck-color-link-fake-selection)}.ck .ck-fake-bookmark-selection_collapsed{border-right:1px solid var(--ck-color-base-text);height:100%;margin-right:-1px;outline:1px solid hsla(0,0%,100%,.5)}",""]);const a=s},501:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var r=o(758),i=o.n(r),n=o(935),s=o.n(n)()(i());s.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-number,.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const a=s},584:(e,t,o)=>{e.exports=o(237)("./src/utils.js")},591:e=>{"use strict";var t=[];function o(e){for(var o=-1,r=0;r<t.length;r++)if(t[r].identifier===e){o=r;break}return o}function r(e,r){for(var n={},s=[],a=0;a<e.length;a++){var c=e[a],l=r.base?c[0]+r.base:c[0],k=n[l]||0,d="".concat(l," ").concat(k);n[l]=k+1;var m=o(d),u={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==m)t[m].references++,t[m].updater(u);else{var h=i(u,r);r.byIndex=a,t.splice(a,0,{identifier:d,updater:h,references:1})}s.push(d)}return s}function i(e,t){var o=t.domAPI(t);o.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,i){var n=r(e=e||[],i=i||{});return function(e){e=e||[];for(var s=0;s<n.length;s++){var a=o(n[s]);t[a].references--}for(var c=r(e,i),l=0;l<n.length;l++){var k=o(n[l]);0===t[k].references&&(t[k].updater(),t.splice(k,1))}n=c}}},639:e=>{"use strict";var t,o=(t=[],function(e,o){return t[e]=o,t.filter(Boolean).join("\n")});function r(e,t,r,i){var n;if(r)n="";else{n="",i.supports&&(n+="@supports (".concat(i.supports,") {")),i.media&&(n+="@media ".concat(i.media," {"));var s=void 0!==i.layer;s&&(n+="@layer".concat(i.layer.length>0?" ".concat(i.layer):""," {")),n+=i.css,s&&(n+="}"),i.media&&(n+="}"),i.supports&&(n+="}")}if(e.styleSheet)e.styleSheet.cssText=o(t,n);else{var a=document.createTextNode(n),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(a,c[t]):e.appendChild(a)}}var i={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=i.singletonCounter++,o=i.singleton||(i.singleton=e.insertStyleElement(e));return{update:function(e){r(o,t,!1,e)},remove:function(e){r(o,t,!0,e)}}}},731:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var r=o(758),i=o.n(r),n=o(935),s=o.n(n)()(i());s.push([e.id,":root{--ck-bookmark-form-width:340px}@media screen and (max-width:600px){:root{--ck-bookmark-form-width:300px}}.ck.ck-bookmark-form{width:var(--ck-bookmark-form-width)}",""]);const a=s},758:e=>{"use strict";e.exports=function(e){return e[1]}},782:(e,t,o)=>{e.exports=o(237)("./src/core.js")},901:(e,t,o)=>{e.exports=o(237)("./src/widget.js")},922:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var r=o(758),i=o.n(r),n=o(935),s=o.n(n)()(i());s.push([e.id,":root{--ck-form-default-width:340px}.ck.ck-form{padding:0 0 var(--ck-spacing-large)}.ck.ck-form.ck-form_default-width{width:var(--ck-form-default-width)}.ck.ck-form:focus{outline:none}.ck.ck-form .ck.ck-input-number,.ck.ck-form .ck.ck-input-text{min-width:100%;width:0}.ck.ck-form .ck.ck-dropdown{min-width:100%}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button:not(:focus){border:1px solid var(--ck-color-base-border)}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button .ck-button__label{width:100%}@media screen and (max-width:600px){.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit{align-items:stretch;flex-direction:column;padding:0}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit>.ck{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_with-submit .ck-button_with-text{justify-content:center}.ck.ck-form.ck-responsive-form .ck.ck-form__row.ck-form__row_large-bottom-padding{padding-bottom:var(--ck-spacing-large)}}[dir=ltr] .ck.ck-form.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-form.ck-responsive-form>:not(:last-child){margin-left:0}",""]);const a=s},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",r=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),r&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),r&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,r,i,n){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(r)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(s[c]=!0)}for(var l=0;l<e.length;l++){var k=[].concat(e[l]);r&&s[k[0]]||(void 0!==n&&(void 0===k[5]||(k[1]="@layer".concat(k[5].length>0?" ".concat(k[5]):""," {").concat(k[1],"}")),k[5]=n),o&&(k[2]?(k[1]="@media ".concat(k[2]," {").concat(k[1],"}"),k[2]=o):k[2]=o),i&&(k[4]?(k[1]="@supports (".concat(k[4],") {").concat(k[1],"}"),k[4]=i):k[4]="".concat(i)),t.push(k))}},t}}},t={};function o(r){var i=t[r];if(void 0!==i)return i.exports;var n=t[r]={id:r,exports:{}};return e[r](n,n.exports,o),n.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";o.r(r),o.d(r,{Bookmark:()=>L,BookmarkEditing:()=>C,BookmarkUI:()=>O,InsertBookmarkCommand:()=>y,UpdateBookmarkCommand:()=>I});var e=o(782),t=o(901),i=o(311),n=o(355),s=o(584);var a=o(591),c=o.n(a),l=o(639),k=o.n(l),d=o(128),m=o.n(d),u=o(21),h=o.n(u),b=o(51),f=o.n(b),p=o(501),w={attributes:{"data-cke":!0}};w.setAttributes=h(),w.insert=m().bind(null,"head"),w.domAPI=k(),w.insertStyleElement=f();c()(p.A,w);p.A&&p.A.locals&&p.A.locals;var g=o(922),v={attributes:{"data-cke":!0}};v.setAttributes=h(),v.insert=m().bind(null,"head"),v.domAPI=k(),v.insertStyleElement=f();c()(g.A,v);g.A&&g.A.locals&&g.A.locals;var _=o(731),B={attributes:{"data-cke":!0}};B.setAttributes=h(),B.insert=m().bind(null,"head"),B.domAPI=k(),B.insertStyleElement=f();c()(_.A,B);_.A&&_.A.locals&&_.A.locals;class V extends i.View{focusTracker=new s.FocusTracker;keystrokes=new s.KeystrokeHandler;idInputView;backButtonView;saveButtonView;children;_validators;_focusables=new i.ViewCollection;_focusCycler;constructor(e,t){super(e),this._validators=t,this.backButtonView=this._createBackButton(),this.saveButtonView=this._createSaveButton(),this.idInputView=this._createIdInput(),this.children=this.createCollection([this._createHeaderView()]),this.children.add(new i.FormRowView(e,{children:[this.idInputView,this.saveButtonView],class:["ck-form__row_with-submit","ck-form__row_large-top-padding"]})),this.keystrokes.set("Esc",((e,t)=>{this.fire("cancel"),t()})),this._focusCycler=new i.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-form","ck-bookmark-form","ck-responsive-form"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,i.submitHandler)({view:this});[this.backButtonView,this.idInputView,this.saveButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this.idInputView.focus()}isValid(){this.resetFormStatus();for(const e of this._validators){const t=e(this);if(t)return this.idInputView.errorText=t,!1}return!0}resetFormStatus(){this.idInputView.errorText=null}_createBackButton(){const e=this.locale.t,t=new i.ButtonView(this.locale);return t.set({class:"ck-button-back",label:e("Back"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M11.463 5.187a.888.888 0 1 1 1.254 1.255L9.16 10l3.557 3.557a.888.888 0 1 1-1.254 1.255L7.26 10.61a.888.888 0 0 1 .16-1.382l4.043-4.042z"/></svg>',tooltip:!0}),t.delegate("execute").to(this,"cancel"),t}_createSaveButton(){const e=this.locale.t,t=new i.ButtonView(this.locale);return t.set({label:e("Save"),withText:!0,type:"submit",class:"ck-button-action ck-button-bold"}),t}_createHeaderView(){const e=this.locale.t,t=new i.FormHeaderView(this.locale,{label:e("Bookmark")});return t.children.add(this.backButtonView,0),t}_createIdInput(){const e=this.locale.t,t=new i.LabeledFieldView(this.locale,i.createLabeledInputText);return t.label=e("Bookmark name"),t.infoText=e("Enter the bookmark name without spaces."),t.class="ck-labeled-field-view_full-width",t}get id(){const{element:e}=this.idInputView.fieldView;return e?e.value.trim():null}}function x(e){return!(!e||"string"!=typeof e)&&!/\s/.test(e)}class y extends e.Command{refresh(){const e=this.editor.model.document.selection,t=this._getPositionToInsertBookmark(e);this.isEnabled=!!t}execute(e){if(!e)return;const{bookmarkId:t}=e;if(!x(t))return void(0,s.logWarning)("insert-bookmark-command-executed-with-invalid-name");const o=this.editor,r=o.model,i=r.document.selection;r.change((e=>{let n=this._getPositionToInsertBookmark(i);if(!r.schema.checkChild(n,"bookmark")){const e=o.execute("insertParagraph",{position:n});if(!e)return;n=e}const s=e.createElement("bookmark",{...Object.fromEntries(i.getAttributes()),bookmarkId:t});r.insertObject(s,n,null,{setSelection:"on"})}))}_getPositionToInsertBookmark(e){const t=this.editor.model,o=t.schema,r=e.getFirstRange(),i=r.start;if(E(i,o))return i;for(const{previousPosition:e,item:i}of r){if(i.is("element")&&o.checkChild(i,"$text")&&E(i,o))return t.createPositionAt(i,0);if(E(e,o))return e}return null}}function E(e,t){return!!t.checkChild(e,"bookmark")||!!t.checkChild(e,"paragraph")&&t.checkChild("paragraph","bookmark")}class I extends e.Command{refresh(){const e=A(this.editor.model.document.selection);this.isEnabled=!!e,this.value=e?e.getAttribute("bookmarkId"):void 0}execute(e){if(!e)return;const{bookmarkId:t}=e;if(!x(t))return void(0,s.logWarning)("update-bookmark-command-executed-with-invalid-name");const o=this.editor.model,r=A(o.document.selection);r&&o.change((e=>{e.setAttribute("bookmarkId",t,r)}))}}function A(e){const t=e.getSelectedElement();return t&&t.is("element","bookmark")?t:null}var S=o(493),T={attributes:{"data-cke":!0}};T.setAttributes=h(),T.insert=m().bind(null,"head"),T.domAPI=k(),T.insertStyleElement=f();c()(S.A,T);S.A&&S.A.locals&&S.A.locals;class C extends e.Plugin{_bookmarkElements=new Map;static get pluginName(){return"BookmarkEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("bookmark",{toolbar:["bookmarkPreview","|","editBookmark","removeBookmark"]})}init(){const{editor:e}=this;this._defineSchema(),this._defineConverters(),e.commands.add("insertBookmark",new y(e)),e.commands.add("updateBookmark",new I(e)),this.listenTo(e.model.document,"change:data",(()=>{this._trackBookmarkElements()}))}getElementForBookmarkId(e){for(const[t,o]of this._bookmarkElements)if(o==e)return t;return null}getAllBookmarkNames(){return new Set(this._bookmarkElements.values())}_defineSchema(){this.editor.model.schema.register("bookmark",{inheritAllFrom:"$inlineObject",allowAttributes:"bookmarkId",disallowAttributes:["linkHref","htmlA"]})}_defineConverters(){const{editor:e}=this,{conversion:o,t:r}=e;e.data.htmlProcessor.domConverter.registerInlineObjectMatcher((e=>F(e))),e.editing.view.domConverter.registerInlineObjectMatcher((e=>F(e,!1))),o.for("dataDowncast").elementToElement({model:{name:"bookmark",attributes:["bookmarkId"]},view:(e,{writer:t})=>t.createEmptyElement("a",{id:e.getAttribute("bookmarkId")})}),o.for("editingDowncast").elementToElement({model:{name:"bookmark",attributes:["bookmarkId"]},view:(e,{writer:o})=>{const i=e.getAttribute("bookmarkId"),n=o.createContainerElement("a",{id:i,class:"ck-bookmark"},[this._createBookmarkUIElement(o)]);o.setCustomProperty("bookmark",!0,n),this._bookmarkElements.set(e,i);return(0,t.toWidget)(n,o,{label:()=>`${i} ${r("bookmark widget")}`})}}),o.for("upcast").add((t=>t.on("element:a",function(e){return(t,o,r)=>{const i=o.viewItem,n=F(i,!1);if(!n||!r.consumable.test(i,n))return;const s=function(e){const t=e.config.get("bookmark.enableNonEmptyAnchorConversion");return void 0===t||t}(e);if(!s&&!i.isEmpty)return;const a=r.writer,c=i.getAttribute("id"),l=i.getAttribute("name"),k=c||l,d=a.createElement("bookmark",{bookmarkId:k});if(!r.safeInsert(d,o.modelCursor))return;r.consumable.consume(i,n),c===l&&r.consumable.consume(i,{attributes:["name"]}),r.updateConversionResult(d,o);const{modelCursor:m,modelRange:u}=r.convertChildren(i,o.modelCursor);o.modelCursor=m,o.modelRange=a.createRange(o.modelRange.start,u.end)}}(e))))}_createBookmarkUIElement(e){return e.createUIElement("span",{class:"ck-bookmark__icon"},(function(e){const t=this.toDomElement(e),o=new i.IconView;return o.set({content:n.IconBookmarkInline,isColorInherited:!1}),o.render(),t.appendChild(o.element),t}))}_trackBookmarkElements(){this._bookmarkElements.forEach(((e,t)=>{"$graveyard"===t.root.rootName&&this._bookmarkElements.delete(t)}))}}function F(e,t=!0){if(!("a"===e.name))return null;if(t&&!e.isEmpty)return null;const o=e.hasAttribute("id"),r=e.hasAttribute("name"),i=e.hasAttribute("href");return o&&!i?{name:!0,attributes:["id"]}:r&&!i?{name:!0,attributes:["name"]}:null}var P=o(156),M={attributes:{"data-cke":!0}};M.setAttributes=h(),M.insert=m().bind(null,"head"),M.domAPI=k(),M.insertStyleElement=f();c()(P.A,M);P.A&&P.A.locals&&P.A.locals;const N="bookmark-ui";class O extends e.Plugin{formView=null;_balloon;static get requires(){return[C,i.ContextualBalloon,t.WidgetToolbarRepository]}static get pluginName(){return"BookmarkUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;this._balloon=e.plugins.get(i.ContextualBalloon),e.plugins.has("LinkUI")&&this._registerLinkProvider(),this._registerComponents(),e.conversion.for("editingDowncast").markerToHighlight({model:N,view:{classes:["ck-fake-bookmark-selection"]}}),e.conversion.for("editingDowncast").markerToElement({model:N,view:(e,{writer:t})=>{if(!e.markerRange.isCollapsed)return null;const o=t.createUIElement("span");return t.addClass(["ck-fake-bookmark-selection","ck-fake-bookmark-selection_collapsed"],o),o}})}afterInit(){const e=this.editor,o=e.locale.t,r=this.editor.plugins.get(t.WidgetToolbarRepository),n=i.BalloonPanelView.defaultPositions;r.register("bookmark",{ariaLabel:o("Bookmark toolbar"),items:e.config.get("bookmark.toolbar"),getRelatedElement:R,balloonClassName:"ck-bookmark-balloon ck-toolbar-container",positions:[n.southArrowNorth,n.southArrowNorthMiddleWest,n.southArrowNorthMiddleEast,n.southArrowNorthWest,n.southArrowNorthEast,n.northArrowSouth,n.northArrowSouthMiddleWest,n.northArrowSouthMiddleEast,n.northArrowSouthWest,n.northArrowSouthEast,n.viewportStickyNorth]})}destroy(){super.destroy(),this.formView&&this.formView.destroy()}_createViews(){this.formView=this._createFormView(),this._enableUserBalloonInteractions()}_createFormView(){const e=this.editor,t=e.locale,o=t.t,r=e.commands.get("insertBookmark"),n=e.commands.get("updateBookmark"),s=[r,n],a=new((0,i.CssTransitionDisablerMixin)(V))(t,function(e){const{t}=e,o=e.plugins.get(C);return[e=>{if(!e.id)return t("Bookmark must not be empty.")},e=>{if(e.id&&/\s/.test(e.id))return t("Bookmark name cannot contain space characters.")},r=>{const i=e.model.document.selection.getSelectedElement(),n=o.getElementForBookmarkId(r.id);if(i!==n)return n?t("Bookmark name already exists."):void 0}]}(e));return a.idInputView.fieldView.bind("value").to(n,"value"),a.saveButtonView.bind("label").to(n,"value",(e=>o(e?"Save":"Insert"))),a.idInputView.bind("isEnabled").toMany(s,"isEnabled",((...e)=>e.some((e=>e)))),a.saveButtonView.bind("isEnabled").toMany(s,"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(a,"cancel",(()=>{this._hideFormView()})),this.listenTo(a,"submit",(()=>{if(a.isValid()){const t=a.id;this._getSelectedBookmarkElement()?e.execute("updateBookmark",{bookmarkId:t}):e.execute("insertBookmark",{bookmarkId:t}),this._hideFormView()}})),this.listenTo(a.idInputView,"change:errorText",(()=>{e.ui.update()})),a}_registerLinkProvider(){const e=this.editor.locale.t,t=this.editor.plugins.get("LinkUI"),o=this.editor.plugins.get(C);t.registerLinksListProvider({label:e("Bookmarks"),emptyListPlaceholder:e("No bookmarks available."),navigate:({href:e})=>this._scrollToBookmark(e),getListItems:()=>Array.from(o.getAllBookmarkNames()).sort(((e,t)=>e.localeCompare(t))).map((e=>({id:e,href:`#${e}`,label:e,icon:n.IconBookmarkMedium}))),getItem:t=>{const r=[...o.getAllBookmarkNames()].find((e=>`#${e}`===t));return r?{href:t,label:r,icon:n.IconBookmarkSmall,tooltip:e("Scroll to bookmark")}:null}})}_scrollToBookmark(e){const t=this.editor.plugins.get(C).getElementForBookmarkId(e.slice(1));return!!t&&(this.editor.model.change((e=>{e.setSelection(t,"on")})),this.editor.editing.view.scrollToTheSelection({alignToTop:!0,forceScroll:!0}),!0)}_registerComponents(){const e=this.editor;e.ui.componentFactory.add("bookmark",(()=>{const e=this._createBookmarkButton(i.ButtonView);return e.set({tooltip:!0}),e})),e.ui.componentFactory.add("menuBar:bookmark",(()=>this._createBookmarkButton(i.MenuBarMenuListItemButtonView))),e.ui.componentFactory.add("bookmarkPreview",(t=>{const o=e.commands.get("updateBookmark"),r=new i.LabelView(t);return r.extendTemplate({attributes:{class:["ck-bookmark-toolbar__preview"]}}),r.bind("text").to(o,"value"),r})),e.ui.componentFactory.add("editBookmark",(t=>{const o=e.commands.get("updateBookmark"),r=new i.ButtonView(t),s=t.t;return r.set({label:s("Edit bookmark"),icon:n.IconPencil,tooltip:!0}),r.bind("isEnabled").to(o),this.listenTo(r,"execute",(()=>{this._showFormView()})),r})),e.ui.componentFactory.add("removeBookmark",(t=>{const o=e.commands.get("delete"),r=new i.ButtonView(t),s=t.t;return r.set({label:s("Remove bookmark"),icon:n.IconRemove,tooltip:!0}),r.bind("isEnabled").to(o),this.listenTo(r,"execute",(()=>{e.execute("delete"),e.editing.view.focus()})),r}))}_createBookmarkButton(e){const t=this.editor,o=t.locale,r=new e(o),i=t.commands.get("insertBookmark"),s=t.commands.get("updateBookmark"),a=o.t;return r.set({label:a("Bookmark"),icon:n.IconBookmark}),this.listenTo(r,"execute",(()=>{t.editing.view.scrollToTheSelection(),this._showFormView()})),r.bind("isEnabled").toMany([i,s],"isEnabled",((...e)=>e.some((e=>e)))),r.bind("isOn").to(s,"value",(e=>!!e)),r}_enableUserBalloonInteractions(){this.editor.keystrokes.set("Esc",((e,t)=>{this._isFormVisible&&(this._hideFormView(),t())})),(0,i.clickOutsideHandler)({emitter:this.formView,activator:()=>this._isFormInPanel,contextElements:()=>[this._balloon.view.element],callback:()=>{this._hideFormView(!1)}})}_addFormView(){if(this.formView||this._createViews(),this._isFormInPanel)return;const e=this.editor.commands.get("updateBookmark");this.formView.disableCssTransitions(),this.formView.resetFormStatus(),this._balloon.add({view:this.formView,position:this._getBalloonPositionData()}),this.formView.backButtonView.isVisible=e.isEnabled,this.formView.idInputView.fieldView.value=e.value||"",this._balloon.visibleView===this.formView&&this.formView.idInputView.fieldView.select(),this.formView.enableCssTransitions()}_removeFormView(e=!0){this.formView.saveButtonView.focus(),this.formView.idInputView.fieldView.reset(),this._balloon.remove(this.formView),e&&this.editor.editing.view.focus(),this._hideFakeVisualSelection()}_showFormView(){this.formView||this._createViews(),this._getSelectedBookmarkElement()||this._showFakeVisualSelection(),this._addFormView(),this._balloon.showStack("main"),this._startUpdatingUI()}_hideFormView(e=!0){if(!this._isFormInPanel)return;const t=this.editor;this.stopListening(t.ui,"update"),this.stopListening(this._balloon,"change:visibleView"),e&&t.editing.view.focus(),this._removeFormView(e),this._hideFakeVisualSelection()}_startUpdatingUI(){const e=this.editor,t=e.editing.view.document;let o=this._getSelectedBookmarkElement(),r=n();const i=()=>{const e=this._getSelectedBookmarkElement(),t=n();o&&!e||!o&&t!==r?this._hideFormView():this._isFormVisible&&this._balloon.updatePosition(this._getBalloonPositionData()),o=e,r=t};function n(){return t.selection.focus.getAncestors().reverse().find((e=>e.is("element")))}this.listenTo(e.ui,"update",i),this.listenTo(this._balloon,"change:visibleView",i)}get _isFormInPanel(){return!!this.formView&&this._balloon.hasView(this.formView)}get _isFormVisible(){return!!this.formView&&this._balloon.visibleView==this.formView}_getBalloonPositionData(){const e=this.editor.editing.view,t=this.editor.model;let o;const r=this._getSelectedBookmarkElement();if(t.markers.has(N)){const t=Array.from(this.editor.editing.mapper.markerNameToElements(N)),r=e.createRange(e.createPositionBefore(t[0]),e.createPositionAfter(t[t.length-1]));o=e.domConverter.viewRangeToDom(r)}else r&&(o=()=>{const t=this.editor.editing.mapper,o=e.domConverter,i=t.toViewElement(r);return o.mapViewToDom(i)});if(o)return{target:o}}_getSelectedBookmarkElement(){const e=this.editor.model.document.selection.getSelectedElement();return e&&e.is("element","bookmark")?e:null}_showFakeVisualSelection(){const e=this.editor.model;e.change((t=>{const o=e.document.selection.getFirstRange();if(e.markers.has(N))t.updateMarker(N,{range:o});else if(o.start.isAtEnd){const r=o.start.getLastMatchingPosition((({item:t})=>!e.schema.isContent(t)),{boundaries:o});t.addMarker(N,{usingOperation:!1,affectsData:!1,range:t.createRange(r,o.end)})}else t.addMarker(N,{usingOperation:!1,affectsData:!1,range:o})}))}_hideFakeVisualSelection(){const e=this.editor.model;e.markers.has(N)&&e.change((e=>{e.removeMarker(N)}))}}function R(e){const o=e.getSelectedElement();return o&&(0,t.isWidget)(o)&&o.getCustomProperty("bookmark")?o:null}class L extends e.Plugin{static get pluginName(){return"Bookmark"}static get requires(){return[C,O,t.Widget]}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).bookmark=r})();