!function(e){const i=e.tt=e.tt||{};i.dictionary=Object.assign(i.dictionary||{},{Activities:"","Animals & Nature":"","Dark skin tone":"","Default skin tone":"",Emoji:"","Emoji picker":"","Find an emoji (min. 2 characters)":"",Flags:"","Food & Drinks":"","Gestures & People":"","Keep on typing to see the emoji.":"","Light skin tone":"","Medium Dark skin tone":"","Medium Light skin tone":"","Medium skin tone":"",'No emojis were found matching "%0".':"",Objects:"","Select skin tone":"","Show all emoji...":"","Smileys & Expressions":"",Symbols:"","The query must contain at least two characters.":"","Travel & Places":""})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));